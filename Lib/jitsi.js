const fs = require('fs-extra');
const path = require('path');
const jsonwebtoken = require('jsonwebtoken');
const dayjs = require('dayjs');


const appId = process.env.JITSI_KEY;
const kid = `${appId}/${process.env.JITSI_KID_KEY}`;

const tokenForJitsi = fs.readFileSync(path.join(__dirname,'../jitsi.pk')).toString();
const constants = require('../Lib/constants');

/**
 * Function generates a JaaS JWT.
 */
const generate = (privateKey, { id, name, email, avatar, appId, kid, isMod }) => {
    const now = dayjs();
    const jwt = jsonwebtoken.sign({
        aud: 'jitsi',
        context: {
            user: {
                id,
                name,
                avatar,
                email: email,
                moderator: isMod?'true':'false',
            },
            features: {
                livestreaming: 'false',
                recording: 'false',
                transcription: 'false',
                "outbound-call": 'false'
            },
            room: {
                regex: false,
            }
        },
        iss: 'chat',
        room: '*',
        sub: appId,
        exp: Math.floor(now.add(constants.liveStreamRedisCacheTime_sec, 'second').toDate().getTime() / 1000),
    }, privateKey, { algorithm: 'RS256', header: { kid } })
    return jwt;
}

const genrateTokenForUser =  ({
    name, email, isMod, userId, profilePicture,
}) => {
    console.log(name, email ,isMod, userId, profilePicture);
    return generate(tokenForJitsi, {
        id: userId,
        name: name,
        email: email,
        avatar: profilePicture,
        appId: appId,
        kid: kid,
        isMod,
    })
}

/**
 * 
 * @param {{name: string, description: string, template_id?: string, region?:string}} payload 
 */
exports.createRoom = (payload) => {
    return  `/${appId}/${payload.name}`;
}

/**
 * 
 * @param {{name: string, email: string, isMod?:boolean, userId: string, profilePic?:string}} config 
 * @returns 
 */
exports.createAuthToken = (config) => {
    return genrateTokenForUser({
        name: config.name,
        email: config.email,
        isMod: (config.isMod == undefined)?false:config.isMod,
        userId: config.userId,
        profilePicture: config.profilePic
    });
}