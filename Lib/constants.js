const dayjs = require('dayjs');
const path = require('path');
const fs = require('fs');

const cloudTestCaseInputString = "<CLOUD_TESTCASE>";
const sessionCookieName = 'hackoo';
const liveStreamTokenUpto_Sec = 60 * 20;
const liveStreamRedisCacheTime_sec = 60 * 60 *  5;
const sessionExpireTimeSeconds = 2 * 60 * 60;
const otpExireTimeSeconds = 15 * 60;
const otpIntervalsSeconds = 60;
const socketTimeoutInSeconds = 10;
const latestCheatingEventExpireTimeInSec = 4 * 60;
const freezeChangeLogPrefix = 'Freeze State Change: ';
const shareTestExpireTimeInDays = 15;

const suspiciousRoomNameSuffix = '-suspicious-room';

var constantString = {
    constantStringTagTypeQuestion: 'question',
    constantStringTagTypeTutorial: 'tutorial',
    constantStringTagTypeCourse: 'course',
    constantStringTagTypeQuiz: 'quiz',
    constantStringError: 'error'
};
var questionTypeNumeric = {
    questionTypeMCQ: '1',
    questionTypeSubjective: '2',
    questionTypeCoding: '4',
    questionTypeStepwise: '5',
    questionTypeWeb: '9',
};
const questionTypeOptions = {
    'coding' : { value : '4', label : "Coding"},
    'MCQ' : { value : '1', label : "MCQ"},
    'multipleAnswer' : { value : '5', label : "Multiple Questions"},
    'web' : { value : '9', label : "Web"},
    'subjective' : { value : '2', label : "Subjective"},
}
var contentTypeString = {
    contentTypeMCQ: '1',
    contentTypeSubjective: '2',
    contentTypeCoding: '3',
    contentTypeStepwise: '4',
    contentTypeTutorial : '5',
    contentTypeQuiz : '6',
    contentTypeProject : '7',
    contentTypeWeb : '9',
    question : 'question',
    course : 'course',
    quiz : 'quiz',
    tutorial : 'tutorial',
};

var questionTypeProperties = {
    '1': {type: contentTypeString.contentTypeMCQ},
    '2': {type: contentTypeString.contentTypeSubjective},
    '4': {type: contentTypeString.contentTypeCoding},
    '5': {type: contentTypeString.contentTypeStepwise}
};

var codingQuestionExecutionType = {
'onlyCompile' : '1',
'resultOnly' : '2',
'resultWithTestCases' : '3'
};

const quizContentCodingQuestionDefaults = {
    showCustomInput: true,
    showHead: true,
    showTail: true
};

var langCodeToLangString = {
    "7": "C",
    "17": "C99",
    "77": "C++",
    "18": "C++11",
    "19": "C++14",
    "4": "JavaScript",
	"8": "Java",
    "11": "Bash Script",
    "0": "Python 2",
    "20": "Python 3",
    "1": "Ruby",
    "10": "C#",
    "3" : "Php",
    "2": "React-Jest",
    "5": "SQL",
    "22": "HTML, CSS, JS",
	"23": "MySql",
	"24": "Oracle",
}

var langStringToLangCode = {
    "C": "7",
    "C99": "17",
    "C++": "77",
    "C++11": "18",
    "C++14": "19",
    "JavaScript": "4",
	"Java": "8",
    "Bash Script": "11",
    "Python 2": "0",
    "Python 3": "20",
    "Ruby" : "1",
    "C#" : "10",
    "Php" : "3",
    "React-Jest" : "2",
    "SQL" : "5",
    "HTML, CSS, JS": "22",
	"MySql": "23",
	"Oracle": "24",
};

var bucketSize = 30;
var maximumTestCases = 20;
var segmentationFaultString = "Segmentation fault";
var coreDumpedString = "core dumped";
var contentActiveStatus = 1;
var roleStringFromNumber = {
    "0" : "admin",
    "1" : "user",
    "2" : "Recruiter",
    "3" : "subAdmin",
    "4" : "contentCreator",
    "5" : "Support",
    "6" : "Recruiter",
    "7" : "Custom",
    "8" : "Super Org",
}

var roleNumberFromString = {
    "admin" : "0",
    "user" : "1",
    "mentor" : "2",
    'subAdmin' : '3',
    "contentCreator" : "4",
    "support": "5",
    "recruiter" : "6",
    "custom": "7",
    "superOrg": "8",
};


var compilePriority = {
    compilePriorityAdmin : 1,
    compilePriorityMentor : 2,
    compilePriorityUser : 3,
}
var bulkUploadFormat ={
    displayname : 0 , email : 1 ,  mobile : 2 , college : 3 , city : 4 , state : 5, role : 6,password : 7
}

var sessionSecret = '#$%!@12345678!@#$';

var redisKeys = {
    'compileSocketQueueAdmin' : 'compileSocketQueueAdmin',
    'compileSocketQueueMentor' : 'compileSocketQueueMentor',
    'compileSocketQueueUser' : 'compileSocketQueueUser',
    'isCompilationResponsePending' : 'isCompilationResponsePending',
    'isMCQResultPending' : 'isMCQResultPending',
    'emptyString' : "",
    'compilationInProgress' : ".",
    'compileSocketQueueMCQ' : 'compileSocketQueueMCQ',
    'courseIdSegmentCountString' : 'courseIdSegmentCount',
    'courseIdString' : 'courseId',
    'quizIdString' : 'quizId',
    'segmentCountString' : 'segmentCount',
    'questionIdString' : 'questionId',
    'compileCounterFieldString' : 'compileCounter',
    'totalCompileCounterFieldString' : 'totalCompileCounter',
    'codeproglangFieldString' : 'codeproglang',
    'readData' : 'readData',
    'docker' : 'docker',
    'apiPath' : 'apiPath',
    'folderPath' : 'folderPath',
    'tempString' : 'temp',
    'ongoingQuiz' : 'ongoingQuiz',
    'questionString' : 'question',
    'links' : 'links',
    'questionsStroage' : 'questionsStorage',
    'length' : 'length',
    'liveReportRoom' : 'liveReport',
    'quizName' : 'quizName',
    'compilationCounter' : 'compilationCounter',
    'quizNameFromId' : 'quizNameFromId',
    'sessionId' : 'sid',
    'liveStreamRoom': 'liveStreamRoom',
    'submittedQuestions' : 'submittedQuestions',
    'submittedQuesScores' : 'submittedQuesScores',
    'submittedSegments' : 'quizSubmittedSegments',
    'userSessions' : 'userSessions',
    'sessionTime' : 'sessionTime',
    'quizUserIp' : 'quizUserIp',
    'activeQuizSet' : 'activeQuizSet',
    'joinedUserIds' : 'joinedUserIds',
    'submittedUserIds' : 'submittedUserIds',
    'quizLastActivityTime' : 'quizLastActivityTime',
    'quizTimeString' : 'quizTime',
    'isArchiving' : 'isArchiving',
    'errors' : 'errors',
    'all' : "All",
    'extraTime' : "extraTime",
    'idleTime' : "idleTime",
    "currentlyProcessingQuizId" : "currentlyProcessingQuizId",
    "quizIdsForExplicitSubmissions" : "quizIdsForExplicitSubmissions",
    'compileServersSet' : "compileServersSet",
    'inActivecompileServersSet' : 'inActivecompileServersSet',
    'compileServer' : 'compileServer',
    'compileServerQueue' : 'compileServerQueue',
    'compileServersInfoHash' : 'compileServersInfoHash',
    'compileServerCodeStringsSet' : 'compileServerCodeStringsSet',
    'langAvgExecutionTimeHash' : 'langAvgExecutionTimeHash',
    'langTotalCodeSentHash' : 'langTotalCodeSentHash',
    'langCode' : 'langCode',
    'totalWaitingTime' : 'totalWaitingTime',
    'totalSent' : 'totalSent',
    'totalCores' : 'totalCores',
    'totalDockerInstances' : 'totalDockerInstances',
    'pendingNewServerTimeStampList' : 'pendingNewServerTimeStampList',
    'cpuCount' : 'cpuCount',
    'sent' : 'sent',
    'dockerInstances' : 'dockerInstances',
    'timeStampForCompileServerDeletion' : 'timeStampForCompileServerDeletion',
    'compileServerSnapshotId' : 'compileServerSnapshotId',
    'lowPriorityQueue' : 'lowPriorityQueue',
    'serverStatus' : 'serverStatus',
    'errorCount' : 'errorCount',
    'isProjectServer': 'isProjectServer',
    "checkExecute" : "checkExecute",
    "maxScore" : "maxScore",
    "filesMd5Hash" : "filesMd5Hash",
    "ongoingQuizUsers" : "ongoingQuizUsers",
    "quizUserDetails" : "quizUserDetails",
    "userId" : "userId",
    "name" : "name",
    "email" : "email",
    "rollNo" : "rollNo",
    "codePasteCount" : "codePasteCount",
    "tabSwitchCount" : "tabSwitchCount",
    "fullScreenInCount" : "fullScreenInCount",
    "fullScreenOutCount" : "fullScreenOutCount",
    
    "tabSwitchInCount" : "tabSwitchInCount",
    "forceLoginCount" : "forceLoginCount",
    "loginCount" : "loginCount",
    "currentIp" : "currentIp",
    "loginTimeStampsList": "loginTimeStampsList",
    "camBlockCount": "camBlockCount",
    "superAdminId" : "superAdminId",
    "applyTest" : "applyTest",
    "applyUserIds" : "applyUserIds",
    "project" : "project",
    "dockerName" : "dockerName",
    "terminalPort" : "terminalPort",
    "projectPort" : "projectPort",
    "compileServerIp" : "compileServerIp",
    "codePayload" : "codePayload",
    "reEvaluatingQuizzesIdSet" : "reEvaluatingQuizzesIdSet",
    "reEvaluateQuizTotalSent" : "reEvaluateQuizTotalSent",
    "reEvaluateQuizTotalReceive" : "reEvaluateQuizTotalReceive",
    "reEvaluateQuizWrongAttempts": "reEvaluateQuizWrongAttempts",
    "submissionQueue" : "submissionQueue",
    "pendingSubmissionsHash" : "pendingSubmissionsHash",
	"userSessionIdsHash" : "userSessionIdsHash",
    "quizAllowedEmailsSet" : "quizAllowedEmailsSet",
    "userQuesAttemptsList" : "userQuesAttemptsList",
    "editGlobalVariables" : "EDIT_GLOBAL_VARIABLES",
    "ramUpdateChannel" : "RAM_UPDATE_CHANNEL",
    "quizAllowedEmailIdsChannel" : "QUIZ_ALLOWED_EMAILIDS_CHANNEL",
    "endTime" : "endTime",
    "startTime" : "startTime",
    "explicitSubmission" : "explicitSubmission",
    "isAutoSubmit" : "isAutoSubmit",
    "recentQuizSubmissionsList" : "recentQuizSubmissionsList",
    "mailQueue" : "mailQueue",
	"tryTest" : "tryTest",
    "extraTimeExpiry" : "extraTimeExpiry",
    "lastInviteLeftMailSentTimestamp": "lastInviteLeftMailSentTimestamp",
    "tabSwitchReport": "tabSwitchReport",
    "fullScreenReport": "fullScreenReport",
    "userRoleHash": "userRoleHash",
    "userRoleUpdated": "userRoleUpdated",
    "scoreUpdationActivityList": "scoreUpdationActivityList",
    "quizAttemptList": "quizAttemptList",
    "quizAttemptUpdateList": "quizAttemptUdateList",
    "emailCountHash": "emailCountHash",
    "emailSaveQueue": "emailSaveQueue",
    'activeUserCountCache': 'activeUserCountCache',
    'fixingQuizContent': 'fixingQuizContent',
    'emailLimitQueue': 'emailLimitQueue',
    'autoSubmitAfterExpire':'autoSubmitAfterExpire',
    "timoutForSubmission": "timeoutForSubmission",
    "failedProccessForTimeoutSubmisison": "failedProcess",
	'inviteUsed':'inviteUsed',
    "liveStreamJWT":"liveStreamJWT",
    'liveStreamRoom': 'liveStreamRoom',
    'otpForTestSubmission': 'otpForTestSubmission',
    'otpBlockedForUser': 'blockOtpSending',
    'aiProctor': 'aiProctor',
    'segmentSubmissionHistory': 'segmentSubmissionHistory',
    'suspiciousUserRemovalQueue': 'suspiciousUserRemovalQueue',
    'jitsiMeetId': 'jitsiMeetId',
    'callStatsUserName': 'callStatsUserName',
    'jitsiBucketId': 'jitsiBucketId',
    'fileUploadErrorList': 'fileUploadErrorList',
    'fileUploadList': 'fileUploadList',
    'userLatestCheatingEvent': 'userLatestCheatingEvent',
    'recordingEventList': 'recordingEventList',
    'terminationEventList': 'terminationEventList',
    'appTemperingList': 'appTemperingList',
    getTerminationList: function (quizId, userId) {
        return `${this.terminationEventList}:${quizId}:${userId}`
    },
    getUserLatestCheatingEventKey: function (quizId, userId) {
        return `${this.userLatestCheatingEvent}:${quizId}:${userId}`;
    },
    getFileUploadErrorList: function (quizId, userId) {
        return `${this.fileUploadErrorList}:${quizId}:${userId}`
    },
    getRecordingEventList: function (quizId, userId) {
        return `${this.recordingEventList}:${quizId}:${userId}`;
    },
    getFileUploadedList: function (quizId, userId) {
        return `${this.fileUploadList}:${quizId}:${userId}`;
    },
    getSegmentSubmissionHistory: function (quizId, userId) {
        return `${this.segmentSubmissionHistory}:${quizId}:${userId}`; 
    },
    getOnGoingQuiz: function(userId, quizId) {
        return `${this.ongoingQuiz}:${userId}:${quizId}`;
    },
    getLiveStreamRoomKey: function (quizId, userId) {
        return `${this.liveStreamRoom}:${quizId}:${userId}`;
    },
    getLiveStreamKey: function (userId, quizId) {
        return `${this.liveStreamJWT}:${userId}:${quizId}`
    },
    getOnGoingQuiz: function(userId, quizId) {
        return `${this.ongoingQuiz}:${userId}:${quizId}`
    },
    getOtpBlockedForUser:  function (userId, quizId) {
        return `${this.otpBlockedForUser}:${userId}:${quizId}`;
    },
    getOtpForTestSubmission: function (userId, quizId) {
        return `${this.otpForTestSubmission}:${userId}:${quizId}`;
    },
    getOnGoingQuiz: function (userId, quizId) {
        return `${this.ongoingQuiz}:${userId}:${quizId}`;
    },
    getInviteUserKeyForCurrentDate: function (orgId) {
        const date = dayjs();
        return `${this.inviteUsed}:${date.format('YYYY-MM-DD')}:${orgId}`;
    },
    getActiveUserByOrgIdCache: function (orgId) {
        if (!orgId) throw new Error('orgid is not provided');
        return `${this.activeUserCountCache}:${orgId.toString()}`;
    },
    getAutoSubmitAfterExpireKey: function (quizId, userId) {
        return `${this.autoSubmitAfterExpire}:${quizId}:${userId}`
    },
    getEmailCount:  function (orgId) {
        const date = dayjs();
        const dateString = date.format('MM-DD');
        return `${this.emailCountHash}:${dateString}:${orgId}`;
    },
    getLoginTimeStampsList: function(quizId, userId) { return quizId && userId && `${this.loginTimeStampsList}:${quizId}:${userId}`; },
    getLastInviteLeftMailSentTimestamp: function(userId) { return userId && `${this.lastInviteLeftMailSentTimestamp}:${userId}`; },
    getTabSwitchReportList: function(quizId, userId) { 
        return quizId && userId && `${this.tabSwitchReport}:${quizId}:${userId}`; 
    },
    getfullScreenReportList: function(quizId, userId) { 
        return quizId && userId && `${this.fullScreenReport}:${quizId}:${userId}`; 
    },
    getScoreUpdationActivityList: function(quizId, userId, quesId) {
        return quizId && userId && quesId && `${this.scoreUpdationActivityList}:${quizId}:${userId}:${quesId}`;
    },
    getAiProctorReportList: function (quiId, userId) {
        return `${this.aiProctor}:${quiId}:${userId}`;
    }
};

var metaDataParams = {
    'activeUsers': 'activeUsers',
    'activeEmails': 'activeEmails',
}


var redisQuestionKeys = {  
    _id : { key : '_id'},
    text : { key : 'text'},
    tags : { key : 'tags'}, 
    type : { key : 'type'},
    files : { key : 'files'} ,
    title : { key : 'title'} ,
    score : { key : 'score'},
    explanation : { key : 'explanation'} ,
    displaystatus :{ key : 'displaystatus'},
    hint : { key : 'hint' },
    languagesAllowed :{key :  'languagesAllowed'} ,
    questionTypeSubjective : { key : 'questionTypeSubjective'},
    showhint : { key : 'showhint' },
    questionTypeMCQ : { key : 'questionTypeMCQ'},
    questionTypeCoding: { key : 'questionTypeCoding'},
    createdBy :{key : 'createdBy'},
    referenceLinks:  { key : 'referenceLinks'},
    questionTypeWeb:  { key : 'questionTypeWeb'},
}

var redisQuestionKeysRequiredParsing = {
    [ redisQuestionKeys.files.key ] : true,
    [ redisQuestionKeys.questionTypeSubjective.key ] : true,
    [ redisQuestionKeys.questionTypeMCQ.key ] : true,
    [ redisQuestionKeys.questionTypeCoding.key ] : true,
    [ redisQuestionKeys.languagesAllowed.key ] : true,
    [ redisQuestionKeys.questionTypeWeb.key ] : true,
};
// dont change these sequence of strings because used in quizControllers' getQuizFromDbFunction

var quizMGETArray = [ 
    'startTime',
    'idleTime',
    redisKeys.submittedQuestions,
    '_id',
    redisKeys.submittedSegments,
    'extraTime',
    'tabSwitchCount',
    'forceLoginCount',
    'codePasteCount',
    'displayName',
    'email',
    'allowedIP',
    'loginCount',
    'camBlockCount',
    'questionId',
    'hasQuizStarted',
    'endTime',
    'userId',
    'dashboardUpdateTime',
    'userSessions',
    'sessionTime',
    'extraTimeExpiry',
    'currentIp',
    'fullScreenReport',
    'fullScreenInCount',
    'fullScreenOutCount',
    'tabSwitchInCount',
    'userEnterCount',
    'userExitCount',
    'multipleUserDetected',
    'illegalObjectDetected',
]

var quizMGETArrayPropertyIndex = {
    'startTime' : 0,
    '_id' : 3,
    'extraTime' : 5,
    'tabSwitchCount' : 6,
    'forceLoginCount' : 7,
    'codePasteCount' : 8,
    'displayName' : 9,
    'email' : 10,
    'allowedIP' : 11,
    'loginCount' : 12,
    'camBlockCount' : 13,
    'questionId' : 14,
    'hasQuizStarted': 15,
    'endTime': 16,
    'userId' : 17,
    'dashboardUpdateTime' : 18,
    'userSessions' : 19,
    'sessionTime' : 20,
    'extraTimeExpiry' : 21,
    'currentIp' : 22,
    'fullScreenReport' : 23,
    'fullScreenInCount' : 24,
    'fullScreenOutCount' : 25,
    'tabSwitchInCount' : 26,
    'userEnterCount': 27,
    'userExitCount': 28,
    'multipleUserDetected': 29,
    'illegalObjectDetected': 30,

};

quizMGETArrayPropertyIndex[ redisKeys.submittedQuestions ] = 2;
quizMGETArrayPropertyIndex[ redisKeys.submittedSegments ] = 4;

var modelForPagination = {
    'Tutorial' : 'Tutorial' ,
    'Questions' : 'Questions' ,
    'Courses' : 'Courses' ,
    'Quiz' : 'Quiz',
    'UserAnswer' : 'UserAnswer' ,
    'User' : 'User' ,
    'UserFeedBack' : 'UserFeedBack'
};

var modelString = {
    'BatchModelString' : "Batch",
    'CourseModelString' : "Course",
    'QuestionModelString' : "Question",
    'QuizModelString' : "Quiz",
    'TagsModelString' : "Tags",
    'TutorialModelString' : "Tutorial",
    'UserModelString' : "User",
    'UserAnswerModelString' : "UserAnswer",
    'MailJetEventsModelString' : "MailJetEvents",
    'UserAttemptModelString' : "UserAttempt",
    'UserAttemptSnapshotModelString' : "UserAttemptSnapshot",
    'UserQuizSubmittedSegment' : "UserQuizSubmittedSegment",
    "UserCourseSubmittedSegmentModelString" : "UserCourseSubmittedSegment",
    "UserIdeSavedCodeModelString" : "UserIdeSavedCode",
    "feedbackModelString" : "Feedback",
    "ProjectModelString" : "Project",
    "AdminProjectModelString" : "AdminProject",
    "quizNamesModelString" : "QuizName",
    "badActivityModelString" : "BadActivity",
    'errorModelString' : "Error",
    'userSpaceModelString' : "UserSpace",
    "SuggestedTestCaseModelString" : "SuggestedTestCase",
    'archivedBatchModelString' : "Batch_Archive",
    'archivedCourseModelString' : "Course_Archive",
    'archivedQuestionModelString' : "Question_Archive",
    'archivedQuizModelString' : "Quiz_Archive",
    'archivedTagsModelString' : "Tags_Archive",
    'archivedTutorialModelString' : "Tutorial_Archive",
    'archivedUserModelString' : "User_Archive",
    'archivedUserAnswerModelString' : "UserAnswer_Archive",
    'archivedUserAttemptModelString' : "UserAttempt_Archive",
    'archivedUserAttemptSnapshotModelString' : "UserAttemptSnapshot_Archive",
    'archivedUserQuizSubmittedSegment' : "UserQuizSubmittedSegment_Archive",
    "archivedUserCourseSubmittedSegmentModelString" : "UserCourseSubmittedSegment_Archive",
    "archivedUserIdeSavedCodeModelString" : "UserIdeSavedCode_Archive",
    "archivedFeedbackModelString" : "Feedback_Archive",
    "archivedProjectModelString" : "Project_Archive",
    "archivedAdminProjectModelString" : "AdminProject_Archive",
    "archivedQuizNamesModelString" : "QuizName_Archive",
    "archivedBadActivityModelString" : "BadActivity_Archive",
    'archivedErrorModelString' : "Error_Archive",
    'archivedUserSpaceModelString' : "UserSpace_Archive",
    "archivedSuggestedTestCaseModelString" : "SuggestedTestCase_Archive",
    "quizFeedbackModelString" : "quizFeedback",
    "questionPoolString" : "questionPool",
    "QuizQuestionMetaData" : "QuizQuestionMetaData",
    "QuizAllowedEmails" : "QuizAllowedEmails",
    'QuizAttempt': 'QuizAttempt',
    'StreamRooms': 'StreamRooms',
    'EmailBasedQuizClone': 'EmailBasedQuizClone',
    'UserQuizSubmittedSegmentMetaData': 'UserQuizSubmittedSegmentMetaData',
    'ActivityLogs': 'ActivityLogs',
    'JitsiBucket': 'JitsiBucket',
};

var defaultProfileLink = `http://${process.env.host}${argv[0] ? ':' + argv[0]: ''}`;
var defaultInfoMail = "<EMAIL>"
var defaultDisplayName = "Admin";
var questionFileUploadPath = './Public/uploads/Questions/';
var tutorialFileUploadPath = './Public/uploads/Tutorials/';
const reportFileUploadPath = './Public/uploads/Reports/';
var quizFileUploadPath = './Public/uploads/Quizzes/';
var courseFileUploadPath = './Public/uploads/Courses/';
const uploadsFolderString = "uploads";
const tutorialFolderString = "Tutorials";
const questionFolderString = "Questions";
const courseFolderString = "Courses";
const quizFolderString = "Quizzes";
var endOfInputString = "\nEND_OF_INPUT\n";
var endOfIterationString = "END_OF_ITERATION";
var queryStatus = {
    'pending' : '0',
    'acknowledged' : '1',
    'resolved' : '2',
    'reOpen' : '4'
};

var displayStatus = {
    'deleted' : '0',
    'active' : '1',
    'disabled' : '2'
};

var maximumOutputMemoryAllowed = 10 * 1024 * 1024; // 10 MB
var maximumMemoryAllowedForQuestionsInRedis = 12 * 1024 * 1024;
var killedString = "Killed";
var thresholdForQueues = 7;
var badActivitiesTypeNumber = {
    'cheating' : 1,
    'permission' : 2,
    'info' : 3,
};

var badActivitiesTypeString = {
    '1' : 'cheating',
    '2' : 'permission',
    '3' : 'info',
};
var badActivityCheatingType = {
    '1' : 'codePasteCount' ,
    '2' : 'tabSwitchCount',
    '3' : 'comeBackToTestWindow',
    '4' : 'logOutFromTest',
    '5' : 'forceLoginCount',
    '6' : 'camBlockCount',
    '9' : 'fullScreenCount',
    '10': 'userExit',
    '11': 'userEnter',
    '12': 'illegalObjectDetected'
};


var userQuizSubmittedSegmentDataType = {
    'questionId' : 'ObjectId',
    'score' :  'number',
    'startTime' : 'date',
    'hasSubmitted' : 'boolean',
    'userProgram' : 'string',
    'userLanguage' : 'string',
    'userInputMCQ' : 'string',
    'userInputSubjective' : 'string',
};

var userQuizSubmittedSegmentKeys = {  'quizSubmittedSegments' : 'quizSubmittedSegments', 'startTime' : 'startTime', 'quizSubmittedQuestion' : 'quizSubmittedQuestion' }
var userQuizSubmittedSegmentKeysArray = [ 'quizSubmittedSegments', 'startTime', 'quizSubmittedQuestion' ];
var num_processes = Math.max( 1, parseInt( require('os').cpus().length ) - 1 );
// var num_processes = 1;
const bytesToMegaBytes = 1000000.0;

var projectStatus = {
    'inProgress' : 1,
    'inReview' : 2,
    'complete' : 3,
}

var memorySizeConstants = {
    "K" : 1,
    "M" : 2,
    "G" : 3,
};

const questionOutcomes = {
    CO1: 'co1',
    CO2: 'co2',
    CO3: 'co3',
    CO4: 'co4',
    CO5: 'co5',
    CO6: 'co6',
    CO7: 'co7',
    CO8: 'co8',
}

var memoryString = {
    "Kilobyte" : "K",
    "MegaByte" : "M",
    "GigaByte" : "G",
};
var defaultUserQuota = "20M";
var blockSize = 512;
var fileScriptName = "listFilesAndContent.sh";
var changedFileScript = "fileChangedScript.sh";
var imageInitial = "DirectoryWithSizeLimit";
var mountFolderBasePath = "/mounts/" + imageInitial;
var folderUnmountScriptName = 'unmountScript.sh';
var unmountAllFoldersScriptName = "unmountAllLoopDevices.sh";
const dbBackUpScript = "createBackup.sh";
var directoryString = "\nDIRECTORIES\n";
var languageCodes = {
    'C' : "7",
    'C99' :  "17",
    'Cpp' : "77",
    'Cpp11' : "18",
    'Cpp14' : "19",
    'Java' : "8",
    "JavaScript" : "4",
    "Bash" : "11",
    "SQL" : "5",
    "MySql": "23",
	"Oracle": "24",
    "Python 2" : "0",
    "Python 3" : "20",
    "Ruby" : "1",
    "C#" : "10",
    "Php" : "3",
    "React-Jest" : "2",
}
/*
var langCodeToLangString = {
    "7": "C",
    "17": "C99",
    "77": "C++",
    "18": "C++11",
    "19": "C++14",
    "4": "JavaScript",
    "11": "Bash Script",
    "0": "Python 2",
    "20": "Python 3",
    "1": "Ruby",
    "10": "C#",
    "3" : "Php"
}
*/

var defaultCodes = {
    'C' : `#include <stdio.h>
/* Include other headers as needed */
int main()
{

    /* Enter your code here. Read input from STDIN. Print output to STDOUT */
    return 0;
}`,
    'Cpp' : `
#include<iostream>
#include<cstdio>
#include<cmath>
using namespace std;

int main()
{
    // Write your code here
    
    // Return 0 to indicate normal termination
    return 0;
}
    `,
    'Java' : `import java.util.Scanner;
    // Other imports go here
    // Do NOT change the class name
    class Main{
        public static void main(String[] args)
        {
            // Write your code here
        }
    }`,

    'JavaScript' : "/* Type your code there */",
    'Bash' : "# Start writing your script",
    'sql' : "",
    'Python 2' : "#Type your code here",
    'Python 3' : "#Type your code here",
    'Ruby' : "#Type your code here",
    'C#' : `
    using System.IO;
    using System;
    
    class Program
    {
        static void Main()
        {
            // Write your code here
        }
    }
    `,
    'Php' : `
    <?php
        // Type your code here
    `
}

// All supported languages
// To add new language simply add to the array and also make appropriate changes in /Views/admin/partials/CodeMirrorScripts.ejs
// Also add new language to languageCodes object and langCodeToLangString object.
const supportedLanguages = [
    { code: "7", name: "C", isQuestionLanguage: true, isProjectLanguage : true, isIdeLanguage : true, mainFile : "main.c" , defaultCode : defaultCodes.C, isCustomInput: true, extension: 'c'},
    { code: "17", name: "C99", isQuestionLanguage: true, isProjectLanguage : true , isIdeLanguage : true, mainFile : "main.c" , defaultCode : defaultCodes.C, isCustomInput: true, extension: 'c'},
    { code: "77", name: "C++", isQuestionLanguage: true, isProjectLanguage : true , isIdeLanguage : true, mainFile : "main.cpp", defaultCode : defaultCodes.Cpp, isCustomInput: true, extension: 'cpp'},
    { code: "18", name: "C++11", isQuestionLanguage: true, isProjectLanguage : true , isIdeLanguage : true, mainFile : "main.cpp", defaultCode : defaultCodes.Cpp, isCustomInput: true, extension: 'cpp'},
    { code: "19", name: "C++14", isQuestionLanguage: true, isProjectLanguage : true, isIdeLanguage : true, mainFile : "main.cpp", defaultCode : defaultCodes.Cpp, isCustomInput: true, extension: 'cpp'},
    { code: "8", name: "Java", isQuestionLanguage: true, isProjectLanguage : false, isIdeLanguage : true, mainFile : "main.java" , defaultCode : defaultCodes.Java, isCustomInput: true, extension: 'java'},
    { code: "4", name: "JavaScript", isQuestionLanguage: true, isProjectLanguage : true, isIdeLanguage : true, mainFile : "main.js", defaultCode : defaultCodes.JavaScript, isCustomInput: false, extension: 'js'},
    { code: "11", name: "Bash", isQuestionLanguage: true, isProjectLanguage : false, isIdeLanguage : true, defaultCode : defaultCodes.Bash, isCustomInput: false, extension: 'sh'},
    { code: "0", name: "Python 2", isQuestionLanguage: true, isProjectLanguage : false, isIdeLanguage : true, mainFile: "main.py", defaultCode : defaultCodes["Python 2"], isCustomInput: true, extension: 'py'},
    { code: "20", name: "Python 3", isQuestionLanguage: true, isProjectLanguage : false, isIdeLanguage : true, mainFile: "main.py3", defaultCode : defaultCodes["Python 3"], isCustomInput: true, extension: 'py3'},
    { code: "5", name: "SQL", isQuestionLanguage: true, isProjectLanguage : false, isIdeLanguage : true, defaultCode : defaultCodes.sql, isCustomInput: false, extension: 'sql'},
    { code: "1", name: "Ruby", isQuestionLanguage: true, isProjectLanguage : false, isIdeLanguage : true, mainFile : "main.rb" , defaultCode : defaultCodes.Ruby, isCustomInput: true, extension: 'rb'},
    { code: "10", name: "C#", isQuestionLanguage: true, isProjectLanguage : false, isIdeLanguage : true, mainFile : "main.cs" , defaultCode : defaultCodes['C#'], isCustomInput: true, extension: 'cs'},
    { code: "3", name: "Php", isQuestionLanguage: true, isProjectLanguage : false, isIdeLanguage : true, mainFile : "main.php" , defaultCode : defaultCodes.Php, isCustomInput: true, extension: 'php'},
	  { code: "21", name: "React", isQuestionLanguage: false, isProjectLanguage : true, isIdeLanguage : false, mainFile : "index.js", defaultCode : defaultCodes.JavaScript, isCustomInput: false, extension: 'js'},	
	  { code: "22", name: "HTML, CSS, JS", isQuestionLanguage: false, isProjectLanguage : true, isIdeLanguage : false, mainFile : "index.html", defaultCode : defaultCodes.Html, isCustomInput: false, extension: 'html'},	
    { code: "2", name: "React-Jest", isQuestionLanguage: false, isProjectLanguage : false, isInterviewLanguage: false, isIdeLanguage : false, mainFile : "index.html", defaultCode : defaultCodes.Html, isCustomInput: false, extension: 'html'},	
	  { code: "23", name: "MySql", isQuestionLanguage: true, isProjectLanguage : false, isInterviewLanguage: true, isIdeLanguage : true, defaultCode : defaultCodes.sql, isCustomInput: false, extension: 'sql'},
	//   { code: "24", name: "Oracle", isQuestionLanguage: true, isProjectLanguage : false, isInterviewLanguage: true, isIdeLanguage : true, defaultCode : defaultCodes.sql, isCustomInput: false, extension: 'sql'},
]

const questionSupportedLanguages = supportedLanguages.filter(langObj => langObj.isQuestionLanguage);
const projectSupportedLanguagesArr = supportedLanguages.filter(langObj => langObj.isProjectLanguage);
const ideSupportedLanguagesArr = supportedLanguages.filter(langObj => langObj.isIdeLanguage);

const getLangExtensionByCode = function(langCode) {
    for (let index = 0; index < supportedLanguages.length; index++) {
        if ( supportedLanguages[index].code == langCode )    return supportedLanguages[index].extension;
    }
    return '';
}

const quizParams = [
    { code: "0", name: "Roll Number", isEnabled: false, id: "rollNo"},
    { code: "1", name: "Branch", isEnabled: false, id: "branch" },
    { code: "2", name: "Batch", isEnabled: false, id: "batch" },
    { code: "3", name: "College", isEnabled: false, id: "college" },
    { code: "4", name: "City", isEnabled: false, id: "city" },
    { code: "5", name: "State", isEnabled: false, id: "state" }
];

const addPermissions = {
    'addBatch' : 'addBatch' ,
    'addQuestion': 'addQuestion',
    'addCourse': 'addCourse',
    'addTutorial': 'addTutorial',
    'addQuiz': 'addQuiz',
    'addUser': 'addUser',
    'addAdminProject' : 'addAdminProject'
};

const SubmittedProjects = "SubmittedProjects";
const PublicFolderString = "Public";
const ProjectsFolderString = "Projects";
const defaultAllowedLanguages = [ '7' ];

const compilerVersion = {
    '7' : 'gcc version 5.4.0',
    '17' : 'gcc version 5.4.0',
    '77' : 'g++ version 5.4.0',
    '18' : 'g++ version 5.4.0',
    '19' : 'g++ version 5.4.0',
    '8' : "java version 1.8.0_171\n" +
    "Java(TM) SE Runtime Environment (build 1.8.0_171-b11)\n" +
    "Java HotSpot(TM) 64-Bit Server VM (build 25.171-b11, mixed mode)",
    '5' : 'version 3.11.0',
    '11' : "GNU bash, version 4.3.48(1)-release (x86_64-pc-linux-gnu)\n" +
    "Copyright (C) 2013 Free Software Foundation, Inc.\n"

};

// Compile Servers Params
const compileServerConstants = {
    //masterCompileServerUrl: "http://cs1.dhokha.ga:8005/compile/code",
    masterCompileServerUrl: process.env.compileMasterHost + "/compile/code",
    responseUrl: "http://" + process.env.host + ":" + argv[0] + "/compileResponse",                    // "http://dhokha.ga:3002/compileResponse",
    serversIpListFileName: "compileServersIp",
    projectServersName: "projectCompileServers",
    errorCountThreshold: 5,
    serverStatus: {active: 'Active', inActive: 'InActive'},
    codeType: {                         // codeType is used to handleCompileResult method
        PROJECT_CODE: 0,
        DEFAULT_CODE: 1,
        IDE_CODE: 2,
        IDE_INTERACTIVE_CODE: 3,
        TUTORIAL: 4,
        MAX: 5,
        CLOUD_TEST_CASE: 6,
    }
}

const digitalOceanToken = "****************************************************************";
const compileServerDropletId = "126640930";
const masterCompileServerIp = "*************";
const compileServerSnapshotId = "44344269"                      // TODO

const compileServerDropletConf = {
    size: "s-4vcpu-8gb",
    region: "blr1"
}

const compileServerApiShellCommands = {
    updateSnapshot: function() {
        let currentDate = new Date();
        let snapshotName = `compileServerSnapshot-${currentDate.getDate()}-${(currentDate.getMonth()+1)}-${currentDate.getFullYear()}-${currentDate.getHours()}:${currentDate.getMinutes()}:${currentDate.getSeconds()}`; 
        return `curl -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${digitalOceanToken}" -d '{"type":"snapshot","name":${snapshotName}}' "https://api.digitalocean.com/v2/droplets/${compileServerDropletId}/actions"`;
    },

    deleteSnapshot: function(snapshotId) {
        return `curl -X DELETE -H 'Content-Type: application/json' -H 'Authorization: Bearer ${digitalOceanToken}' "https://api.digitalocean.com/v2/snapshots/${snapshotId}"`;
    },

    snapshotList: function() {
        return `curl -X GET -H "Content-Type: application/json" -H "Authorization: Bearer ${digitalOceanToken}" "https://api.digitalocean.com/v2/snapshots"`;
    },

    sizesList: function() {
        return `curl -X GET -H "Content-Type: application/json" -H "Authorization: Bearer ${digitalOceanToken}" "https://api.digitalocean.com/v2/sizes?per_page=200"`;
    },

    regionsList: function() {
        return `curl -X GET -H "Content-Type: application/json" -H "Authorization: Bearer ${digitalOceanToken}" "https://api.digitalocean.com/v2/regions?per_page=200"`;
    },

    createDroplet: function(name, region, size, snapshotId) {
        return `curl -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${digitalOceanToken}" -d '{"name":"${name}","region":"${region}","size":"${size}","image":${snapshotId},"ssh_keys":null,"backups":false,"ipv6":false,"user_data":null,"private_networking":null,"volumes": null,"tags":["web"]}' "https://api.digitalocean.com/v2/droplets"`;
    }
};

const tagsIdType = {
    courseId: 'courseId',
    questionId: 'questionId',
    tutorialId: 'tutorialId',
    batchId: 'batchId',
    adminId: 'adminId',
    quizId: 'quizId',
    projectId: 'projectId'
}

const testPlatform = {
    'browser': 1,
    'app': 2
}

const testPlatformNumberToName = Object.entries(testPlatform).reduce((result, currentValue) => {
    result[currentValue[1]] = currentValue[0];
    return result;
}, {});

const questionDifficultyLevel = {
    Easy: '0',
    Medium: '1',
    Hard: '2'
}

const questionSubType = {
    Basic: '0',
    Practice: '1',
    Premium: '2'
}

//  constant used for the apply users set user id who create test
const applySubAdminEmail = "<EMAIL>";
const capchaKeys = {
    prodSiteKey : '6LfB15wUAAAAAEzQ_IANz31uxxnA0dVbM3RfoXKg' ,
    prodSecretKey : '6LfB15wUAAAAABJG_-GO7Z0mh9Qd_RluzuoChwxe',
    SiteKey : process.env.isQuizServer && parseInt(process.env.isQuizServer) ? '6LcjFMEUAAAAAA-DIk29eU3En7RuB2FTr8AhwmP9' : '6LeF4ZwUAAAAANu3nzJUq_Byiq0HI6AS33X_1X89',
    SecretKey : process.env.isQuizServer && parseInt(process.env.isQuizServer) ? '6LcjFMEUAAAAAGVWwVG2ScfdvkLMYyubjO5bg2t6' : '6LeF4ZwUAAAAAJIAsjcl2PL8UbQ0S3Na0jFvwt3O' ,
    recaptcha_url : 'https://www.google.com/recaptcha/api/siteverify?'
}

// questionPool db _id
const poolQuestDifficultyLevel = {
    '1': 'Easy',
    '2': 'Medium',
    '3': 'Hard',
};

const poolQuestDifficultyLevelNumToStr ={
    'Easy': '1',
    'Medium': '2',
    'Hard': '3',
}

const applyTestObj ={
    description : "Apply user test.",
    quizTime : "60",
    quizStartDate : "60",
    quizEndDate : "120",
    progLang : JSON.stringify(["7","77","8"]),
    attemptInSequence : true,
    quizCode : "12345",
    instructions : "Hii This is test for the apply users.",
    keywords : "",
    isApplyTest : true,
}

const defaultProgLangForQuiz = ["7"]

const staticServer = {
    // link: "http://localhost:4000",
    //link: "https://static.codequotient.com"
    link: process.env.staticHost,
    addProjectRoute: "/project/addProject",
    forkProjectRoute: "/project/forkProject",
    projectDirectoryRoute: "/project/directory",
    addFileOrDirectory: "/project/addFileOrDir",
    renameFileOrDirectory: "/project/renamePath",
    renameProject: "/project/renameProject",
    removeFileOrDirectory: "/project/removeFileOrDir"
}

const quizParamsHMSET = {
    _id : { key : "_id", index : 0},
    title : { key : "title", index : 1},
    time : { key : "time", index : 2},
    quizContentLength : { key : "quizContentLength", index : 3},
    endTime : { key : "endTime", index : 4 },
    instructions : { key : "instructions", index : 5 },
    startTime : { key : "startTime", index :  6 },
    creatorId : { key : "creatorId", index : 7},
    quizCode : { key: "quizCode", index : 8},
    allowedIP : { key: "allowedIP", index : 12},
    entryStopTime : { key : "endTime", index : 13 },
    quizSegments : { key : "quizSegments", parse : true },
    quizContent : { key : "quizContent", parse : true },
    attemptInSequence : { key : "attemptInSequence", boolean : true },
    revisitAllowed : { key : "revisitAllowed", boolean : true },
    displayStatus : { key : "displaystatus" },
    languagesAllowed : { key : "languagesAllowed", parse : true },
    quizTime : { key : "quizTime" },
    copyPasteAllowed : { key : "copyPasteAllowed", boolean : true },
    tabSwitchAllowed : { key : "tabSwitchAllowed", boolean : true },
    sendMailToMentorAllowed : { key : "sendMailToMentorAllowed", boolean : true },
    createdBy : { key : "createdBy" },
    parentIdOfCreator : { key : "parentIdOfCreator" },
    quizParams : { key: "quizParams" , parse : true },
    isWebCamAllowed: { key: "isWebCamAllowed", boolean : true },
    isRandomImageCaptureEnabled: { key: "isRandomImageCaptureEnabled", boolean : true },
    isAIProctoringEnabled: { key: "isAIProctoringEnabled", boolean : true },
    isPrivate: { key: "isPrivate", boolean : true },
    isSignUpAllowed: { key: "isSignUpAllowed", boolean : true },
    quizEmailSetting: { key: "quizEmailSetting", parse : true },
    cutOffMarks : { key : "cutOffMarks"},
    questionId : { key : "questionId", parse : true},
    randomizeQuestion : { key : "randomizeQuestion"},
    poolQuestion : { key : "poolQuestion",boolean : true},
    updatedAt : { key : "updatedAt"},
    quizUserDetails : { key: "quizUserDetails" , parse : true },
    hasQuizStarted : { key : "hasQuizStarted", boolean : true},
    logo: { key: "logo"},
    tabSwitchAlertLimit: { key: "tabSwitchAlertLimit" },
    isFullScreen: { key: "isFullScreen", boolean: true },
    isAppOnly: { key: "isAppOnly", boolean: true },
    isRecordingEnabled: {key: "isRecordingEnabled", boolean: true},
    isRecordingUploadBlockingEnabled: { key: 'isRecordingUploadBlockingEnabled', boolean: true },
    isLiveStreamEnabled: {key: "isLiveStreamEnabled", boolean: true},
    allowClose: {key: "allowClose", boolean: false},
    isCodingQuesPresent: { key: "isCodingQuesPresent", boolean: true},      // will true if there is coding question exists in the quiz
    orgId: {key: "orgId"},
    submitTestOnTabSwitchLimitBreach: {key: 'submitTestOnTabSwitchLimitBreach', boolean: true},
    onlySuspiciousRoom: { key: 'onlySuspiciousRoom', boolean: true },
    isVmDetectionEnabled: { key: 'isVmDetectionEnabled', boolean: true },
    toTerminateTestOnVmDetection: { key: 'toTerminateTestOnVmDetection', boolean: true },
    toShuffleMCQOptions: { key: "toShuffleMCQOptions", boolean: true },
}

const JWTSecret = "SU57=F22+F35";

const maxLoginAttemptLeft = 5;
const loginErrorMsg = {
    exceedAndSendMail : `Login Attempt Exceed. Please check your email.`,
    exceedMsg : `Please check your email.`,
    incorrectEmail : `User does not exist.`,
}
const activeUserOrgIdExpireTimeInSec = 60 * 5; 
const redisExpireTimeConstant = 86400 //60*60*24
const processDataRemovalInterval = 60000 * 60 * 6 ;
const sessionCookieKey = 'hackoo';
const maxQuestionInCache = 200;
const submissionQueueData = {
    thresholdLoad : 10,
    timeBasedCounters : [ 60000 ],
    maxTimeSlice : 60000,
    timeSlices : 10,
    writeInterval : 15000,
    maxWriteInterval : 45000,
    minSubmissionInterval : 1000,
    maxSubmissionInterval : 60000 * 7,
    modifySubmissionInterval : 30000,
    oldIntervalCleaningInterval : 60000 * 5,
    defaultSliceInterval : 60000,
    offsetTimeAfterQuizTime : 
    ( process.env.NODE_ENV == "production") 
    ? 10 * 60 : 5 * 60,
}

const cachingKeys = {
    question : { controlKey : 'BYPASS_QUESTION_IN_RAM',message : "Used for using process memory questions", 
    type : '1', redisKeyPrefix : `${redisKeys.questionString}:`, processMapName : 'questionData',
    name : 'question', idlePeriod : 60000 * 60 * 5},
    quiz : { controlKey : 'BYPASS_QUIZ_IN_RAM', message : "Used for using process memory quiz", type : '2',
    redisKeyPrefix : `${ redisKeys.quizIdString }:`, processMapName : 'quizData', name : 'quiz',
    idlePeriod : 60000 * 60 * 5 },
    quizAllowedEmailIds: {
        type: '3',
        controlKey: 'BYPASS_QUIZ_ALLOWED_EMAILIDS_IN_RAM',
        processMapName: 'quizAllowedEmailIdsData',
        name: 'quizAllowedEmailIds',
        idlePeriod : 60000 * 60 * 5,
    }
}

const jestTestCaseStatus = {
    pass : { text : "passed" },
    fail : { text : "failed" },
    passed : { text : "passed" },
}

const STATIC_STORAGE_API_URL = process.env.NODE_ENV === 'production' 
  ? 'https://static.test.codequotient.com'
  : 'https://test-static.cqtestga.com'

  const quizMailSettings = {
    testCompletionStudentMail : { 
        'sendMail' : { label : "Send Mail", value : 1 },
        'dontSendMail' : { label : "Don't Send Mail", value : 0 },
    },
    quizCompletionIntimationSetting : {
        'sendMail' : { label : "Send Mail", value : 1 },
        'dontSendMail' : { label : "Don't Send Mail", value : 0 },
    }
}

const mailTemplateVariables = {
    'name' : { field : 'displayname', templateString : 'name',  label:'User Name'},
    'testTitle' : { field : 'title', templateString : 'testTitle', label:"Test Title"},
    'testInviteLink': { field : 'testInviteLink', templateString : 'testInviteLink', label:'Invite Link' },
    'testDurationInMins': { field : 'testDurationInMins', templateString : 'testDurationInMins', label:'Test Duration(mins)' },
    'rollNumber': { field: 'enrollmentId', templateString: 'rollNumber', label: 'Roll Number'},
    'senderName': {field: 'senderName', templateString: 'senderName', label: 'Sender Name'},
    'senderEmail': {field: 'senderEmil', templateString: 'senderEmil', label: 'Sender Email'},
}

const expiryTimeOfQuizReportInSec = 60 * 60 * 24 * 3;  // 3 days

const userTimelineEventTypes = {
    'courseAssigned' : { label : "Course Assigned", value : 1 },
    'courseCompleted' : { label : "Course Completed", value : 2 },
    'courseUnassigned' : { label : "Course Completed", value : 3 },
    'quizStarted' : { label : "Quiz started", value : 4 },
    'quizEnded' : { label : "Quiz ended", value : 5 },
}

const awsS3Constants = {
    bucketName: 'cq-test-1',
    testCaseFolderName: 'testCases',
    getPrefix: function(p) { return `${ ( p && p.replace(/\\/g,'/') )|| '' }`},  // in case of windows // is present in path
    getTestCaseStoragePath: function(quesId, testId) { return `testCases/${quesId}/${testId}`; },
}

const maxRecentQuizSubmissionsSaveInRedis = 100;

const QuizFormSegmentTypes = {
	BasicDetails: 0,
	TestAccess: 1,
	TestContent: 2,
	Proctoring: 3,
    Notifications: 4,
};

const QuizUserDetailsInputType = {
	TextInput: 0,
	Select: 1,
	Checkbox: 2,
	Radio: 3,
};


const DefaultQuizUserDetails = [
    {
        "fieldLabel" : "ID",
        "fieldName" : "id",
        fieldType: QuizUserDetailsInputType.TextInput,
        "fieldIsSelected" : false,
        "fieldOptions" : [ ]
    },
    {
        "fieldLabel" : "Group",
        "fieldName" : "group",
        fieldType: QuizUserDetailsInputType.TextInput,
        "fieldIsSelected" : false,
        "fieldOptions" : [ ]
    },
    {
            "fieldLabel" : "College",
            "fieldName" : "college",
            fieldType: QuizUserDetailsInputType.TextInput,
            "fieldIsSelected" : false,
            "fieldOptions" : [ ]
    },
    {
            "fieldLabel" : "City",
            "fieldName" : "city",
            fieldType: QuizUserDetailsInputType.TextInput,
            "fieldIsSelected" : false,
            "fieldOptions" : [ ]
    },
    {
            "fieldLabel" : "State",
            "fieldName" : "state",
            fieldType: QuizUserDetailsInputType.TextInput,
            "fieldIsSelected" : false,
            "fieldOptions" : [ ]
    }
];

const tabSwitchEventType = {
    in: 1,
    out: 2,
}

const quizBackendKey = "jabsjhdbajhsdgiualbdiqudbajkhdoi462341264464#$#@$Q#6d1a6s5d1a65d1as86d4asd61ad8";

const resourceStringToNumber = {
    'user': 1,
    'batch': 2,
    'quest': 3,
    'tutorial': 4,
    'course': 5,
    'quiz': 6,
    'snapshot': 7,
    'tags': 8,
    'attempts': 9,
    'suggestTestCase': 10,
    'acc': 11,
    'project': 12,
    'adminProject': 13,
    'mentor': 14,
    'error': 15,
    'poolQuest': 16,
    'class': 17,
    'feedback': 18,
    'user-quiz': 19,
    'attempt': 20,
    'user-dashboard': 21,
    'blog': 22,
    'institution': 23,
    'userBatchActivity': 24,
    'contact': 25,
    'leads': 26,
    'email': 28,
}

const actionStringToNumber = {
    'courseAdminPermission': -1,
    'add': 1,
    'edit': 2,
    'list': 3,
    'delete': 4,    
    'details': 5,
    'batchInCourse': 6,
    'addStudent': 7,
    'removeStudent': 8,
    'bulk': 9,
    'questionInCourse': 10,
    'questionInQuiz': 11,
    'preview': 12,
    'detail': 13,
    'addFile': 14,
    'emailTemplate': 15,
    'export': 16,
    'aws': 17,
    'view': 18,
    'tutorialInCourse': 19,
    'complete': 20,
    'clone': 21,
    'removeMentor': 22,
    'createSegments': 23,
    'addBatch': 24,
    'removeBatch': 25,
    'link': 26,
    'report': 27,
    'provideExtraTime': 28,
    'courseAttempt': 29,
    'quizAttempt': 30,
    'mentorInCourse': 31,
    'archive': 32,
    'setQuiz': 33,
    'doc': 34,
    'addContent': 35,
    'removeContent': 36,
    'dashboard': 37,
    'data': 38,
    'addMentor': 39,
    'create': 40,
    'join': 41,
    'updateStatus': 42,
    'validate': 43,
    'start': 44,
    'logout': 45,
    'forceLogin': 46,
    'quizQuestion': 47,
    'question': 48,
    'tutorial': 49,
    'contentLink': 50,
    'completeClass': 51,
    'remainingTime': 52,
    'userReport' : 53,
    'classReport' : 54,
    'enableProject': 55,
    'enableDb': 56,
    'sendMail': 57,
    'enableUser': 58,
    'classInCourse': 59,
    'testInCourse': 60,
    'bulkAdd': 61,
    'bulkAssign': 62,
    'bulkUpdate': 63,
    'bulkDelete': 64,
    'bulkStatusUpdate' : 65,
    'bulkFollowUpdate' : 66,
    'followUpdate' : 67,
    'assignTo' : 68,
    'update' : 69,
    'activity': 70,
    'allLeads': 71,
    'createLink':72,
    'lastCalled':73,
    'exportAll': 74,
    'subjectiveUpload': 75,
    'suspicious': 76,
    'enrolmentBatch': 77,
    'projectQuestion': 78,
    'traceTableQuestion': 79,
    'allowAllTestSubmission': 80,
}


const questionKeys = {
    title: 'title',
    score: 'score',
    text: 'text',
    tags: 'tags',
    mcqCorrentAns: 'questionTypeMCQ.correctAnswers',
    courseOutcomes: 'courseOutcomes',
    topic: 'topic',
}

const submisssionTimeOffsetInSeconds = 20;

const quizScoreUpperLimit = 9999;

const teamKey = 'Team-KeyTo)Access!@#Clone!Questio##$orquiz'
const platform = {
    'Windows': 'win',
    'Linux': 'linux',
    'Mac': 'mac',
    'MacIntel': 'mac-intel',
};

const app_uri = 'test-codequotient';

const defaultIframePath =  {
    '/user': '/user/list',
    '/settings': '/setting',
    '/userRole': '/userRole/list',
    '/listTheRoles': '/userRole/list',
    '/email': '/email/list',
}

const defaultEmailSetting = JSON.stringify({
    'sendMail': false,
    'testCompletionStudentMail': '0',
    'quizCompletionIntimationSetting': 0,
    'mailTemplate': {
        'msg: ': [
            "hello ",
            "<name>",
            "\nYour "
            ,"<testTitle>"
            ," has been submitted successfully",
            "\nThanks;"
        ],
        "subject": ""
    },
    'intimationMailTemplate': {
        'msg': [
            "<name>",
            " submitted test",
        ],
        'subject': '',
    }
});

const dailyLimits = {
    'emailDailyLimit': 400,
    'concurrentUserLimit': 100,
    'dailyUserAttemptLimit': 1000,
}

const limitWarning = {
    'email' : parseInt(dailyLimits.emailDailyLimit / 100 * 80),
    'invite': (invitesAssigned = 100) => {
        return parseInt(invitesAssigned / 10 * 8);
    } 
}


const emailTypes = {
    'NewUser': 0,
    'ForgotPassword': 1,
    'TestInvite': 3,
    'InsufficientInviteLeft': 4,
    'Custom':5,
    'TestSubmit': 6,
    'EmailLimitWarning': 7,
    'EmailLimitBreached': 8,
    'ConcurrentUserBreached': 9,
    'InviteLimitWarning': 10,
    'InviteLimitBreached': 11,
    'TestClone': 14,
    'TestSubmissionOtp': 15,
};

const socketParams = {
    'userId': {key: 'userId'},
    'quizId': {key: 'quizId'},
    'orgId' : {key: 'orgId' },
}

const emailStatus = {
    'Pending': 0,
    'Sent': 1,
    'Error': -1,
}

const timeoutIncrement = 1000 * 60 * 10;

const testCloneJWTSecret = '6c906ece6578d06a0f76bd102b71ef94d7283cce';
const logger = {
    'redisLogger': {
        'filePath': path.join(__dirname, '../redisLogs.txt'),
        'parserFunction': (data) => {
            try {
                const cb = data.args?.pop()
                if(!(typeof cb == 'function')) {
                    data.args.push(cb);
                }
                return `redis command = ${data.args.join(' ')}`;
            } catch (error) {
                console.log(error);
                return `ERROR : ${error?.message}`;
            }
        }
    } 
}

const staticHostObj = {
    'default': process.env.STATIC_STORAGE_API_URL,
    'infra.assess.testpad.chitkara.edu.in': 'https://static.assess.testpad.chitkara.edu.in',
    'infra.assess.testpad.chitkarauniversity.edu.in': 'https://static.assess.testpad.chitkarauniversity.edu.in'
}

const appHostObj = {
    'default': process.env.APP_HOST,
    'assess.supercoders.tech': 'https://tests.supercoders.tech',
    'cqtestga.com': 'https://tests.cqtestga.com',
    'codequotient.com': 'https://tests.codequotient.com',
    'assess.testpad.chitkara.edu.in': 'https://exam.testpad.chitkara.edu.in',
    'assess.testpad.chitkarauniversity.edu.in': 'https://exam.testpad.chitkarauniversity.edu.in',
    'infra.assess.testpad.chitkara.edu.in': 'https://exam.testpad.chitkara.edu.in',
    'infra.assess.testpad.chitkarauniversity.edu.in': 'https://exam.testpad.chitkarauniversity.edu.in',
}

const jitsiHostObj = {
    'default': process.env.jitsiHost,
    'assess.supercoders.tech': '',
    'cqtestga.com': 'https://invigilate.cqtestga.com',
    'test.chitkara.cqtestga.com': 'https://invigilate.chitkara.cqtestga.com',
    'codequotient.com': 'https://invigilate.codequotient.com',
    'infra.assess.testpad.chitkara.edu.in': 'https://invigilate.testpad.chitkara.edu.in',
    'infra.assess.testpad.chitkarauniversity.edu.in': 'https://invigilate-v2.testpad.chitkarauniversity.edu.in',
}

const loginHostObj = {
    'default': process.env.loginHost,
    'assess.supercoders.tech': 'https://login.supercoders.tech',
    'cqtestga.com': 'https://login.cqtestga.com',
    'codequotient.com': 'https://login.codequotient.com',
    'infra.assess.testpad.chitkara.edu.in': 'https://login.testpad.chitkara.edu.in',
    'infra.assess.testpad.chitkarauniversity.edu.in': 'https://login.testpad.chitkarauniversity.edu.in',
}

const courseHostObj = {
    'default': process.env.courseHost,
    'assess.supercoders.tech': 'https://course.supercoders.tech',
    'cqtestga.com': 'https://course.cqtestga.com',
    'codequotient.com': 'https://course.codequotient.com',
    'assess.testpad.chitkara.edu.in': 'https://course.testpad.chitkara.edu.in',
    'assess.testpad.chitkarauniversity.edu.in': 'https://course.testpad.chitkarauniversity.edu.in',
    'infra.assess.testpad.chitkara.edu.in': 'https://course.testpad.chitkara.edu.in',
    'infra.assess.testpad.chitkarauniversity.edu.in': 'https://course.testpad.chitkarauniversity.edu.in',
}


const mapOfHostNameToEmailService = new Map(
    [
        ['codequotient.com',        '<EMAIL>'],
        ['login.codequotient.com',  '<EMAIL>'],
        ['course.codequotient.com', '<EMAIL>'],

        ['cqtestga.com',        '<EMAIL>'],
        ['login.cqtestga.com',  '<EMAIL>'],
        ['course.cqtestga.com', '<EMAIL>'],

        ['login.supercoders.tech',  '<EMAIL>'],
        ['course.supercoders.tech', '<EMAIL>'],
        ['assess.supercoders.tech', '<EMAIL>'],

        ['login.testpad.chitkara.edu.in', '<EMAIL>'],
        ['course.testpad.chitkara.edu.in', '<EMAIL>'],
        ['infra.assess.testpad.chitkara.edu.in', '<EMAIL>'],

        ['login.testpad.chitkarauniversity.edu.in',  '<EMAIL>'],
        ['course.testpad.chitkarauniversity.edu.in', '<EMAIL>'],
        ['infra.assess.testpad.chitkarauniversity.edu.in', '<EMAIL>'],
    ]
)
const userStateInTest = {
    'pause': 0,
    'resume': 1,
    'terminate': 2,
}

const getEmailLogo = () => {
    const basePath = '/mail';
    let pathToImage = `/${basePath}/cq.png`;
    if (process.env.CHITKARA_THEME) {
        pathToImage = `/${basePath}/chitkara.png`;
    }
    return pathToImage;
}


const mailConfig = {
    platform: process.env.PLATFORM_NAME,
    supportMail: process.env.EMAIL,
    emailBrand: `${process.env.loginHost}${getEmailLogo()}`,
    baseURL: `${(process.env.isHttps)?'https':'http'}://${process.env.baseDomain}`,
    emailSenderName: process.env.EMAIL_SENDER_NAME,
}

const mailTemplatePath = path.join(__dirname, '../Views/emailTemplate');

const emailContent = {
    'image': {
        subject: 'Custom Email',
        content: fs.readFileSync(path.join(mailTemplatePath,'/addLogo.ejs')).toString()
    },
    'invigilatorInvite': {
        subject: 'Inviting for quiz invigilation',
        content: fs.readFileSync(path.join(mailTemplatePath,'/invigilatorInvite.ejs')).toString(),
    },
    'submitTestForAllUserOtp': {
        subject: 'OTP for Test Submission',
        content: fs.readFileSync(path.join(mailTemplatePath ,'/otpForTestSubmission.ejs')).toString(),
    },
    'quizMarkedAsResolved': {
        subject: (quizTilte) => {
            return `Quiz ${quizTilte} is marked as resolved`;
        },
        content: fs.readFileSync(path.join(mailTemplatePath, '/quizMarkResolved.ejs')).toString(),
    },
}


const socketEvents = {
    userJoined: 'user_joined',
    userLeft: 'user_left',
    recordingEvent: 'recordingEvent',
    cheatingEvent: 'cheating_event',

    forceSubmit: 'force_submit',
    sectionUpdate: 'section_update',

    appConfigChange: 'app_config_change',
}

// ActivityLogs
const activityLogsType = Object.freeze({
    'submitAllQuiz': 1,
})

const activityLogsSubType = {
    [activityLogsType.submitAllQuiz]: {
        "success": 1,
        "error": 2,
    }
}

const submitAllQuizErrorType = {
    'otpInvalid': 1,
}

const dataParserActivityLogs = Object.freeze({
    [activityLogsType.submitAllQuiz]: (log, userSet) => {
        if (log.version === 1) {
            if (log.subType === activityLogsSubType[activityLogsType.submitAllQuiz].success) {
                if (log.data.userIds) {
                    log.data.userIds.forEach((element) => {
                        userSet.add(element.toString());
                    })
                }
                if (log.data.failedUserIds) {
                    log.data.failedUserIds.forEach((element) => {
                        userSet.add(element.toString())
                    })
                };
            }
            return log;
        }
    },
})

const dataTransferPadding = 'thisIsARandomPaddingIsThisYesOrNotProbablyYesButIDontKnow'
const dataTransferKeys = {
    publicKey: '-----BEGIN PUBLIC KEY-----\n' +
        'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDBPXeAfktTOHpoX6OsXCMiJQM\n' +
        'aQDTCwg0oEvGBgEJvkbzGlishpyyX998IrUwYfhMsAbSRtQTwepUlQf8mXan3aLC\n' +
        'qRtP5BJyLdyxkXvQk5XD/Kt6luov9aeofjUH1QAqtI71CFxKQcPDF3fuI6w2X4mR\n' +
        '9jMiR2ynxL/M5oASFwIDAQAB\n' +
        '-----END PUBLIC KEY-----\n',
    privateKey: '-----BEGIN PRIVATE KEY-----\n' +
        'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMME9d4B+S1M4emh\n' +
        'fo6xcIyIlAxpANMLCDSgS8YGAQm+RvMaWKyGnLJf33witTBh+EywBtJG1BPB6lSV\n' +
        'B/yZdqfdosKpG0/kEnIt3LGRe9CTlcP8q3qW6i/1p6h+NQfVACq0jvUIXEpBw8MX\n' +
        'd+4jrDZfiZH2MyJHbKfEv8zmgBIXAgMBAAECgYEAh0UfjZhs0AEuCjHcaPmnCbOK\n' +
        'jXkf+0MBtA8jv/1WJv32lJVHwJO2iPt7Ns2URDUQV5EZii1a2UF+WqDs40dHRz6K\n' +
        'MUeYMk9IQBfFB/LRRdoZAjWnC/woUz+UXJqP144jMkLSrHKiPYBZULBdJQHnjNnA\n' +
        'MYXYP+KS4cr1swS4pXECQQDnXj7lHuCzf+UVvpsqGly7eUGwOtCyKMlzyleEbeaP\n' +
        'nGRhU7UuZauT/wUsPw1xBIlYCIQ9aPct76T7/wcnRno9AkEA18gO2BUcx75EVY04\n' +
        '+KVU60yFo9ilo/2a8vLclqMcGfRvsFxyLdYLyvU/EzrUbSiCwFw3SFnddURx/1No\n' +
        'TjlG4wJBAMuS4UQ4Jv3JSogXbe9dwroxttqY5Yfc22RGZTrgeD1kHe6E+KYaTx2p\n' +
        'BaQo1mra0v3igHiIIvUIJuKTQngMSwECQBeI7nWK9/ShP/rEyG32Omjfk8PnIUnd\n' +
        'o0TUUFLtv2blTjlCHAH70pV0YkjKpdiyDnDEnhL6GjlsGbLrefjuLlkCQFSCI66U\n' +
        'bd4QIb5RFBkwF1asDaq3DEwgztIFh2yQbcU+O8NM/kYz86BJ3dN6wOFzhZ1ViE6b\n' +
        '6kUWVm9hSMaWRMw=\n' +
        '-----END PRIVATE KEY-----\n'
}

const cheatingType = {
	paste: '1',
	tabSwitch: '2',
	webcamBlock: '6',
	fullScreen: '9',
	aiProctor: '11',
    recordingUploadFailed: '99',
}

const aiProctoringCheatingSubType = {
    userExit: 1,
    userEnter: 2,
    illegalObject: 3,
    multipleUserDetected: 4,
}

const aiProcterFromNumberToString = Object.keys(aiProctoringCheatingSubType).reduce((result, current) => {
    result[aiProctoringCheatingSubType[current]] = current;
    return result;
}, {});

const hostNameToWhiteLabel = {
    'infra.assess.testpad.chitkarauniversity.edu.in': 'Chitkara University',
    'infra.assess.testpad.chitkara.edu.in': 'Chitkara',
}

const maintenanceMode = path.join(__dirname, '../MaintenanceMode.json')
console.log(maintenanceMode);

const jitsiConfigJson = path.join(__dirname, '../jitsiConfig.json');
console.log(jitsiConfigJson);

const segmentHistoryType = {
    'submission': 1,
    'reset': 2,
}

const fileUploadEvent = {
	fileNotPresent: 1,
	serverError: 2,
	fileUploaded: 3,
    fileRecordingComplete: 4,
    timelineVideoRecordingComplete: 5,
}

const jitsiBucketConstants = {
    active: 1,
    inUse: 0,
}


const questionBloomTaxonomyOld = [
    'remembering',
    'understanding',
    'applying',
    'analyzing',
    'evaluating',
    'creating',
]

const questionBloomTaxonomy = {
    "L1": 1,
    "L2": 2,
    "L3": 3,
    "L4": 4,
    "L5": 5,
    "L6": 6,
}

const questionBloomTaxonomyNumberToString = {
    [questionBloomTaxonomy.L1]: "L1 - Remembering",
    [questionBloomTaxonomy.L2]: "L2 - Understanding",
    [questionBloomTaxonomy.L3]: "L3 - Applying",
    [questionBloomTaxonomy.L4]: "L4 - Analyzing",
    [questionBloomTaxonomy.L5]: "L5 - Evaluating",
    [questionBloomTaxonomy.L6]: "L6 - Creating",
}

// const questionBloomTaxonomyNumberToString = Object.keys(questionBloomTaxonomy).reduce((result, current) => {
//     result[questionBloomTaxonomy[current] ] = current;
//     return result;
// }, {});

const questionBloomTaxonomyOldToNew = {
    [questionBloomTaxonomyOld[0]]: questionBloomTaxonomy.L1,
    [questionBloomTaxonomyOld[1]]: questionBloomTaxonomy.L2,
    [questionBloomTaxonomyOld[2]]: questionBloomTaxonomy.L3,
    [questionBloomTaxonomyOld[3]]: questionBloomTaxonomy.L4,
    [questionBloomTaxonomyOld[4]]: questionBloomTaxonomy.L5,
    [questionBloomTaxonomyOld[5]]: questionBloomTaxonomy.L6,
}

const quizRoomPrefix = 'quiz-room';

const jitsiDefaultConfig = Object.freeze({
    disablePolls: true,
    prejoinConfig: {
	enabled: true,
	hideDisplayName: true,
    },
    recording: {
	recordAudioAndVideo: false,
	suggestRecording: false,
    },
    localRecording: {
	disable: false,
    },
    toolbarButtons: [
	'camera',
	'closedcaptions',
	'desktop',
	'fullscreen',
	'hangup',
	'highlight',
	'noisesuppression',
	'microphone',
	'chat',
	'participants-pane',
	'noisesuppression',
	'tileview',
    ],
    filmstrip: {
	disableStageFilmstrip: false,
    }
});

const feedBackNumberToString = {
    '1' : 'Poor',
    '2' : 'Average',
    '3' : 'Good',
    '4' : 'Excellent',
}

const forceLogoutType = {
    default: 0,
    byAdmin: 1,
    dueToTabSwitch: 2,
    dueToVmDetection: 3,
}

const quizProgressStep = {
    'basic' : { value : 1, label : "Basic Details"},
    'access' : { value : 5, label : "Access Details"},
    'content' : { value : 10, label : "Test Content"},
    'proctoring' : { value : 15, label : "Proctoring Details"},
    'notifications' : { value : 20, label : "Notifications"},
}

const reactFrontEndUrl = {
    'infra.assess.testpad.chitkara.edu.in': 'https://assess.testpad.chitkara.edu.in',
    'infra.assess.testpad.chitkarauniversity.edu.in': 'https://assess.testpad.chitkarauniversity.edu.in',
    'codequotient.com': 'https://app.codequotient.com',
}

const inviteHost = {
    'cqtestga.com': 'invite.cqtestga.com',
    'infra.assess.testpad.chitkara.edu.in': 'assess.testpad.chitkara.edu.in',
    'infra.assess.testpad.chitkarauniversity.edu.in': 'assess.testpad.chitkara.edu.in'
}

module.exports = {
    quizProgressStep,
    submisssionTimeOffsetInSeconds,
    resourceStringToNumber,
    actionStringToNumber,
    userTimelineEventTypes,
    quizMailSettings,
    mailTemplateVariables,
    processDataRemovalInterval,
    jestTestCaseStatus,
    ProjectsFolderString, PublicFolderString, bytesToMegaBytes, quizParamsHMSET, 
    userQuizSubmittedSegmentKeysArray, userQuizSubmittedSegmentKeys,
    badActivitiesTypeNumber, badActivitiesTypeString,
    badActivityCheatingType, questionFileUploadPath,
    courseFileUploadPath, roleStringFromNumber, roleNumberFromString,
    constantString, contentTypeString, questionTypeProperties,
    questionTypeNumeric, bucketSize, langStringToLangCode,
    langCodeToLangString, supportedLanguages, quizParams,
    maximumTestCases, codingQuestionExecutionType, quizContentCodingQuestionDefaults,
    compilePriority, bulkUploadFormat, sessionSecret, redisKeys ,
    segmentationFaultString, contentActiveStatus, defaultProfileLink,
    defaultInfoMail, defaultDisplayName, modelForPagination, modelString ,
    endOfInputString, queryStatus, displayStatus, maximumOutputMemoryAllowed,
    killedString, endOfIterationString, maximumMemoryAllowedForQuestionsInRedis,
    thresholdForQueues, coreDumpedString,
    userQuizSubmittedSegmentDataType, quizMGETArray, quizMGETArrayPropertyIndex,
    num_processes, projectStatus, defaultUserQuota, memorySizeConstants, questionOutcomes, memoryString,
    blockSize, fileScriptName, changedFileScript, imageInitial, mountFolderBasePath,
    folderUnmountScriptName, unmountAllFoldersScriptName, directoryString, languageCodes,
    defaultCodes, addPermissions, SubmittedProjects, defaultAllowedLanguages,
    compilerVersion, dbBackUpScript, uploadsFolderString, questionFolderString,
    tutorialFolderString, tutorialFileUploadPath, quizFileUploadPath, courseFolderString,
    compileServerConstants, compileServerApiShellCommands, digitalOceanToken,
    compileServerDropletId, masterCompileServerIp, compileServerSnapshotId, tagsIdType,
    questionDifficultyLevel, questionSubType, compileServerDropletConf, capchaKeys ,
    staticServer, JWTSecret, maxLoginAttemptLeft, loginErrorMsg,
    applyTestObj, defaultProgLangForQuiz,
    poolQuestDifficultyLevel, poolQuestDifficultyLevelNumToStr,
    applySubAdminEmail, redisExpireTimeConstant, sessionCookieKey,
    redisQuestionKeys, redisQuestionKeysRequiredParsing,
    maxQuestionInCache,
    questionTypeOptions,
    submissionQueueData,
    cachingKeys,
    quizFolderString,
    projectSupportedLanguagesArr,
    ideSupportedLanguagesArr,
    getLangExtensionByCode,
    STATIC_STORAGE_API_URL,
    expiryTimeOfQuizReportInSec,
    questionSupportedLanguages,
    awsS3Constants,
    maxRecentQuizSubmissionsSaveInRedis,
    QuizFormSegmentTypes,
    QuizUserDetailsInputType,
    DefaultQuizUserDetails,
    tabSwitchEventType,
    quizBackendKey,
    questionKeys,
    quizScoreUpperLimit,
    teamKey,
    platform,
    app_uri,
    fileUploadEvent,
    defaultIframePath,
    defaultEmailSetting,
    dailyLimits,
    emailTypes,
    metaDataParams,
    socketParams,
    emailStatus,
    activeUserOrgIdExpireTimeInSec,
    sessionCookieName,
    limitWarning,
    timeoutIncrement,
    liveStreamTokenUpto_Sec,
    liveStreamRedisCacheTime_sec,
    testCloneJWTSecret,
    testPlatform,
    testPlatformNumberToName,
    sessionExpireTimeSeconds,
    logger,
    appHostObj,
    loginHostObj,
    courseHostObj,
    mapOfHostNameToEmailService,
    userStateInTest,
    emailContent,
    mailConfig,
    socketEvents,
    activityLogsType,
    activityLogsSubType,
    otpExireTimeSeconds,
    otpIntervalsSeconds,
    socketTimeoutInSeconds,
    dataParserActivityLogs,
    submitAllQuizErrorType,
    staticHostObj,
    cheatingType,
    aiProctoringCheatingSubType,
    dataTransferKeys: dataTransferKeys,
    dataTransferPadding,
    aiProcterFromNumberToString: aiProcterFromNumberToString,
    hostNameToWhiteLabel,
    maintenanceMode,
    segmentHistoryType,
    jitsiBucketConstants,
    questionBloomTaxonomy,
    suspiciousRoomNameSuffix,
    quizRoomPrefix,
    jitsiDefaultConfig,
    jitsiHostObj,
    latestCheatingEventExpireTimeInSec,
    feedBackNumberToString,
    freezeChangeLogPrefix,
    jitsiConfigJson,
    forceLogoutType,
    reportFileUploadPath,
    reactFrontEndUrl,
    questionBloomTaxonomyOldToNew,
    questionBloomTaxonomyOld,
    questionBloomTaxonomyNumberToString,
    shareTestExpireTimeInDays,
    cloudTestCaseInputString,
    inviteHost,
};
