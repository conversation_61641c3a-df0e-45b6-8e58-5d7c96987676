const fs = require('fs');
const path = require('path');
const dayjs = require('dayjs');
const _ = require('lodash');

const offset = 50000;

class PerformaceLogger{
    #logs = '';
    #filePath;
    #pFunction;
    /**
     * 
     * @param {string} filePath 
     * @param {([string]:any) => void} parserFunction 
     */
    constructor (filePath, parserFunction) {
        if (!filePath) throw new Error('File Path Not Provided');
        if (!parserFunction) console.warn(`Praser Function not provided using default parser`);
        this.#filePath = filePath;
        if (!fs.existsSync(filePath)) {
            fs.writeFileSync(filePath, '')
        }
        this.#pFunction = parserFunction ?? this.#defaultParserFunction;

        process.on('exit', () => {
            this.forceFlush();
        });
    }
    log(data) {
        data.startTime = Date.now();
        return () => {
            data.endTime = Date.now();
            this.#logs += this.#parserFunction(data); 
            this.flush()
        }
    }
    #parserFunction (data) {
        return `\nStartTime: ${dayjs(data.startTime).format('YYYY-MM-DDTHH:mm:ss:SSSZ[Z]')} EndTime: ${dayjs(data.endTime).format('YYYY-MM-DDTHH:mm:ss:SSSZ[Z]')} Duration = ${data.endTime - data.startTime} ms ${this.#pFunction(data)}`
    }
    #defaultParserFunction (data) {
        let arrayOfValues = [];
        Object.keys(data).forEach((key) => {
            const value = data[key];
            arrayOfValues.push(`${key}:  `, `${value},\t`);
        })
        return arrayOfValues.join();
    }
    async flush() {
        const toAppend = this.#logs;
        try {
            if ( this.#logs.length < offset ) {
                return false;
            }
            if (!this.#logs) return false;
            this.#logs = '';
            await fs.promises.appendFile(this.#filePath, toAppend);
            return true;
        } catch (error) {
            console.log(error);
            this.#logs += toAppend; 
            return false;
        }
    }

    async forceFlush() {
        try {
            if (!this.#logs) return false;
            const toAppend = this.#logs;
            this.#logs = '';
            fs.appendFileSync(this.#filePath, toAppend);
            return true;
        } catch (error) {
            console.log(error);
            return false;
        }
    }
}


module.exports = PerformaceLogger;