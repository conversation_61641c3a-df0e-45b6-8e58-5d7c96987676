const quizTitleRegex = /^[a-zA-Z0-9-@,&/.()\[\]\s+]{1,125}$/

const quizNameRegex =  /^[a-zA-Z0-9-]{1,30}$/

const questionTitleRegex = /^[^\s][a-zA-Z0-9-@,&/.()\[\]+ ]{1,125}$/

const questionDescription = /[^.*$]{1}/

const emojiDetector =  /[\p{Extended_Pictographic}\u{1F3FB}-\u{1F3FF}\u{1F9B0}-\u{1F9B3}]/u;

const tagsRegex = /^[a-zA-Z0-9)(!@#$%^&*+={}|;:'"/?>,,._\-`~\[\]\\]{1,200}$/;

const streamRoomRegex = /^[a-z0-9-@,&/.()+]{1,50}$/

const courseOutcomeRegex = lengthRegex(50);

const topicRegex = lengthRegex(50);

function lengthRegex(number){
	if (!number) number = 50;
	return new RegExp(`^.{0,${number}}$`);
}

module.exports = {
    quizTitleRegex,
    quizNameRegex,
    questionTitleRegex,
    questionDescription,
    emojiDetector,
    tagsRegex,
    streamRoomRegex,
    courseOutcomeRegex,
    topicRegex,
    lengthRegex,
}
