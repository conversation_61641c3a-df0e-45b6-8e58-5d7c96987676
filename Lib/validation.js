const regex = require('./regex')
const constant = require('./constants');

const isValidQuizUserDetails = (quiz,req) => {
    let content = new Set();
    quiz?.quizUserDetails?.reduce((result,element)=>{
        if(element?.fieldIsSelected){
            // result.push({fieldLabel,fieldName,fieldOptions,fieldType}=element);
            result.add(element.fieldLabel);
        }
        return result;
    },content);
    
    for(let i=0;i<req?.body?.quizUserDetails?.length ?? 0; ++i){
        if(content.has(req.body.quizUserDetails[i].fieldLabel)){
            content.delete(req.body.quizUserDetails[i].fieldLabel);
        }else{
            return false;
        }
    }

    return !content.size
}

const quizValidationFunction = function (key, value){
    if(value === null || value === undefined){
        return false;
    }
    switch (key){
        case constant.quizParamsHMSET.title.key:{
            return regex.quizTitleRegex.test(value);
        }
        case constant.quizParamsHMSET.quizTime.key:{
            return parseInt(value) !== NaN && value > 0 && value < 100000;
        }
        case constant.quizParamsHMSET.cutOffMarks.key:{
            return parseInt(value) !== NaN && value < 100000 && value >=0;
        }
        case constant.quizParamsHMSET.tabSwitchAlertLimit.key:{
            return parseInt(value) !== NaN && value >= 0 && value < 100000;
        }
        default: {
            return true;
        }
    }
}

/**
 * 
 * @param {string} questionId 
 * @param {Array<unknown>} quizSegment 
 * @param {Array<unknown>} quizContent
 * @param {{ poolQuestion: boolean } | undefined} config
 * @returns 
 */
const validateQuizSegment = (questionId, quizSegment, quizContent, config) => {
    const questionSet = new Set();
    questionId.forEach((element) => {
        questionSet.add(element?.toString() ?? element);
    })
    let duplicate = 0;
    let currentQuizContentIndex = 0;
    const quizContentResult = [];
    quizSegment.forEach((element) => {
        let validCount = 0;
        const count = element?.count ?? 0;
        if (!count) {
            return ;
        }
        for(let i=0; i<count; ++i) {
            const quizContentObj = quizContent[currentQuizContentIndex];
            const id = quizContentObj.id?.toString() ?? quizContentObj.id;
            if (questionSet.has(id)) {
                questionSet.delete(id);
                validCount++;
                quizContentResult.push(quizContentObj);
            } else {
                duplicate++;
            }
            ++currentQuizContentIndex;
        }
        element.count = validCount;
        if (config && config?.poolQuestion) {
            if ( !element.pollNumber ) {
                element.pollNumber = element.count ? element.count : 0;
            }        
        }
    })
    return [quizSegment, quizContentResult, (duplicate)?`Duplicate Question ${duplicate}`:undefined];
}

const validateQuizData = function (quizData, isUpdate){
    let toCheck = []
    if(isUpdate){
        if(quizData.title !== undefined){
            toCheck.push(constant.quizParamsHMSET.title.key);
        }
        if(quizData.quizTime !== undefined){
            toCheck.push(constant.quizParamsHMSET.quizTime.key);
        }
        if(quizData.cutOffMarks !== undefined){
            toCheck.push(constant.quizParamsHMSET.cutOffMarks.key);
        }
        if(quizData.tabSwitchAlertLimit !== undefined) {
            toCheck.push(constant.quizParamsHMSET.tabSwitchAlertLimit.key);
        }
    }else{
        toCheck = [
            constant.quizParamsHMSET.title.key,
            constant.quizParamsHMSET.quizTime.key,
            constant.quizParamsHMSET.cutOffMarks.key,
            constant.quizParamsHMSET.tabSwitchAlertLimit.key
        ]
    }

    
    for(let i=0;i<toCheck.length;++i){
        if(!quizValidationFunction(toCheck[i],quizData[constant.quizParamsHMSET[toCheck[i]].key])){
            throw new Error('Invalid value for: ' +toCheck[i]);
        }
    }

    //User Details Check
    for(let i=0; i < quizData.quizUserDetails?.length ?? 0 ; ++i){
        const element = quizData.quizUserDetails[i];
        if(element.fieldIsSelected != 1 ) continue;
        switch (element.fieldType) {
            case constant.QuizUserDetailsInputType.TextInput:{
                break;
            }
            case constant.QuizUserDetailsInputType.Radio:
            case constant.QuizUserDetailsInputType.Select:
            case constant.QuizUserDetailsInputType.Checkbox: {
                if(!element.fieldOptions || element.fieldOptions.length === 0){
                    throw new Error ('Invalid fiedOptions for '+ element.fieldLabel);
                }
                for(let j=0;j<element.fieldOptions.length;++j){
                    if(!element.fieldOptions[j]){
                        throw new Error ('Invalid fiedOptions for '+ element.fieldLabel);
                    }
                }
                break;
            }
            default :{
                throw new Error('Invalid FileInputType.');
            }
        }   
    }

    return quizData;
}

const questionValidationFunction = (key, value) => {
    if(value === undefined || value === null){
        return false;
    }
    switch(key){
        case constant.questionKeys.title :{
            return regex.questionTitleRegex.test(value) && !regex.emojiDetector.test(value);
        }
        case constant.questionKeys.text :{
            return regex.questionDescription.test(value);
        }
        case constant.questionKeys.score :{
            if (typeof value === 'string') {
                value = parseInt(value);
            }
            return value !== NaN && value >= 0 && value < 10000;
        }
        case constant.questionKeys.tags :{
            if (!value) return true;
	        return regex.tagsRegex.test(value) && !regex.emojiDetector.test(value);
        }
        case constant.questionKeys.mcqCorrentAns :{
            if(!value.length){
                return false;
            }
            return true;
        }
        case constant.questionKeys.courseOutcomes: {
            return regex.courseOutcomeRegex.test(value);
        }
        case constant.questionKeys.topic: {
            return regex.topicRegex.test(value);
        }
        default:{
            return true;
        }
    }
}

const getObjectValue = (object, key) => {
    return key.split('.').reduce((obj, current) =>  obj &&  obj[current] ,object);
}

const validateQuestionData = function (questionData){
    // Basic Checks
    let toCheck = [];
    toCheck = [
        constant.questionKeys.title,
        constant.questionKeys.text,
        constant.questionKeys.score,
        constant.questionKeys.tags,
    ]
    if (questionData.courseOutcomes) {
        toCheck.push(constant.questionKeys.courseOutcomes);
    }
    if (questionData.topic) {
        toCheck.push(constant.questionKeys.topic);
    }
    //Question Type Specific Checks
    if(questionData.type === constant.questionTypeNumeric.questionTypeMCQ){
        toCheck.push(constant.questionKeys.mcqCorrentAns);
        let totalScore = 0;
    }
    if(questionData.type === constant.questionTypeNumeric.questionTypeWeb){
        if( !(questionData.questionTypeWeb.isCssAllowed || questionData.questionTypeWeb.isHtmlAllowed || questionData.questionTypeWeb.isJsAllowed || questionData.questionTypeWeb.isReactQuestion) ){
            throw new Error('Atleast one language required in web question.');
        }
        let totalScore = 0;
        (questionData.questionTypeWeb?.testCase ?? []).forEach((element) => {
            totalScore += parseInt(element.scoreip);
            if(!element.description || !element.evaluator || typeof totalScore !== 'number' || totalScore > 9999){
                throw new Error('Invalid testcase.');
            }
        })
    }

    for (let i=0;i<toCheck.length ; ++i){
        if(!questionValidationFunction(toCheck[i], getObjectValue(questionData,toCheck[i]) )){
            throw new Error('Invalid value for: ' +toCheck[i]);
        }
    }
    return questionData;
}

module.exports = {
    isValidQuizUserDetails,
    validateQuizData,
    validateQuestionData,
    validateQuizSegment,
}
