var express = require('express');
var router = express.Router();
var ejs = require('ejs');
var path = require('path');
//-- Load the auth variables
var configAuth = require('../config/auth');
const util = require('../Lib/Util');
const logger = console
const constants = require("../Lib/constants");

const compileServerMasterErrorMail = function( obj ) {
    let mailTo = "<EMAIL>";
    let subject = "URGENT - Master Compile Server Error, Server Ip = " + obj.server;
    let text = "Server Ip = " + obj.server;
    util.sendEmail(mailTo, subject, text, (error, info) => {
        if (error) {
            logger.debug("error in sending mail to compileServerMasterErrorMail---", error);
        } else {
            logger.debug(`Message sent: ${info.response}`);
        }
    });
}

module.exports = {
    compileServerMasterErrorMail,
};
