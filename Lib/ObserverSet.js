class ObserverSet extends Set{
    #listenserFunction;
    /**
     * 
     * @param {((set: ObserverSet) => void)[] | (set: ObserverSet) => void} listner 
     */
    constructor(listner) {
        super();
        this.#listenserFunction = [];
        if (Array.isArray(listner)) {
            listner.forEach((func) => {
                if (typeof func === 'function') {
                    this.#listenserFunction.push(func);
                }
            })
        }
        if (typeof listner === 'function') {
            this.#listenserFunction.push(listner);
        }
    }


    #addWithoutTrigeringListner(value) {
        super.add(value);
    }

    #invokeListner(){
        this.#listenserFunction.forEach((func) => func(this));
    }

    /**
     * 
     * @param {Function} listner 
     */
    
    addListener(listner) {
        this.#listenserFunction.push(listner);
    }

    /**
     * 
     * @param {Function} listner 
     */
    removeListener(listner) {
        this.#listenserFunction = this.#listenserFunction.filter(func => func != listner);
    }

    add(value) {
        this.#invokeListner();
        super.add(value);
    }

    /**
     * 
     * @param {any[]} arrayOfValues 
     */
    addMultiple(arrayOfValues) {
        if (arrayOfValues.length) {
            arrayOfValues.forEach((value) => this.#addWithoutTrigeringListner(value));
            this.#invokeListner();
        }
    }

    delete(value) {
      if(super.has(value)) {
        this.#invokeListner();
        super.delete(value);
      }
    }
}


module.exports = ObserverSet;