var moment = require('moment');
var mtz = require('moment-timezone');

//mtz.tz.setDefault("Asia/Kolkata");

module.exports = {
    dateFormat : function(date) {
        return moment(date).tz("Asia/Kolkata").format('DD-MMM-YYYY, h:mm:ss A (z)'); //22-May-2017, 12:56:37 PM IST
    },
    dateFormatBatch : function(date) {
        return moment(date).tz("Asia/Kolkata").format('DD-MM-YYYY, h:mm:ss A'); //22-May-2017, 12:56:37 PM IST
    },
    dateTimeFormat : function(date) {
        //return moment(date).format('MMMM Do YYYY, h:mm:ss a'); //March 14th 2017, 5:40:58 pm
        //return moment(date).format('Do MMMM YYYY, h:mm:ss a'); //4th May 2017, 10:59:29 am
        //return moment(date).format('DD/MM/YYYY, h:mm:ss a z'); //04/05/2017, 10:59:29 am PST
        return moment(date).tz("Asia/Kolkata").format('DD-MMM-YYYY, h:mm:ss A'); //04-May-2017, 10:59:29 AM
    },
    datePattern : function(date) {
        return moment(date).tz("Asia/Kolkata").format('DD-MM-YYYY'); //04-05-2017
    },
    datePatternYYYYMMDD : function(date) {
        return moment(date).tz("Asia/Kolkata").format('YYYY-MM-DD'); //04-05-2017
    },
    dateOnly : function(date) {
        return moment(date).tz("Asia/Kolkata").format('Do MMMM, YYYY'); //4th May, 2017
    },
    timeOnly : function(date) {
        return moment(date).tz("Asia/Kolkata").format('h:mm A (z)'); //10:59 AM (IST)
    },
    fromNow : function(date) {
        return moment(date).fromNow(); //Time elapsed since date
    },
    datePatternYMDHM : function(date) {
        return moment(date).tz("Asia/Kolkata").format('YYYY-MM-DDTHH:mm'); //04-05-2017
    },
    datePatternYYYYMMDD_HHMMSS : function(date) {
        return moment(date).format('YYYY-MM-DD HH:mm:ss.SSS'); //04-05-2017
    }
};
