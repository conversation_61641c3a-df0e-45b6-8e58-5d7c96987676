const dayjs = require('dayjs');

const messages = {
    'quizUpdation': {
        'isFrozen': 'Changes are not allowed in this quiz',
    },
    'quiz_dashboard':{
        time_over:'Time is over',
        hurry_up:'Hurry Up! Only 5 minutes are left to complete your test.',
        submit_quiz_message:'Are you sure you want to submit the test?',
        section_submit:'You will not be able to access this section again.\n Do you really want to submit this section?',
    },
    'common':{
        library_not_load:'Some libraries are not connected due to poor internet connection',
    },
    'quizAuthentication' : {
        quizCodeNull : "Test code is not present",
        quizNameNull : "Test name not present",
        wrongTestCode : "Entered test code is not correct",
        emailNotAllowed : "Email is not allowed",
        loginDetailError : "Error while checking login details",
        adminNotAllowed : "You do not have permission to attempt this test.",
        wrongQuizName : "Test name is not valid",
        privateQuiz: "Test is private. Not allowed to attempt.",
        updateQuizIssue: "Test has been updated, Please reload and try again.",
        orgIdNull: "Cannot find the creator of the quiz.",
    },
    'startQuiz' : {
        inviteLinkExpiredOfInvalid: "Invite link invalid or expired",
        anotherSessionPresent: "Not allowed to attempt test.Please logout your current session first or attempt in incognito mode",
        invalidInviteToken: "Invite token is not valid",
        alreadyAttempted : "Test already submitted",
        earlyAttempt: "Trying to attempt earlier than start time",
        linkExpired : "Test link has expired",
        invitesFinished: "Invites finished, Contact test support",
        sessionTimeNotPresent: "Get extra time to attempt again",
        extraTimeExpiryReached: "Extra time expired",
    },
    'loginForQuiz' : {
        quizNotPresent : "Quiz not present",
        ipNotAllowed: "IP not allowed"
    },
    'registerForQuiz' : {
        registerError : "Error while registering for quiz.",
        alreadyRegistered : "User with current email address already exists.",
        signUpCountFinished : "Credits finished, Contact test support",
        rollNumberAlreadyInUse: "User with current Roll number already exists.",
    },
    'quizName' : {
        inUse : 'Test link is already in use.',
        notFound: 'Quiz name not found.'
    },
    'errorMessage' : {
        'concurrentUserCountBreached': 'Concurrent User Count Of The Organization Has Been Breached.',
        'dailyLimitOfEmail': 'Daily Limit Of Email is breached.',
        'concurrentEmailLimit': 'Concurrent Email Limit Reached.',
        'notAuthorized': 'Not authorized to perform this action',
        'quizNotFound': 'Quiz does not exists',
        'otpWait': 'Please wait 60 seconds before sending otp',
    },
    'successMessage': {
        'otpsent': 'Otp sent successfully, Please check your mail.',
    },
}

messages.invitePageError = {
    [messages.startQuiz.anotherSessionPresent]: (quizObj, error) => {
        return {
            message: error?.message ?? error,
            status: 401,
        }
    },
    [messages.startQuiz.invalidInviteToken]: (quizObj, error) => {
        return {
            message: error?.message ?? error,
            status: 400,
        }
    },
    [messages.startQuiz.alreadyAttempted]: (quizObj, error) => {
        console.log(quizObj);
        return {
            message: error?.message ?? error,
            status: 409,
        }
    },
    [messages.startQuiz.earlyAttempt]: (quizObj, error) => {
        return {
            message: `Test is not started yet. Test will start at ${dayjs(quizObj.startTime).add(5, 'h').add(30, 'm').format('hh:mm:ss a DD MMM YYYY')}`,
            status: 409,
        }
    },
    [messages.startQuiz.linkExpired]: (quizObj, error) => {
        return {
            message: error?.message ?? error,
            status: 409,
        }
    },
    [messages.startQuiz.extraTimeExpiryReached]: (quizObj, error) => {
        return {
            message: error?.message ?? error,
            status: 400,
        }
    },
    [messages.loginForQuiz.ipNotAllowed]: (quizObj, error) => {
        console.log(quizObj);
        return {
            message: error?.message ?? error,
            status: 403,
        }
    },
    [messages.startQuiz.inviteLinkExpiredOfInvalid]: (quizObj, error) => {
        console.log(quizObj);
        return {
            message: error?.message ?? error,
            status: 400,
        }
    }
}

module.exports = Object.freeze(messages);
