const fs = require('fs')
const path = require('path');

const defaultConsole = console;

class Logger {
    #functionToDisable
    #loggerFilePath

    /**
     * 
     * @param {{loggerFilePath?: string}} config 
     */
    constructor(config) {
        this.#functionToDisable = []
        this.#loggerFilePath = config.loggerFilePath
        
        if (this.#loggerFilePath) {
            fs.promises.readFile(this.#loggerFilePath).then((data) => {
                data = JSON.parse(data.toString())
                if (Array.isArray(data)) {
                    this.#functionToDisable = data;
                }
            }).catch((error) => {
                defaultConsole.error(error);
            })

            fs.watchFile(this.#loggerFilePath, this.#updateLoggerFileHandler.bind(this))
        }
    }
    get console() {
        return new Proxy(defaultConsole, {
            get: (target, p, receiver) => {
                if (this.#functionToDisable.includes(p)) {
                    return () => {}
                }
                return Reflect.get(target, p, receiver)
            }
        })
    }    
    /**
     * 
     * @param {fs.Stats} state 
     */
    async #updateLoggerFileHandler(state){
        try {
            let data = await fs.promises.readFile(this.#loggerFilePath);
            data = JSON.parse(data.toString())
            if (Array.isArray(data)) {
                this.#functionToDisable = data
            }
        } catch (error) {
            this.console.error(error);
        }
    }
}
module.exports = Logger