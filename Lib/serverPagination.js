'use strict';
const constants = require("../Lib/constants");
const utils = require("../Lib/Util");
const defaultColumns = require("./defaultColumns");
const { tableCloumns } = require("./defaultColumns");
RegExp.prototype.toJSON = RegExp.prototype.toString;

var async = require('async'),
    /**
     * Method getSearchableFields
     * Returns an array of fieldNames based on DataTable params object
     * All columns in params.columns that have .searchable == true field will have the .data param returned in an String
     * array. The .data property is used because in angular frontend DTColumnBuilder.newColumn('str') puts 'str' in the
     * data field, instead of the name field.
     * @param params
     * @returns {Array}
     */
    getSearchableFields = function (params) {
        if( params.quizList){
            return tableCloumns.test.searchableColumns
        } else {
            return tableCloumns.questions.searchableColumns

        }
        return params.columns.filter(function (column) {
            return JSON.parse(column.searchable);
        }).map(function (column) {
            return column;//.data;
        });
    },

    /**
     * Method isNaNorUndefined
     * Checks if any of the passed params is NaN or undefined.
     * Used to check DataTable's properties draw, start and length
     * @returns {boolean}
     */
    isNaNorUndefined = function () {
        var args = Array.prototype.slice.call(arguments);
        return args.some(function (arg) {
            return isNaN(arg) || (!arg && arg !== 0);
        });
    },

    /**
     * Methdd buildFindParameters
     * Builds a MongoDB find expression based on DataTables param object
     * - If no search text if provided (in params.search.value) an empty object is returned, meaning all data in DB will
     * be returned.
     * - If only one column is searchable (that means, only one params.columns[i].searchable equals true) a normal one
     * field regex MongoDB query is returned, that is {`fieldName`: new Regex(params.search.value, 'i'}
     * - If multiple columns are searchable, an $or MongoDB is returned, that is:
     * ```
     * {
     *     $or: [
     *         {`searchableField1`: new Regex(params.search.value, 'i')},
     *         {`searchableField2`: new Regex(params.search.value, 'i')}
     *     ]
     * }
     * ```
     * and so on.<br>
     * All search are by regex so the field param.search.regex is ignored.
     * @param params DataTable params object
     * @returns {*}
     */
    buildFindParameters = function (params, Model ) {

        if ( 0 && !params.quizList && ( !params || !params.columns || !params.search || (!params.search.value && params.search.value !== '') ) ) {
            return null;
        }

        var searchText = params?.search?.value,
            findParameters = {},
            searchRegex,
            searchOrArray = [],
            filterAndArray = [];

        searchRegex = new RegExp(utils.escapeStringForRegExp(searchText), 'i');
        if(params.userlist || params.batchId) {
            if(params.adminId){ //  user listing acc to the user space(created by admin), .
                filterAndArray.push({'status': {'$ne': constants.displayStatus.deleted} , 'createdBy' : params.adminId});
            }else {
                filterAndArray.push({'status': {'$ne': constants.displayStatus.deleted}});
            }
        }
        else if(params.feedback){}
        else filterAndArray.push({ 'displaystatus' : 1 } );

        if (Model.modelName == constants.modelString.QuizModelString) {
            if (params.quizIds) {
                filterAndArray.push({'_id': {$in: params.quizIds}});
            }
        }

        if (Model.modelName == constants.modelString.QuestionModelString || Model.modelName == constants.modelString.QuizModelString) {

            if (params.myList) {
                filterAndArray.push({'createdBy': params.userId});
            }

            if (params.superOrgList) {
                filterAndArray.push({'orgId': params.orgIdsArray})
            }

            if (params.myOrgList) {
                filterAndArray.push({'orgId': params.orgIdsArray ?? params.orgId, createdBy: {'$ne': params.userId}});
                if (params.fltrCreatedBy && params.fltrCreatedBy?.length )    filterAndArray.push({'createdBy': {$in : params.fltrCreatedBy}});
            }
            if (params.cqList) {
                filterAndArray.push({'orgId': global.superAdminId, 'isPublic': true});
                if (params.fltrCreatedBy && params.fltrCreatedBy.length )    filterAndArray.push({'createdBy': {$in : params.fltrCreatedBy}});
            }
            
            if (params.isCq && params.otherList) {
                filterAndArray.push({'orgId': {'$ne': params.orgId || global.superAdminId}});
            }

            if (!params.myList) {
                if (params.fltrCreatedBy && params.fltrCreatedBy.length){
                    if( params.isCq && params.otherList && params.role == constants.roleNumberFromString.admin){
                        filterAndArray.push({'orgId': {$in: params.fltrCreatedBy }} );
                    }
                    else filterAndArray.push({'createdBy': {$in: params.fltrCreatedBy}});
                }   
                    
                //if (Model.modelName != constants.modelString.QuizModelString && !(params.role === constants.roleNumberFromString.admin)) filterAndArray.push({'isPublic': true});
            }
        }
        if (Model.modelName == constants.modelString.QuestionModelString) {
            if (params.role == constants.roleNumberFromString.admin && Boolean(params.isPremium))   filterAndArray.push({ 'isPremium': true });
            if (params.isPublic === true) {
                filterAndArray.push({'isPublic': true});
            }
            if (params.isPublic === false) {
                filterAndArray.push({'isPublic': false});
            }
            if (params.languagesAllowed && ! params.quizId){
                filterAndArray.push( { 
                    $or: [{
                        'type': {
                            $in: [
                                constants.questionTypeNumeric.questionTypeMCQ,
                                constants.questionTypeNumeric.questionTypeSubjective, 
                                constants.questionTypeNumeric.questionTypeStepwise,
                                constants.questionTypeNumeric.questionTypeWeb,
                            ]
                        }
                    },
                    { 'questionTypeCoding.codeproglang.language': { $in: params.languagesAllowed } }]
                })
            }

            if (params.questionType ) {
                filterAndArray.push({ type : params.questionType });
            }

        }
        else if (Model.modelName == constants.modelString.QuizModelString) {
            if(params.isTemplate)   filterAndArray.push({'isTemplate': true});
            else                    filterAndArray.push({$or: [{'isTemplate': false}, {'isTemplate': {$exists: false} }]});

            if (params.isTemplate) {
                if (params.isPublic === true) {
                    filterAndArray.push({'isPublic': true});
                }

                if (params.isPublic === false) {
                    filterAndArray.push({'isPublic': false});
                }
            }

            if(params.archaic)      filterAndArray.push({'archaic': true});
            else filterAndArray.push({ $or : [ {'archaic': {$exists: true, $eq : false } } , {'archaic': {$exists: false} } ] });

            if( params.dateRange && Array.isArray(params.dateRange) && params.dateRange.length  ){
                const[ start, end ] = params.dateRange;
                if( start && utils.isValidDate( new Date(start))){
                    filterAndArray.push( { startTime : { $gte : start} })
                }
                if( end && utils.isValidDate( new Date(end))){
                    filterAndArray.push( { startTime : { $lte : end} })
                }
            }
        }

        var searchableFields = getSearchableFields(params);

        // if (searchableFields.length === 1) {
        //     findParameters[searchableFields[0].data] = searchRegex;
        //     return findParameters;
        // }
        searchableFields.forEach(function (field) {
            var orCondition = {};
            var andCondition = {};
            var outerAndCondition = [];
            orCondition[ field.data ] = searchRegex;
            
            //filters
            if ((field?.data == 'keywords' || field?.data == 'tags') && (params.keywords || params.tags)) {
                var arrayTags = Array.isArray(params.keywords) ? params.keywords : (params?.keywords)?.split(',');
                // Clean and prepare tags array
                arrayTags = arrayTags
                    ?.map(tag => tag.trim().toLowerCase())
                    .filter(tag => tag.length > 0);
                
                if (arrayTags?.length > 0) {
                    let obj = {};
                    // Use $all operator to match all tags
                    obj[field.data] = {
                        $all: arrayTags.map(tag => new RegExp(utils.escapeStringForRegExp(tag), 'i'))
                    };
                    filterAndArray.push(obj);
                }
            } else {
                searchOrArray.push(orCondition);
            }
        });
        
        if (params.quizId) {
            //add tutorial or question in course
            if (filterAndArray.length > 0) {
                findParameters.$and = [{ $and: filterAndArray },
                { $or: searchOrArray }
                ]
            } else {
                findParameters.$and = [{ $or: searchOrArray }];
            }

            if(params.contentIdList)
            findParameters.$and.push({ '_id': { $nin: ((params.contentIdList)) } });

            if (Model.modelName === constants.modelString.QuestionModelString) {
                findParameters.$and.push({
                    $or: [{
                        'type': {
                            $in: [constants.questionTypeNumeric.questionTypeMCQ, constants.questionTypeNumeric.questionTypeWeb,
                            constants.questionTypeNumeric.questionTypeSubjective, constants.questionTypeNumeric.questionTypeStepwise]
                        }
                    },
                    { 'questionTypeCoding.codeproglang.language': { $in: params.languagesAllowed } }]
                })
            }
        }
        else{
             //list without selection for other purpose
            if (filterAndArray.length > 0) {
                findParameters.$and = [{$and: filterAndArray},
                    {$or: searchOrArray}
                ]
            }
            else
                findParameters.$and = [{$or: searchOrArray}]
        }
        if (Model.modelName == constants.modelString.QuizModelString) {
            if (params.orConditionQuizIds) {
                searchOrArray.push({'_id': {$in: params.orConditionQuizIds}});
            }

        }

        return findParameters;
    },

    /**
     * Method buildSortParameters
     * Based on DataTable parameters, this method returns a MongoDB ordering parameter for the appropriate field
     * The params object must contain the following properties:
     * order: Array containing a single object
     * order[0].column: A string parseable to an Integer, that references the column index of the reference field
     * order[0].dir: A string that can be either 'asc' for ascending order or 'desc' for descending order
     * columns: Array of column's description object
     * columns[i].data: The name of the field in MongoDB. If the index i is equal to order[0].column, and
     * the column is orderable, then this will be the returned search param
     * columns[i].orderable: A string (either 'true' or 'false') that denotes if the given column is orderable
     * @param params
     * @returns {*}
     */
    buildSortParameters = function (params) {
        if( 1 ){
            if( params.criteria ) return -1;

            if( params.sorting && params.sorting.column && params.sorting.order){
                return { [ params.sorting.column ] : params.sorting.order == 'asc' ? 1 : -1, _id : 1 };
            }
            return { 'updatedAt' : -1, _id : 1 };
        }
        if (!params || !Array.isArray(params.order) || params.order.length === 0) {
            return null;
        }

        var sortColumn = Number(params.order[0].column),
            sortOrder = params.order[0].dir,
            sortField;

        if (isNaNorUndefined(sortColumn) || !Array.isArray(params.columns) || sortColumn >= params.columns.length) {
            return null;
        }

        if (params.columns[sortColumn].orderable === 'false') {
            return null;
        }

        sortField = params.columns[sortColumn].data;

        if (!sortField) {
            return null;
        }

        if (sortOrder === 'asc') {
            if (params.criteria)return 1;
            return {[sortField]: 1, _id: 1};
        }
        if (params.criteria)return -1;
        return {[sortField]: -1, _id: 1};
    },

    buildSelectParameters = function (params) {

        const columns = params.columns ? params
            .columns
            .map(col => col.data)
            .reduce((selectParams, field) => {
                selectParams[field] = 1;
                return selectParams;
            }, {}) : ( params.quizList ? tableCloumns.test.columns : tableCloumns.questions.columns );
        return columns;
    },

    /**
     * Run wrapper function
     * Serves only to the Model parameter in the wrapped run function's scope
     * @param {Object} Model Mongoose Model Object, target of the search
     * @returns {Function} the actual run function with Model in its scope
     */
    run = function (Model) {

        /**
         * Method Run
         * The actual run function
         * Performs the query on the passed Model object, using the DataTable params argument
         * @param {Object} params DataTable params object
         */
        return function (params) {

            var draw = Number(params.draw),
                start = Number(params.start),
                length = Number(params.length),

                sortParameters = buildSortParameters(params),
                selectParameters = buildSelectParameters(params),
                recordsTotal,
                recordsFiltered;
            if (params.criteria)
                var findParameters = params.criteria;
            else {
                var findParameters = buildFindParameters(params, Model);
            }


            return new Promise(function (fullfill, reject) {

                async.series([
                    function checkParams(cb) {
                        if (isNaNorUndefined(start, length)) {
                            return cb(new Error('Some parameters are missing or in a wrong state. ' +
                                'Could be any of draw, start or length'));
                        }

                        if (!findParameters || !sortParameters || !selectParameters) {
                            return cb(new Error('Invalid findParameters or sortParameters or selectParameters'));
                        }
                        cb();
                    },
                    function fetchRecordsTotal(cb) {
			    recordsTotal = 0;
			                            cb();
			                            return ;
                        let crit;
                        if (params.criteria) {
                            crit = { 'userId' : params.criteria.userId, courseId: params.criteria.courseId , questionId: {$exists: true}};
                        } else if(params.feedback) {
                            crit = {};
                        } else {
                            if(params.batchId || params.userlist) {
                                if(params.adminId){
                                    crit = {'status': {$ne: constants.displayStatus.deleted}, role: {$ne: constants.roleNumberFromString.admin } , 'createdBy' : params.adminId};
                                }else {
                                    crit = {'status': {$ne: constants.displayStatus.deleted}, role: {$ne: constants.roleNumberFromString.admin }};
                                }
                            }else if(params.courseListingAccToUserSpace){
                                if(params.userType && params.userType == constants.roleNumberFromString.admin || params.userType == constants.roleNumberFromString.contentCreator ){
                                    crit = { 'displaystatus' : { $ne :  constants.displayStatus.deleted}  };
                                    //crit = { 'displaystatus' : { $ne :  constants.displayStatus.deleted} , 'isCloned' : {$exists: false} };
                                }else if(params.userType && params.userType == constants.roleNumberFromString.subAdmin ){
                                    crit = { $and : [{'displaystatus' : { $ne :  constants.displayStatus.deleted}} ,
                                        { $or : [ {'_id' : { $in : params.myCourses}},{'createdBy': params.userId}] } ,
                                        { $or : [ {'createdBy': params.userId} ,{'isActive': {$exists: true, $eq : true } } , {'isActive': {$exists: false} } ] }
                                        ] } ;
                                }
                            }else if(params.role &&  params.role== constants.roleNumberFromString.subAdmin  ){
                                //var findAccessableRecords = [];
                                crit = {'displaystatus' : { $ne :  constants.displayStatus.deleted}};

                                if(params.myQuestions){
                                    if(params.fltrCreatedBy)    crit['createdBy'] = {$in : params.fltrCreatedBy};
                                    else                        crit['$or'] = [{'createdBy':params.parentIdOfCreator}, {'parentIdOfCreator' : params.parentIdOfCreator}];
                                } else if (params.allQuestions) {
                                    // No Conditions, views all questions
                                }else{
                                    crit['isPublic'] = {$exists: true, $eq : true };
                                    crit['isCqDocument'] = {$exists: true, $eq : true };
                                    if (!params.isPremiumUser)   crit['isPremium'] = {$ne: true};
                                }
                            }else  if(params.role && ( params.role== constants.roleNumberFromString.mentor
                                || params.role== constants.roleNumberFromString.recruiter)){
                                if(params.quizList){
                                    crit =   { $and : [{'displaystatus' : { $ne :  constants.displayStatus.deleted}} ,
                                        {'createdBy' : params.parentIdOfCreator} ]} ;

                                }else{//question list
                                    crit = {'displaystatus' : { $ne :  constants.displayStatus.deleted}};
                                    if(params.myQuestions){
                                        if(params.fltrCreatedBy){
                                            if(params.fltrCreatedByMe){
                                                crit['$or'] = [ {'createdBy':params.userId} ,
                                                        {$and : [ {'createdBy': {$in : params.fltrCreatedBy}} ,
                                                    { 'isPublic': {$exists: true, $eq : true } }  ]}] // If mentor also in list of selected users then fetch all the public questions of selected users in org. and their own questions .
                                            }else{
                                                crit['createdBy'] = {$in : params.fltrCreatedBy};
                                                crit['isPublic'] = {$exists: true, $eq : true };
                                            }
                                        }else{
                                            crit['$or'] = [{'createdBy':params.userId}  , 
                                                { $and : [{'createdBy':params.parentIdOfCreator}, {'isPublic': {$exists: true, $eq : true } } ] } ,
                                                    { $and : [{'parentIdOfCreator' : params.parentIdOfCreator} , {'isPublic': {$exists: true, $eq : true } } ]}
                                                ];
                                        }
                                    } else if (params.allQuestions) {
                                        // No Conditions, views all questions
                                    }else{
                                        crit['isPublic'] = {$exists: true, $eq : true};
                                        crit['isCqDocument'] = {$exists: true, $eq : true};
                                        if (!params.isPremiumUser)   crit['isPremium'] = {$ne: true};
                                    }
                                }
                            }
                            else crit = { 'displaystatus' : { $ne :  constants.displayStatus.deleted} };
                        }
                        if ( Model.modelName == constants.modelForPagination.User ) {
                            utils.userQuery('count', crit, {}, { collation: {'locale':'en'} }, (err, count) => {
                                if (err) {
                                    console.log("Err total = ", err);
                                    return cb(err);
                                }
                                //console.log("Total = ", count);
                                recordsTotal = count;
                                cb();
                            })
                        }
                        else {
                            Model.count(crit, function (err, count) {
                                if (err) {
                                    return cb(err);
                                }
                                recordsTotal = count;
                                cb();
                            });
                        }
                    },
                    function fetchRecordsFiltered(cb) {
                        if ( Model.modelName == constants.modelForPagination.User ) {
                            utils.userQuery('count', JSON.stringify(findParameters), {}, {}, (err, count) => {
                                if (err) {
                                    console.log("Err filtered = ", err);
                                    return cb(err);
                                }
                                //console.log("Filtered = ", count);
                                recordsFiltered = count;
                                cb();
                            })
                        }
                        else {
                            Model.count(findParameters, function (err, count) {
                                if (err) {
                                    return cb(err);
                                }
                                recordsFiltered = count;
                                cb();
                            });
                        } 
                    },
                    function runQuery(cb) {
                        let query;
                        if (params.criteria) {
                            // DEPRECIATED
                            console.log("Hey fool. how did you enter wakanda");
                            query = Model.aggregate( [{$match: findParameters},
                                //{$project: selectParameters},
                                {$sort: {'lastActive': sortParameters}},
                                {$skip: start},
                                {$limit: length},
                                { $lookup : {
                                    'from' : 'questions',
                                    'localField' : 'questionId',
                                    'foreignField' : '_id',
                                    'as' : 'questionId'} },
                                { $lookup : {
                                    'from' : 'courses',
                                    'localField' : 'courseId',
                                    'foreignField' : '_id',
                                    'as' : 'courseId'} },
                                { $lookup : {
                                    'from' : params.isArchived === "true" ? 'userattempt_archives' : 'userattempts',
                                    'localField' : '_id',
                                    'foreignField' :  'userAnswerId',
                                    'as' : 'userattempts'} } ,
                                {$unwind : {path :"$userattempts" , preserveNullAndEmptyArrays: true}},
                                { $project: { 'userattempts.attemptData.userInputMCQ':1 ,'userattempts.attemptData.userOutputCoding':1 ,'userattempts.attemptData.userCompilationError':1 ,'courseId._id':1 ,'courseId.title' : 1 ,'questionId._id': 1 ,'questionId.questionTypeCoding' : 1 ,'questionId.questionTypeMCQ' : 1 ,  'questionId.title':1 , 'questionId.type' : 1 , 'lastActive' : 1} } ,

                            ], { "allowDiskUse" : true } )
                            query.exec(function (err, results) {
                                if (err) {
                                    return cb(err);
                                }
                                cb(null, {
                                    draw: draw,
                                    recordsTotal: recordsTotal,
                                    recordsFiltered: recordsFiltered,
                                    data: results
                                });
                            });
                        } else {
                            selectParameters.status = 1;
                            selectParameters.createdBy = 1;
                            selectParameters.parentIdOfCreator = 1;
                            selectParameters.isPublic = 1 ;
                            selectParameters.isActive = 1;
                            selectParameters.archaic = 1;
                            if ( Model.modelName == constants.modelForPagination.User ) {
                                //{ sort:{lastActive:"desc"} ,skip: start , limit: length }
                                let opt = {
                                    lean: true,
                                    sort: sortParameters,
                                    limit: length,
                                    skip: start,
                                    collation: {'locale':'en'},
				                    "allowDiskUse" : true,
                                }
                                utils.userQuery('getUser', JSON.stringify(findParameters), selectParameters, opt, (err, results) => {
                                    if (err) {
                                        return cb(err);
                                    }
                                    // console.log("Data = ", results );
                                    // console.log("draw = ", draw );
                                    // console.log("recordsTotal = ", recordsTotal );
                                    // console.log("recordsFiltered = ", recordsFiltered );

                                    cb(null, {
                                        draw: draw,
                                        recordsTotal: recordsTotal,
                                        recordsFiltered: recordsFiltered,
                                        data: results
                                    });
                                })
                            }
                            else {
                                query = Model
                                    .find(findParameters)
                                    .select(selectParameters)
                                    .limit(length)
                                    .skip(start)
                                    .sort(sortParameters)    //.populate("createdBy", 'displayname email')
                                    .collation({ locale: "en" })
                                query.lean().exec(function (err, results) {
                                    if (err) {
                                        return cb(err);
                                    }
                                    cb(null, {
                                        draw: draw,
                                        recordsTotal: recordsTotal,
                                        recordsFiltered: recordsFiltered,
                                        data: results
                                    });
                                });
                            }
                        }
                    }
                ], function resolve(err, results) {
                    if (err) {
                        reject({
                            error: err
                        });
                    } else {
                        var answer = results[results.length - 1];
                        fullfill(answer);
                    }
                });
            });
        };
    },

    /**
     * Module datatablesQuery
     * Performs queries in the given Mongoose Model object, following DataTables conventions for search and
     * pagination.
     * The only interesting exported function is `run`. The others are exported only to allow unit testing.
     * @param Model
     * @returns {{run: Function, isNaNorUndefined: Function, buildFindParameters: Function, buildSortParameters:
     *     Function}}
     */
    datatablesQuery = function (Model) {
        return {
            run: run(Model),
            isNaNorUndefined: isNaNorUndefined,
            buildFindParameters: buildFindParameters, // find query
            buildSortParameters: buildSortParameters,
            buildSelectParameters: buildSelectParameters
        };
    };

module.exports = datatablesQuery;
