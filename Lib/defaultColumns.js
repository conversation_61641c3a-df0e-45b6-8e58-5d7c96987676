const columns = {
  test: {
    columns: {
      title: 1,
      keywords: 1,
      quizTime: 1,
      quizCode: 1,
      createdBy: 1,
      startTime: 1,
      endTime: 1,
      updatedAt: 1,
      _id: 1,
      randomizeQuestion: 1,
      poolQuestion: 1,
      isFrozen: 1,
      isMarkedAsCheck: 1,
      totalQuestions: 1,
      isPrivate: 1,
      progressStep: 1,
      orgId:1,
    },
    searchableColumns: [
        { data : 'title' },
        { data : 'keywords' }
    ],
  },
  questions: {
    columns: {
      _id: 1,
      title: 1,
      type: 1,
      score: 1,
      tags: 1,
      isLocked: 1,
      updatedAt: 1,
      createdAt: 1,
      questionTypeCoding : 1,
      questionTypeMCQ: 1,
      questionTypeSubjective : 1,
      questionTypeWeb : 1,
      

    },
    searchableColumns: [
      { data : 'title' },
      { data : 'tags' }
  ],
  },
};
module.exports = {
  tableCloumns: columns,
};
