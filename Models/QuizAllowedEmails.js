var mongoose = require('mongoose');
var libs = require("../Lib");

var quizAllowedEmailsSchema = mongoose.Schema({
        'quizId': {
            'type': mongoose.Schema.ObjectId,
            'require': true,
        },
        'emails': [String],
        'invitees': [{
            'email': String,
            'validTill': Date,
            'isValid': {type: Boolean, default: true},
            'token': String,
        }],
    },
    {
        timestamps: true
    }
);

quizAllowedEmailsSchema.index( { quizId : 1 } );

module.exports = mongoose.model( libs.constants.modelString.QuizAllowedEmails, quizAllowedEmailsSchema);