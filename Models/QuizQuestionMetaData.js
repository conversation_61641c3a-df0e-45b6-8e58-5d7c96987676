const mongoose = require('mongoose');
const libs = require("../Lib");

const constants = libs.constants;

const QuizQuestionMetaData = mongoose.Schema({
        'quizId': { type: mongoose.Schema.Types.ObjectId, ref: constants.modelString.QuizModelString },
        'questionId' : { type: mongoose.Schema.Types.ObjectId, ref: constants.modelString.QuestionModelString },
        'type' : String,
        'compileError' : { type: Number, default: 0},
        'runTimeError' : { type: Number, default: 0},
        'timeLimitExceeded' : { type: Number, default: 0},
        'wrongCount' : { type: Number, default: 0},
        'successCount' : { type: Number, default: 0},
        'totalAttempts' : { type: Number, default: 0},
        'langTotalAttempts' : [{
            'code': String,
            'totalAttempts': Number
        }],
        'successAttemptByUserIds' : [mongoose.Schema.Types.ObjectId],
    },
    { 'timestamps' : true });

const populatePathAndSelectObjArray = [
    {path: 'quizId', select: 'title quizContent startTime endTime displaystatus quizSegments revisitAllowed attemptInSequence languagesAllowed quizTime'},
]

const autoPopulateCourses = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};

QuizQuestionMetaData.index( {'quizId': -1, 'questionId': -1 }, { 'background' : true } );

module.exports = mongoose.model(constants.modelString.QuizQuestionMetaData, QuizQuestionMetaData);