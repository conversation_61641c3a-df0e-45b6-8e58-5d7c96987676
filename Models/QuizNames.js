var mongoose = require('mongoose');
var libs = require('../Lib');

var quizNamesSchema = mongoose.Schema({
    'name' : String,
    'quizId': {'type': mongoose.Schema.Types.ObjectId, 'ref': 'Quiz'},
    'createdBy': {'type': mongoose.Schema.ObjectId, 'ref': 'User'},
},
    { 'timestamps' : true } );

var populatePathAndSelectObjArray = [
    {path: 'quizId', select: 'title quizContent displaystatus quizSegments attemptInSequence quizTime endTime startTime instructions createdBy parentIdOfCreator quizCode orgId'},
];

var autoPopulateQuiz = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};
quizNamesSchema.index( { 'name': 1 },{ 'background' : true } ) ;
quizNamesSchema.pre('findOne', autoPopulateQuiz).pre('find', autoPopulateQuiz);

module.exports = mongoose.model( libs.constants.modelString.quizNamesModelString, quizNamesSchema );