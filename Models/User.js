var mongoose = require('mongoose');
var bcrypt = require('bcrypt-nodejs');
var libs = require("../Lib");
//--
var userSchema = mongoose.Schema({
        'email': {type: String, unique: true},
        'mobile': String, //{ type: String, unique: true },
        'password': String,
        'displayname': String,
        'company': String,
        'website': String,
        'address': String,
        'profilePic': String,
        'role': {type: String, default: '1'}, //-- [ 0-Admin 1-User/Student 2-SuperAdmin ]
        'status': {type: String, default: '1'}, //--  [ 0-Deleted 1-Active 2-Inactive 3-Banned ]
        'resetPasswordToken': String,
        'resetPasswordExpires': Date,
        'courseId': [{'type': mongoose.Schema.Types.ObjectId, 'ref': libs.constants.modelString.CourseModelString}],
        'quizId': [{'type': mongoose.Schema.Types.ObjectId, 'ref': libs.constants.modelString.QuizModelString}],
        'city': String,
        'state': String,
        'college': String,
        'profileLink': String,
        'allowInteractiveMode': {'type': Boolean, 'default': false},
        'canAddTestCase': {'type': Boolean, default: false},
        'batchIdCourseId': [{
            'courseId': {'type': mongoose.Schema.Types.ObjectId, 'ref': libs.constants.modelString.CourseModelString},
            'batchId': [{'type': mongoose.Schema.Types.ObjectId, 'ref': libs.constants.modelString.BatchModelString}]
        }],
        'isProjectAllowed': Boolean,
        'allowedProjects': {'type': Number, 'default': 5},
        'concurrentAllowedProjects': {'type': Number, 'default': 5},
        'projectId': [{'type': mongoose.Schema.Types.ObjectId, 'ref': libs.constants.modelString.ProjectModelString}],
        'inProgressProjectId': [{
            'type': mongoose.Schema.Types.ObjectId,
            'ref': libs.constants.modelString.ProjectModelString
        }],
        'userQuota': {'type': String, 'default': libs.constants.defaultUserQuota},
        'addQuestion': Boolean,
        'addCourse': Boolean,
        'addTutorial': Boolean,
        'addQuiz': Boolean,
        'addBatch': Boolean,
        'addUser': Boolean,
        'addAdminProject': Boolean,
        'learningSpace': {'type': Boolean, default: false},
        'testingSpace': {'type': Boolean, default: false},
        'projectSpace': {'type': Boolean, default: false},
        'createdBy': {'type': mongoose.Schema.ObjectId},
        'updatedBy': {'type': mongoose.Schema.ObjectId},
        'deletedBy': {'type': mongoose.Schema.ObjectId},
        'parentIdOfCreator': {'type': mongoose.Schema.ObjectId},
        'languagesAllowed' : [ String ],
        'invitesLeft' : {'type': Number, 'default': 0},
        'usersLeft' : {'type': Number, 'default': 0},
        'onlyAdminLogin' : Boolean,
        'stream' : String,
        'year' : String,
        'skills' : String,
        'projectLink' : String,
        'information' : String,
        'testToken' : String,
        'testTokenExpires': Date,
        'isExtenalUser' : Boolean,
        'loginAttemptLeft' : {'type': Number, 'default':libs.constants.maxLoginAttemptLeft},
    },
    {
        'timestamps': true
    });
//--
userSchema.index({email: 1}, {'background': true});
userSchema.methods.generateHash = function (password) {
    return bcrypt.hashSync(password, bcrypt.genSaltSync(8), null);
};
//--
userSchema.methods.validPassword = function (password) {
    return bcrypt.compareSync(password, this.password);
};

var autoPopulateBatch = function (next) {
    // this.populate( { path: 'batchIdCourseId.batchId', select: 'title batchContent displaystatus'});
    // this.populate( { path: 'batchIdCourseId.courseId', select: 'title courseContent courseSegments displaystatus'});
    // this.populate( { path: 'courseId', select: 'title courseContent courseSegments'});
    // this.populate( { path: 'quizId', ->select: 'title description quizContent quizSegments revisitAllowed quizTime'});
    next();
};

userSchema.pre('findOne', autoPopulateBatch).pre('find', autoPopulateBatch);

//-- Create and export our model
module.exports = mongoose.model(libs.constants.modelString.UserModelString, userSchema);
