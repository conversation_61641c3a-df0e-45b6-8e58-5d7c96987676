var mongoose = require('mongoose');
var libs = require("../Lib");

var project = mongoose.Schema({
        'title': String,
        'description': String,
        'keywords': String,
        'instructions' : String ,
        'displaystatus': {'type': Number, 'default': libs.constants.displayStatus.active}, //--  [ 0-Deleted 1-Active 2-PendingDeletion ]
        'createdBy': {'type': mongoose.Schema.ObjectId},
        'updatedBy': {'type': mongoose.Schema.ObjectId},
        'deletedBy': {'type': mongoose.Schema.ObjectId},
        'parentIdOfCreator': {'type': mongoose.Schema.ObjectId},
        'startTime' : Date,
        'endTime' : Date,
        'projectTime' : Number,
        'submissionsAllowed' : Number,
        'isPublic' : Boolean,
        'language': String,
        'projectLink' : String,
        'decryptedProjectLink' : String,
    },
    { 'timestamps' : true } );

// var populatePathAndSelectObjArray = [
//     { path : 'userId', select: 'displayname email role' },
// ];
//
// var autoPopulateCourses = function (next) {
//     // libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
//     next();
// };
//
// project.pre('findOne', autoPopulateCourses).pre('find', autoPopulateCourses);
project.index( { 'parentIdOfCreator' : 1 }, { 'background' : true } );
module.exports = mongoose.model(libs.constants.modelString.AdminProjectModelString, project);
