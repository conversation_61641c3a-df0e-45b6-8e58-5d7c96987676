
var mongoose = require('mongoose');
var libs = require("../Lib");
var UserQuizSubmittedSegment = mongoose.Schema({
        'courseId' : {'type': mongoose.Schema.Types.ObjectId, 'ref': 'Course'},
        'quizId': {'type': mongoose.Schema.Types.ObjectId, 'ref': 'Quiz'},
        'userId' : { 'type': mongoose.Schema.Types.ObjectId },
        'quizSubmittedSegments' : [ Number ],
        'submittedQuestions' : [ String ],
        'quizSubmittedQuestion' : [ {
            'questionId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref': 'Question'},
            'score' : { 'type' : Number, 'default' : 0},
            'scoreUpdationActivity': [{
                'originalScore': Number,
                'newScore': Number,
                'updatedBy': mongoose.Schema.Types.ObjectId,
                'updatedAt': Date, }],
            'additionalScore' : { 'type' : Number, 'default' : 0},
            'startTime' : Date,
            'submissions' : [ Date ],
            'hasSubmitted' : { 'type' : Boolean, 'default' : false},
            'userProgram' : String,
            'userLanguage' : String,
            'userInputMCQ' : String,
            'userInputSubjective' : String,
            'userOutputCoding' : [ String ],
        }],
        'displayName' : String,
        'email' : String,
        'startTime' : { type : Date },
        'endTime' : { type : Date },
        'extraTime' : { type : Number, default : 0},
        'idleTime' : { type : Number, default : 0},
        'rollNo' : String,
        'branch' : String,
        'batch' : String,
        'quizName' : String,
        'college' : String,
        'city' : String,
        'state' : String,
        'isAutoSubmit' : { 'type' : Boolean, 'default' : true},
        'tabSwitchCount' : { 'type' : Number, 'default' : 0},
        'tabSwitchInCount' : { 'type' : Number, 'default' : 0},
        'forceLoginCount' : { 'type' : Number, 'default' : 0},
        'codePasteCount' : { 'type' : Number, 'default' : 0},
        'userExitCount': { 'type': Number, 'default': 0},
        'userEnterCount': { 'type': Number, 'default': 0 },
        'multipleUserDetected': { 'type': Number, 'default': 0 },
        'illegalObjectDetected': { 'type': Number, 'default': 0 },
        'fullScreenInCount' : { 'type' : Number, 'default' : 0},
        'fullScreenOutCount' : { 'type' : Number, 'default' : 0},
        'loginCount' : { 'type' : Number, 'default' : 0},
        'camBlockCount' : { 'type' : Number, 'default' : 0},
        'tryTest' : { 'type' : Boolean, 'default' : false},
        'userIp' : [ {'userMachineIp': String, 'userProxyIp': String, 'loginDate': Date} ],
        'currentIp' : String,
        'questionId' : [{ 'type': mongoose.Schema.Types.ObjectId }],
				'quizUserDetails': [{
					fieldLabel: String,
					fieldValue: String
				}],
				'hasQuizStarted': Boolean,
        'explicitSubmission' : Boolean,
        'loginDetails': [{
            'loginTime': Date,
            'ip': String,
            'platform': String,
            'sessionId': String,
        }],
        'terminationDetails': [{
            'terminationTime': Date,
            'sessionId': String,
            'terminationType': Number,
        }],
        'userSessions' : [{
            startTime : Date,
            endTime : Date,
            sessionTime : Number,
            explicitSubmission : Boolean,
            isAutoSubmit : Boolean,
            isForceSubmit: Boolean,
            forceSubmissionType: Number,
            extraTime : Number,
        }],
        'tabSwitchReport': [{
            tabTime: Date,
            tabSwitchType: Number,
        }],
        'fullScreenReport': [{
            time: Date,
            eventType: Number,
        }],
        'aiProctoringReport': [{
            subType: Number,
            time: Date,
            illegalObject: [{type: String}],
            imageUrl: String,
        }],
        'segmentSubmissionHistory': [{
            time: Date,
            segmentIndex: Number,
            submissionType: Number,
            createdBy: mongoose.Schema.ObjectId,
            imageURL: String,
            otp: String,
        }],
    },
    { 'timestamps' : true } );

var populatePathAndSelectObjArray = [
    // {path: 'courseId', select: 'title courseContent displaystatus courseSegments'},
    {path: 'quizId', select: 'title cutOffMarks quizContent startTime endTime displaystatus quizSegments revisitAllowed attemptInSequence languagesAllowed quizTime'},
]

var autoPopulateCourses = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};

UserQuizSubmittedSegment.pre('findOne', autoPopulateCourses).pre('find', autoPopulateCourses);

UserQuizSubmittedSegment.index( {'quizId': -1, 'userId': -1 }, { 'background' : true } );
UserQuizSubmittedSegment.index( {'userId': -1, 'quizId': -1 }, { 'background' : true } );

module.exports = mongoose.model(libs.constants.modelString.UserQuizSubmittedSegment, UserQuizSubmittedSegment);
