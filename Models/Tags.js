var mongoose = require('mongoose');
var libs = require("../Lib");
var tagsSchema = mongoose.Schema({
    tag : String,
    objectType  : String,
    questionId : [{ type: mongoose.Schema.Types.ObjectId, ref: 'Question' }],
    topicId : [{ type: mongoose.Schema.Types.ObjectId, ref: 'Topic' }],
    courseId : [{ type: mongoose.Schema.Types.ObjectId, ref: 'Course' }],
    quizId : [{ type: mongoose.Schema.Types.ObjectId, ref: 'Quiz' }],
    adminId : [{ type: mongoose.Schema.Types.ObjectId }],
    createdon : { type: Date, default: Date.now }
    },
  {
      timestamps : true
  });
tagsSchema.index( { tag: 1 } );
module.exports = mongoose.model(libs.constants.modelString.TagsModelString, tagsSchema);