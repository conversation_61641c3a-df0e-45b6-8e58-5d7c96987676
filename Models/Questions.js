const mongoose = require('mongoose');
const constants = require('../Lib/constants');
var libs = require("../Lib");
var questionSchema = mongoose.Schema({
        'quesString': String, //Not using
        'title': String,
        'text': String,
        'tags': String,
        //'category': [{ type: mongoose.Schema.Types.ObjectId, ref: 'Qcategory' }],//String
        'type': String,
        'score': {type: Number, default: 0},
        'askconf': Boolean,

        'explanation': String, //Explanation for correct Answer
        'hint': String, //Hint For Student
        'showhint': Boolean,
        'displaystatus': {type: Number, default: '1'}, //--  [ 0-Deleted 1-Active 2-PendingDeletion ]
        'importedby': [{type: mongoose.Schema.Types.ObjectId }],
        // 'courseId': [{'type': mongoose.Schema.Types.ObjectId, 'ref': 'Course'}],
        // 'quizId': [{'type': mongoose.Schema.Types.ObjectId, 'ref': 'Quiz'}],
        'questionTypeMCQ': {
            'options': [String],
            'correctAnswers': [Number],
            'answer': String
        },

        'questionTypeCoding': {
            'multipleTestCases' : { 'type' : Boolean , 'default' : false},
            'codeproglang': [{
                'language': String,

                'defaultCompileResult' : { 'output' : String , 'errors' : String },
                'defaultTrimmedCode' : String,
                'codeComponents': {
                    'head': String,
                    'body': String,
                    'tail': String,
                    'solution': String
                }
            }],
            'codeprogcode': String,
            'testCase': [
                {
                    'onCloud': Boolean,
                    'codeproginputparams': String,
                    'codeprogexpectedoutput': String,
                    'scoreip': {type: Number, default: 0},
                    'attemptInMultiLine': Boolean,
                    'sampleTest': Boolean
                }
            ],
        },
        'questionTypeSubjective': {
            'subjectiveanswer': String,
            'isFileUpload': Boolean
        },
        'questionTypeWeb' : {
            'html' : String,
            'css' : String,
            'js' : String,
            'isHtmlAllowed' : Boolean,
            'isCssAllowed' : Boolean,
            'isJsAllowed' : Boolean,
            'isReactQuestion': Boolean,
            'reactRendererCode': String,
            'testRunnerNeeded': Boolean,
            'testRunnerFileContent': String,
            'testCase': [
                {
                    'description': String,
                    'evaluator': String,
                    'scoreip': {type: Number, default: 0},
                    'sampleTest': Boolean,
                }
            ]
        },
        'files' : [ String ],
        'createdBy': {'type': mongoose.Schema.ObjectId },
        'updatedBy': {'type': mongoose.Schema.ObjectId },
        'deletedBy': {'type': mongoose.Schema.ObjectId },
        'parentIdOfCreator': {'type': mongoose.Schema.ObjectId },
        'referenceLinks' : [ String ],
        'isCqDocument' : Boolean ,
        'isPublic' : { 'type' : Boolean , 'default' : false },
        'isLocked' : { 'type' : Boolean , 'default' : false },
        'isPremium': { 'type' : Boolean , 'default' : false },
        'isSubAdminQuestion' : Boolean,
        'difficultyLevel' : String,
        'executionTime' : String,
        'subType' : String,
        'negativeScore' : {type: Number, default: 0},
        'orgId': {'type': mongoose.Schema.ObjectId},
        'createdByTransfer': {type: Boolean, default: false},
        'bloomTaxonomy': {type: Number, default: 0},
        'courseOutcomes': {type: String},
        'topic': {type: String},
    },
    {
        'timestamps': true
    });
var populatePathAndSelectObjArray = [];

var autoPopulateUsers = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};

questionSchema.pre('findOne', autoPopulateUsers).pre('find', autoPopulateUsers);


module.exports = mongoose.model(libs.constants.modelString.QuestionModelString, questionSchema);
