const mongoose = require('mongoose');
const libs = require('../Lib');

const schema = mongoose.Schema({
    'title': {type: String},
    'quizId': {type: mongoose.Schema.ObjectId, ref: 'quiz._id', required: true},
    'roomName': {type: String, unique: true, required: true,},
    'userIds': [{type: mongoose.Schema.ObjectId}],
    'invigilatorIds': [{type: mongoose.Schema.ObjectId}],
    'createdBy': {type: mongoose.Schema.ObjectId, required: true},
    'orgId': {type: mongoose.Schema.ObjectId, required: true},
    'updatedBy': {type: mongoose.Schema.ObjectId, required: true},
    'deletedBy': {type: mongoose.Schema.ObjectId},
    'displaystatus': {type: String, default: libs.constants.displayStatus.active}
}, {timestamps: true})


schema.index({roomName: 1}, {'background': true});
schema.index({quizId: 1}, {'background': true});
module.exports = mongoose.model(libs.constants.modelString.StreamRooms, schema);