const mongoose = require('mongoose');
const libs = require("../Lib");

const quizFeedbackSchema = mongoose.Schema({
    'userId' : { 'type': mongoose.Schema.Types.ObjectId},
    'quizId': {'type': mongoose.Schema.Types.ObjectId, 'ref': 'Quiz'},
    'rating' : { type: Number, default: 0 },
    'feedbackText' : String,
}, { 'timestamps' : true } );


quizFeedbackSchema.index({ quizId: 1 }, { 'background': true });
module.exports = mongoose.model( libs.constants.modelString.quizFeedbackModelString, quizFeedbackSchema );