const mongoose = require('mongoose');
const libs = require("../Lib");

var activityLogs = mongoose.Schema(
    {
        'activityTime': {type: Date, required: true, default: new Date()},
        'type': Number,
        'subType': {type: Number, default: 1},
        'data': {type: Object},
        'quizId': {type: mongoose.Schema.ObjectId},
        'createdBy': {type: mongoose.Schema.ObjectId, required: true},
        'orgId': {type: mongoose.Schema.ObjectId, required: true},
        'version': {type: Number, required: true},
    },
    { 'timestamps' : true }
);


activityLogs.index( { 'activityTime' : 1 }, { 'background' : true } );
activityLogs.index({ 'orgId': 1, 'createdBy': 1}, {'background': true});
module.exports = mongoose.model(libs.constants.modelString.ActivityLogs, activityLogs);
