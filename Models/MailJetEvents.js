var mongoose = require('mongoose');
var libs = require("../Lib");
const { constants  } = libs
const { modelString } = constants;
var mailJet = mongoose.Schema({
    email: {
        type: String,
        required: true,
        index: true, // Index to quickly search by email
      },
      msgId: {
        type: String,
        required: true,
      },
      eventType: {
        type: String,
        required: true,
        enum: ['sent', 'delivered', 'opened', 'clicked', 'blocked', 'spam', 'bounced'], // Common event types
        index: true, // Index for filtering events by type
      },
      payload: {
        type: Object, // Additional event-related details
        required: false,
      },
      status : {
        type : String,
        required : false,
        enum : ['pending', 'acknowledged', 'resolved'],
        default : 'pending',
        index : true
      }
    },
    { 'timestamps' : true } );


let mailJetModel = mongoose.model(libs.constants.modelString.MailJetEventsModelString, mailJet);
module.exports = mailJetModel;