var mongoose = require('mongoose');
var libs = require("../Lib");
const { invalid } = require('moment');
const { quizProgressStep } = require('../Lib/constants');
//--
var quizSchema = mongoose.Schema({
    title: {
        type: String,
        required: [true, 'Title is required'],
        trim: true,
        minlength: [1, 'Title must be at least 1 character'],
        maxlength: [125, 'Title must be at most 125 characters'],
      },
      quizTime: {
        type: Number,
        required: [true, 'Quiz time is required'],
        min: [1, 'Quiz time must be greater than 0'],
        max: [100000, 'Quiz time must be less than 100000 minutes'],
      },
      instructions: {
        type: String,
        required: [true, 'Instructions are required'],
        maxlength: [1000000, 'Instructions must be less than 1000000 characters'],
      },
      isPrivate: {
        type: Boolean,
      },
      quizCode : {
        type: Number,
      },
      startTime: {
        type: Date,
      },
      endTime: {
        type: Date
      },
      entryStopTime: {
        type: Date,
      },
      showResults: {
        type: Boolean,
      },
        'keywords': String,
        'allowedIP': String,
        'quizContent': [{'id': {'type': mongoose.Schema.ObjectId}, 'executionType': String, 'contentType': String,
            'showHead' : Boolean, 'showTail' : Boolean , 'showCustomInput' : {'type': Boolean, default: true},
            score : Number, threshold : Number , isFreeze:Boolean },],
        'questionId' : [{ 'type' : mongoose.Schema.Types.ObjectId, 'ref': 'Question'}],
        // 'tutorialId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref' : 'Tutorial'},
        'languagesAllowed': [String],
        'attemptInSequence': {'type': Boolean, default: false},
        'revisitAllowed': {'type': Boolean, default: false},
        'displaystatus': {'type': Number, 'default': libs.constants.displayStatus.active}, //--  [ 0-Deleted 1-Active 2-PendingDeletion ]
        'quizSegments': [{
            'title': String, 
            'count' : { type : Number, default : 0},
            'pollNumber':Number,
        }],
        'createdBy': {'type': mongoose.Schema.ObjectId},
        'updatedBy': {'type': mongoose.Schema.ObjectId},
        'deletedBy': {'type': mongoose.Schema.ObjectId},
        'parentIdOfCreator': {'type': mongoose.Schema.ObjectId},
        'originalQuizId' : { 'type' : mongoose.Schema.Types.ObjectId },
        // 'quizCode' : Number,
        'startTime' : { 'type': Date, default: Date.now },
        'endTime' : Date,
        'reoprtsCode' : String,
        // 'invigilators' : [ { 'id' : String, 'name' : String, 'code' : String } ],
        'archaic' : Boolean ,
        'showResults' : Boolean,
        'quizParams' : [String],
        'copyPasteAllowed' : {'type': Boolean, default: false},
        'tabSwitchAllowed' : {'type': Boolean, default: false},
        'submitTestOnTabSwitchLimitBreach': {'type': Boolean, default: false},
        'sendMailToMentorAllowed' : {'type': Boolean, default: false},
        'isCreatedByMentor' : {'type': Boolean, default: true},
        'isCreatedByRecruiter' : {'type': Boolean, default: false},
        'isApplyTest' : Boolean,
        'isWebCamAllowed': Boolean,
        'isRandomImageCaptureEnabled' : { type: Boolean, default: false },
        'isAIProctoringEnabled' : { type: Boolean, default: false },
        'isSignUpAllowed': {'type': Boolean, default: false},
        'quizEmailSetting' : {
            'sendMail' : Boolean,
            'testCompletionStudentMail' : { type : Number, default : 0},
            'quizCompletionIntimationSetting' : { type : Number, default : 0},
            'mailTemplate' : {
                msg : [String], 
                subject : String, 
            },
            'intimationMailTemplate' : {
                msg : [String], 
                subject : String, 
            }
        },
        'cutOffMarks' : { type : Number, default : 0},
        'isTemplate' : Boolean,
        'randomizeQuestion' : Boolean,
        'poolQuestion' : Boolean,
        'isPublic': {'type': Boolean, default: true},          // public flag like in questions
        'quizUserDetails': [{
            fieldLabel: String,
            fieldName: String,
            fieldType: Number,
            fieldOptions: [String],
            fieldIsSelected: Boolean,
        }],
        'tabSwitchAlertLimit': { type: Number, default: 0 },
        'isFullScreen': { type: Boolean, default: false },
        'isLiveStreamEnabled': {type: Boolean, default: false}, 
        'roomEnabled': {type: Boolean, default: false},
        'isAppOnly': { type: Boolean, default: false },
        'allowClose': { type: Boolean, default: false },
        'orgId': {'type': mongoose.Schema.ObjectId},
        'usersInLiveStreamRoom': [{type: mongoose.Schema.ObjectId}],
        'createdByTransfer': {type: Boolean, default: false},
        'shareTestTokenId': {type: mongoose.Schema.ObjectId},
        'isRecordingEnabled': {type: Boolean, default: false},
        'isFrozen': { type: Boolean, default: false },
        'isMarkedAsCheck': {type: Boolean, default: false},
        'isVmDetectionEnabled': { type: Boolean, default: false },
        'toTerminateTestOnVmDetection': { type: Boolean, default: false},
        'toShuffleMCQOptions': { type: Boolean, default: false },
        'isRecordingUploadBlockingEnabled': { type: Boolean, default: false },
         progressStep: {
          type: Number,
          default: quizProgressStep.basic.value,
          required: true,
        },
        totalQuestions : { type: Number, default: 0},
        'onlySuspiciousRoom': { type: Boolean, default: false },
    },
    {
        timestamps: true
    }
);

// Pre-save hook to set fields based on session
// quizSchema.pre('save', function (next, options) {
//     if (!options || !options.session) {
//       return next(new Error('Session information is required'));
//     }
  
//     const session = options.session;
//     const constants = libs.constants; 
//     this.createdBy = session.userId;
//     if( session.parentId )
//         this.parentIdOfCreator = session.parentId;
//     this.isCreatedByMentor = session.role === constants.roleNumberFromString.mentor;
//     this.isCreatedByRecruiter = session.role === constants.roleNumberFromString.recruiter;
//     this.orgId = session.orgId;
  
//     if (this.isCreatedByMentor || this.isCreatedByRecruiter) {
//       this.parentIdOfCreator = session.parentId; // Assuming session contains parentId
//     }
  
//     next();
// });
// //--

// quizSchema.pre('findOneAndUpdate', function (next) {
//     const session = (this.getOptions ? this.getOptions() : this.options)?.session;
//     if(!session) {
//       return next(new Error('Session information is required'));
//     }
//     if (session && session.userId) {
//         this.updateOne({ $set: { updatedBy: session.userId } });
//     }
//     next();
//   });
module.exports = mongoose.model( libs.constants.modelString.QuizModelString, quizSchema);
