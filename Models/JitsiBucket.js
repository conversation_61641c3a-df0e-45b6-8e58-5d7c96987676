const mongoose = require('mongoose');

const libs = require('../Lib');

const schema = mongoose.Schema({
    'status': { type: Number, default: libs.constants.jitsiBucketConstants.active },
    'callStatsUserName': { type: String, required: true },
    'jitsiMeetId': { type: String, required: true },
    'assignedAt': { type: Date, required: true },
    'userId': { type: mongoose.Schema.ObjectId },
    'quizId': { type: mongoose.Schema.ObjectId },
});

schema.index({'status': 1}, {background: true})
schema.index({'userId': 1, 'quizId': 1}, {background: true, sparse: true, unique: true});
module.exports = mongoose.model(libs.constants.modelString.JitsiBucket, schema);
