var mongoose = require('mongoose');
var libs = require("../Lib");
const { constants  } = libs
const { modelString } = constants;
var userAnswer = mongoose.Schema({
        'courseId' : {'type': mongoose.Schema.Types.ObjectId, 'ref': modelString.CourseModelString },
        'quizId': {'type': mongoose.Schema.Types.ObjectId, 'ref': modelString.QuizModelString},
        'adminProjectId' : { 'type': mongoose.Schema.Types.ObjectId, 'ref': modelString.AdminProjectModelString },
        'projectId' : { 'type': mongoose.Schema.Types.ObjectId, 'ref': modelString.ProjectModelString },
        'questionId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref': modelString.QuestionModelString},
        'tutorialId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref' : modelString.TutorialModelString },
        'courseQuizId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref' : modelString.QuizModelString },
        'completed' : {'type' : Boolean, 'default': false},
        'userId' : { 'type': mongoose.Schema.Types.ObjectId },
        'lastActive' : { 'type' : Date, 'default' : Date.now},
        'tutorialAttemptedSegments' : { 'type' : Number, 'default' : 0},
        'finalSubmission' : {'type' : Boolean, 'default': false},
        'score' : { 'type' : Number, 'default' : 0},
        'projectStatus' : {type: Number, default: libs.constants.projectStatus.inProgress },
        'oldQuizAttempt' : Boolean,
        'projectName': String,
        'attemptProjectId': { 'type': mongoose.Schema.Types.ObjectId, 'ref': modelString.ProjectModelString },
        'isReviewPending': {'type' : Boolean, 'default': false},
        // 'quizSubmissions' : Number,
    },
    { 'timestamps' : true } );

var populatePathAndSelectObjArray = [
    // {path: 'courseId', select: 'title courseContent displaystatus courseSegments attemptInSequence'},
    {path: 'quizId', select: 'title quizContent displaystatus quizSegments attemptInSequence toShuffleMCQOptions'},
    {path: 'questionId', "select": '-hint -askconf -courseId -createdAt -explanation -showhint -tags -updatedAt'},
    // {path: 'adminProjectId', "select": 'title description projectLink'},
    // {path: 'projectId', "select": 'name description'},
]

var autoPopulateCourses = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};

userAnswer.pre('findOne', autoPopulateCourses).pre('find', autoPopulateCourses);
userAnswer.index({ 'userId' : 1, 'questionId' : 1 }, { 'background' : true } );
let userAnswerModel = mongoose.model(libs.constants.modelString.UserAnswerModelString, userAnswer);
userAnswerModel.on('index', function(error, result) {
    // "_id index cannot be sparse"
    if( error )
    console.log(error.message);
});
module.exports = userAnswerModel;