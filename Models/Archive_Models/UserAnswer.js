var mongoose = require('mongoose');
var libs = require("../../Lib");

var userAnswer = mongoose.Schema({
        'courseId' : {'type': mongoose.Schema.Types.ObjectId, 'ref': 'Course'},
        'quizId': {'type': mongoose.Schema.Types.ObjectId, 'ref': 'Quiz'},
        'adminProjectId' : { 'type': mongoose.Schema.Types.ObjectId, 'ref': libs.constants.modelString.AdminProjectModelString },
        'questionId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref': 'Question'},
        'tutorialId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref' : 'Tutorial'},
        'completed' : {'type' : Boolean, 'default': false},
        'userId' : { 'type': mongoose.Schema.Types.ObjectId, 'ref' : 'User'},
        'lastActive' : { 'type' : Date, 'default' : Date.now},
        'tutorialAttemptedSegments' : { 'type' : Number, 'default' : 0},
        'finalSubmission' : {'type' : Boolean, 'default': false},
        'score' : { 'type' : Number, 'default' : 0},
        'projectStatus' : {type: Number, default: libs.constants.projectStatus.inProgress },
    },
    { 'timestamps' : true } );

var populatePathAndSelectObjArray = [
    {path: 'courseId', select: 'title courseContent displaystatus courseSegments attemptInSequence'},
    {path: 'quizId', select: 'title quizContent displaystatus quizSegments attemptInSequence'},
    {path: 'questionId', "select": '-hint -askconf -courseId -createdAt -explanation -showhint -tags -updatedAt'},
    {path: 'adminProjectId', "select": 'title description projectLink'},
    {path: 'userId', select: 'displayname email role'}
]

var autoPopulateCourses = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};

userAnswer.pre('findOne', autoPopulateCourses).pre('find', autoPopulateCourses);
userAnswer.index({ 'userId' : 1, 'questionId' : 1 }, { 'background' : true } );
userAnswer.index({ 'userId' : 1, 'projectId' : 1 }, { 'background' : true } );
let userAnswerModel = mongoose.model(libs.constants.modelString.archivedUserAnswerModelString, userAnswer);
userAnswerModel.on('index', function(error, result) {
    // "_id index cannot be sparse"
    if( error )
    console.log(error.message);
});
module.exports = userAnswerModel;