var mongoose = require('mongoose');
var libs = require("../../Lib");

var userAttempt = mongoose.Schema({
        'userAnswerId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref' : libs.constants.modelString.archivedUserAnswerModelString },
        'tutorialSegmentId' : { 'type' : mongoose.Schema.Types.ObjectId },
        'count' : {'type': Number, 'default' : 1},
        'attemptData' : [
            {
                '_id' : {'type': mongoose.Schema.Types.ObjectId},
                'userProgram' : String,
                'userLanguage' : String,
                'userOutputCoding' : [String],
                'userCompilationError' : { 'type' : String, default : "" },
                'userInputMCQ' : String,
                'userInputSubjective' : String,
                'timeOfCreation' : { 'type' : Date, 'default' : Date.now },
                'finalSubmission' : {'type' : Boolean, 'default': false},
                'isCustomInput' : Boolean,
                'customInput' : String,
                'suspiciousActivityData' : [ { 'time' : { 'type' : Date, 'default' : Date.now }, 'code' : String, 'pastedData' : String } ],
                'testCasePassed' : Number,
                'projectLink' : String,
            }
        ]
    },
    {
        'timestamps' : true
    });

var populatePathAndSelectObjArray = [ { path: 'userAnswerId', select: 'courseId email questionId completed userId quizId adminProjectId' } ];

var autoPopulateUserAnswer = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};

userAttempt.
pre('findOne', autoPopulateUserAnswer).
pre('find', autoPopulateUserAnswer);

userAttempt.index( { 'userAnswerId' : 1 }, { 'background' : true }) ;

module.exports = mongoose.model(libs.constants.modelString.archivedUserAttemptModelString, userAttempt);