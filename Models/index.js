module.exports = {
    Questions: require("./Questions"),
    Quiz: require("./Quiz"),
    Tags: require("./Tags"),
    User: require("./User"),
    StreamRoom : require('./StreamRoom'),
    UserAnswer : require('./UserAnswer'),
    UserAttempt : require('./UserAttempt'),
    UserQuizSubmittedSegment : require('./UserQuizSubmittedSegments'),
    UserQuizSubmittedSegmentMetaData: require('./UserQuizSubmittedSegmentsMetaData'),
    QuizName : require("./QuizNames"),
    BadActivity : require("./BadActivities"),
    AdminProject : require("./AdminProject"),
    ArchivedModels : require("./Archive_Models"),
    quizFeedback : require("./quizFeedback"),
    QuizQuestionMetaData : require("./QuizQuestionMetaData"),
    QuizAllowedEmails: require("./QuizAllowedEmails"),
    JitsiBucket: require('./JitsiBucket'),
    MailJetEvent: require('./MailJetEvents'),
};