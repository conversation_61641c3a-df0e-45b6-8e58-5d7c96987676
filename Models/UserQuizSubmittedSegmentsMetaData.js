const mongoose = require('mongoose');
const constants = require('../Lib/constants');

const schema = mongoose.Schema({
    userId: {type: mongoose.Schema.Types.ObjectId},
    quizId: {type: mongoose.Schema.Types.ObjectId},
    extraTime: [{
        extraTime: Number,
        expireAt: Date,
        createdBy: {type: mongoose.Schema.Types.ObjectId},
        createdAt: Date,
    }]
})


schema.index( {'quizId': -1, 'userId': -1 }, { 'background' : true } );
module.exports = mongoose.model(constants.modelString.UserQuizSubmittedSegmentMetaData, schema);
