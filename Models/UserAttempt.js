var mongoose = require('mongoose');
var libs = require("../Lib");
var userAttempt = mongoose.Schema({
        'userAnswerId' : { 'type' : mongoose.Schema.Types.ObjectId, 'ref' : 'UserAnswer'},
        'tutorialSegmentId' : { 'type' : mongoose.Schema.Types.ObjectId },
        'count' : {'type': Number, 'default' : 1},
        'attemptData' : [
            {
                '_id' : {'type': mongoose.Schema.Types.ObjectId},
                'codeId': String, 
                'userProgram' : String,
                'userLanguage' : String,
                'userOutputCoding' : [String],
                'userCompilationError' : { 'type' : String, default : "" },
                'userInputMCQ' : String,
                'userInputSubjective' : String,
                'timeOfCreation' : { 'type' : Date, 'default' : Date.now },
                'finalSubmission' : {'type' : Boolean, 'default': false},
                'isCustomInput' : Boolean,
                'customInput' : String,
                'suspiciousActivityData' : [ { 'time' : { 'type' : Date, 'default' : Date.now }, 'code' : String, 'pastedData' : String } ],
                'projectLink' : String,
                'files': [String],
                'testCasePassed': Number,
                'totalTestCases': Number,
                'totalScore': Number,
                'html' : String,
                'css' : String,
                'js' : String,
                'webTestCaseStatus': [Boolean],
            }
        ]
    },
    {
        'timestamps' : true
    });

var populatePathAndSelectObjArray = [ { path: 'userAnswerId', select: 'courseId email questionId completed userId quizId adminProjectId' } ];

var autoPopulateUserAnswer = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};

userAttempt.
pre('findOne', autoPopulateUserAnswer).
pre('find', autoPopulateUserAnswer);

userAttempt.index( { 'userAnswerId' : 1 }, { 'background' : true }) ;

module.exports = mongoose.model(libs.constants.modelString.UserAttemptModelString, userAttempt);