const mongoose = require('mongoose');
const libs = require("../Lib");

const schema = mongoose.Schema({
    'quizId': {'type': mongoose.Schema.Types.ObjectId, 'ref': 'Quiz'},
    'courseId' : {'type': mongoose.Schema.Types.ObjectId, 'ref': 'Course'},
    'userId' : { 'type': mongoose.Schema.Types.ObjectId},
    'activity' : [ { 'level' : Number, 'time' : Date, 'msg' : String, 'code' : String } ] },
    { 'timestamps' : true } );


var populatePathAndSelectObjArray = [
    // {path: 'courseId', select: 'title courseContent displaystatus courseSegments attemptInSequence'},
    {path: 'quizId', select: 'title quizContent displaystatus quizSegments attemptInSequence'},
    //{path: 'userId', select: 'displayname email role'}
]

var autoPopulateCourses = function (next) {
    libs.util.handleAutoPopulate( this, populatePathAndSelectObjArray );
    next();
};

schema.pre('findOne', autoPopulateCourses).pre('find', autoPopulateCourses);
schema.index({'userId' : 1, 'quizId' : 1 }, { 'background' : true } );
module.exports = mongoose.model(libs.constants.modelString.badActivityModelString, schema);