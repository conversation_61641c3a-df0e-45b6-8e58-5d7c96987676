const axios = require('axios').default;
const mongoose = require('mongoose');
const libs = require('../Lib');
const service  = require('../Services');
const questionController = require('./QuestionController');

const fetchContentFromOtherServer = async (type, contentId) => {
    const dataToSend = JSON.stringify({type, contentId});
    const signature = libs.util.getSignatureOfPayload(dataToSend);
    const response = await axios.post(`${process.env.DATA_TRANSFER_SERVER}/teamOnly/getData`, dataToSend, {
        headers: {
            'Content-Type': 'application/json',
            'signature': signature,
        }
    });
    if (response.data.error) {
        throw new Error(response.data.error);
    }
    return response.data;
}



const saveQuestionFromOtherServer = async (questionId, receiverObj) => {
    const questionObj = await fetchContentFromOtherServer(libs.constants.contentTypeString.question, questionId);
    if (!questionObj) {
        throw new Error('No Question to save');
    }
    questionObj._id = mongoose.Types.ObjectId();
    questionObj.createdBy = receiverObj._id;
    questionObj.updatedBy = receiverObj._id;
    questionObj.orgId = receiverObj.orgId;
    delete questionObj.deletedBy;
    delete questionObj.bloomTaxonomy;
    questionObj.parentIdOfCreator = receiverObj.orgId;
    questionObj.createdByTransfer = true;
    questionObj.tags = '';
    await questionController.handleTestCaseSaveToCloud(questionObj, questionObj?.questionTypeCoding?.testCase ?? []);
    const result = await service.QuestionService.addQuestion(questionObj);
    console.log(`Question with id ${result._id.toString()} is saved`);
    return result;
}

const isQuestionAlreadyPreset = async (questionId) => {
    try {
        const questionObj = await service.QuestionService.getOneQuestion({_id: questionId}, {}, {});
        if (!questionObj) {
            throw new Error('Question not present');
        }
        return true;
    } catch (error) {
        console.log(error);
        return false;
    }
}

const saveQuizFromOtherServer = async (quizId, receiverObj) => {
    const quizObj = await fetchContentFromOtherServer(libs.constants.contentTypeString.quiz, quizId);
    const questionsToSave = quizObj.questionId;
    const newQuestionArray = [];
    const questionMapFromOldToNew = new Map();
    for (let questionId of questionsToSave) {
        try {
            console.log(`Save Question For Quiz ${questionId}`);
            const questionSaveResult = await saveQuestionFromOtherServer(questionId, receiverObj);
            newQuestionArray.push(questionSaveResult._id);
            questionMapFromOldToNew.set(questionId, questionSaveResult._id.toString());
            questionSaveResult._id
        } catch (error) {
            console.log(error);
        }
    }
    quizObj.quizContent.forEach((ele) => {
        ele.id = questionMapFromOldToNew.get(ele.id);
    });
    quizObj.questionId = newQuestionArray;
    quizObj.createdBy = receiverObj._id;
    quizObj._id = mongoose.Types.ObjectId();
    quizObj.updatedBy = receiverObj._id;
    delete quizObj.deletedBy;
    quizObj.parentIdOfCreator = receiverObj.orgId;
    quizObj.createdByTransfer = true;
    quizObj.keywords = '';
    const resultQuiz = await service.QuizService.addQuiz(quizObj)
    let createObj = { 'name': Date.now(), 'quizId': resultQuiz._id, 'createdBy': receiverObj._id };
    await service.QuizNameService.addQuizName(createObj);

}
module.exports = {
    saveQuestionFromOtherServer,
    saveQuizFromOtherServer,
}
