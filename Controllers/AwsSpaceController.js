const { S3, ListObjectsCommand, PutObjectTaggingCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const s3PublicUrl = require('s3-public-url');

const AwsConfigObj = {
    bucket: process.env.TEST_CASE_BUCKET_NAME,
    region: process.env.TEST_CASE_BUCET_REGION,
    endpoint: process.env.TEST_CASE_BUCKET_ENDPOINT,
    accessKeyId: process.env.TEST_CASE_BUCKET_KEY,
    secretAccessKey: process.env.TEST_CASE_BUCKET_AUTH_KEY,
}

const s3Client = new S3({
    forcePathStyle: false,
    endpoint: AwsConfigObj.endpoint,
    region: AwsConfigObj.region,
    credentials: {
        accessKeyId: AwsConfigObj.accessKeyId,
        secretAccessKey: AwsConfigObj.secretAccessKey,
    }
});

/**
 * 
 * @param {string} location 
 * @param {{ ResponseContentType: string, ResponseContentDisposition: string }} config 
 * @returns 
 */
const presignedGetUrl = async (location, config) => {
    const defaultConfig = {
        Bucket: AwsConfigObj.bucket,
        Key: location,
    }
    const params = {
        ...defaultConfig,
        ...(config ?? {}),
    }
    const command = new GetObjectCommand({
        ...params,
    });
    return getSignedUrl(s3Client, command, {
        expiresIn: 5 * 24 * 60 * 60,
    });
}


/**
 * 
 * @param {string} location 
 * @param {{ content: string, ContentType: string, publicRead?: boolean }} config 
 */
const putFile = async (location, config) => {
    return s3Client.putObject({
        Bucket: AwsConfigObj.bucket,
        Key: location,
        Body: config.content,
        ContentType: config.ContentType,
        ACL: config.publicRead ? 'public-read': 'private',
    })
}


/**
 * 
 * @param {string} source 
 * @param {string} dest 
 * @param {{ sameBucket: boolean } | { sourceBucket: string }} config 
 * @returns 
 */
const copyFile = (source, dest, config) => {
    let finalSource = '';
    if (config.sameBucket) {
        finalSource = encodeURIComponent(`${AwsConfigObj.bucket}/${source}`);
    } else {
        finalSource = encodeURIComponent(`${config.sourceBucket}/${source}`);
    }
    return s3Client.copyObject({
        Bucket: AwsConfigObj.bucket,
        CopySource: finalSource,
        Key: dest,
    });
}

/**
 * 
 * @param {string} location 
 * @param {{ isDir?: boolean }} config 
 */
const deleteObjects = async (location, config = {}) => {
    let filesToDelete = [location];
    if (config.isDir) {
        filesToDelete = [];
        const listCommand = new ListObjectsCommand({
            Prefix: location,
            Bucket: AwsConfigObj.bucket,
        });
        const files = await s3Client.send(listCommand);
        if (files.Contents) {
            files.Contents.forEach((content) => {
                filesToDelete.push(content.Key);
            });
        }
    }
    const promiseArray = [];
    filesToDelete.forEach((ele) => {
        const command = new PutObjectTaggingCommand({
            Bucket: AwsConfigObj.bucket,
            Key: ele,
            Tagging: {
                TagSet: [
                    {
                        Key: "expiry",
                        Value: "7d",
                    }
                ]
            }
        });
        promiseArray.push(s3Client.send(command));
    });
    await Promise.all(promiseArray);
}

const streamToString = async (stream) => {
    const chunks = [];
    for await (const chunk of stream) {
        chunks.push(Buffer.from(chunk));
    }
    return Buffer.concat(chunks).toString("utf-8");
};


const getContent = async (location) => {
    try {
        const response = await s3Client.getObject({
            Key: location,
            Bucket: AwsConfigObj.bucket,
            ResponseContentType: "text/plain",
        });
        if (!response.Body || !(response.Body)) {
            throw new Error("Invalid response body");
        }
        const text = await streamToString(response.Body);
        return text;
    } catch (error) {
        console.error(error);
        throw error;
    }
}

const getLocation = (location) => {
    const url = s3PublicUrl.getHttps(AwsConfigObj.bucket, location, AwsConfigObj.region);
    return url;
}

module.exports = {
    putFile,
    copyFile,
    getContent,
    getLocation,
    deleteObjects,
    presignedGetUrl,
}
