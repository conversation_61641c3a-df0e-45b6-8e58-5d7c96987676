// Import necessary modules
const express = require('express');
const fs = require('fs');
const path = require('path');
const router = require('express').Router();
const socket = require('socket.io');
const ejs = require('ejs');
const dayjs = require('dayjs');
const timezone = require('dayjs/plugin/timezone');
dayjs.extend(timezone);
const services = require('../Services');
const puppeteer = require('puppeteer');
const QuizController = require('./QuizController');
const axios = require('axios');

const libs = require("../Lib");
const { aiProctoringCheatingSubType } = require('../Lib/constants');
const { UserController } = require('.');
global.revaluationData = {};
// Endpoint to process the image
const handleProctoring = async (req, res, next) => {
  try {
    const { quizId, userId } = req.query;
    console.time("Proctoring");
    console.log("quizId:", quizId, "userId:", userId);
    if (!quizId) throw new Error("Quiz ID is required");
    const findObj = { quizId, userId };
    req.params = { quizId, userId };
    global.revaluationData[ quizId ] =  global.revaluationData[ quizId ] || {};
    const result = await QuizController.getUsersDataForQuizFromRedis(req);

    const browser = await puppeteer.launch({ }); // Set to true for production
    const page = await browser.newPage();
    const revaluationData = global.revaluationData[ quizId ] 
    for (let i = 0; i < result?.userDataArray?.length; i++) {
      const userId = result?.userDataArray?.[i]._id;
      revaluationData[userId] = { 'valid' : [], 'invalid' : [], details : { } };
      let aiProctorReport = await QuizController.getUserAiProctorReport({ quizId, userId });
      for (let j = 0; j < aiProctorReport?.length; j++) {
        const proctorObj = aiProctorReport[j];
        const { imageUrl } = proctorObj;
        if (proctorObj.subType === aiProctoringCheatingSubType.userExit) {
          const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
          const base64Image = `${Buffer.from(response.data).toString()}`;
          await page.setContent('<!DOCTYPE html><html><body></body></html>', { waitUntil: 'networkidle2' });

          await page.evaluate((imageSrc) => {
            const img = document.createElement('img');
            img.id = 'inputImage';
            img.crossOrigin = 'anonymous';
            img.style.maxWidth = '640px';
            img.style.maxHeight = '480px';
            img.src = imageSrc;
            document.body.appendChild(img);

            const canvas = document.createElement('canvas');
            canvas.id = 'hiddenCanvas';
            canvas.style.display = 'none';
            document.body.appendChild(canvas);
          }, base64Image);

          const scriptPath = path.resolve(path.dirname(__dirname), 'Public', 'puppeteer', 'proctorScript.js'); // External script file
          console.log("scriptPath:", scriptPath);
          await page.addScriptTag({ path: scriptPath });

          // Wait for image data to be available
          await page.waitForFunction('window.imageData !== undefined', { timeout: 200000 });
          const imageData = await page.evaluate(() => window.imageData);

          console.log("Image data:", imageData);
          const { faceDetection, objectDetection } = imageData || {};   
          if (faceDetection && faceDetection.length > 0) {
            revaluationData[userId]?.invalid.push({ faceDetection, objectDetection, proctorObj });
            for( k = j+1; k < aiProctorReport?.length; k++) {
              const { subType } = aiProctorReport[k];
              if( subType === aiProctoringCheatingSubType.userEnter) {
                revaluationData[userId]?.invalid.push({ proctorObj : aiProctorReport[k] });
                j = k;
                break;
              } else if ( subType === aiProctoringCheatingSubType.userExit) {
                j = k -1;
                break;
              } else {
                  revaluationData[userId]?.valid.push({ proctorObj : aiProctorReport[k] });
              }
            }
          } else {
            revaluationData[userId]?.valid.push({ proctorObj });

          }
          await page.close();
        } else {
          revaluationData[userId]?.valid.push({ proctorObj });
        }
      }
    }
    console.log("revaluationData:", revaluationData);
    await browser.close();
    console.timeEnd("Proctoring");
    res.json({ message: 'Image processed successfully' });
  } catch (error) {
    console.error('Error processing image:', error);
    res.status(500).json({ error: 'Failed to process the image' });
  }
}

const handleProctoringUpdation = async (req, res, next) => {
  console.log(" payload ", req.body);
  const { quizId, userId, data } = req.body;
  services.RedisServices.redis('rpush', "procotringupdate", JSON.stringify({ quizId, userId, data }));
}

const updateNewProctoringdata = async (req, res, next) => {
  try {
    const { quizId, userId } = req.query;
    console.time("Proctoring");
    console.log("quizId:", quizId, "userId:", userId);
    if (!quizId) throw new Error("Quiz ID is required");
    if (!userId) throw new Error("User ID is required");

    const quizRevluationData = global.revaluationData[ quizId ] || {};
    const userRevaluationData = quizRevluationData[userId] || {};
    const { valid, invalid } = userRevaluationData;
    if( !valid ) throw new Error("No valid data found for the user");
    console.log("valid new data:", valid);
    const updatedData = await QuizController.updateAiProctorReport({ quizId, userId}, valid);
    console.log("updatedData:", updatedData);
    res.json({ message: 'Proctoring data updated successfully' });
  } catch (error) {
    console.error('Error updating proctoring image:', error);
    res.status(500).json({ error: 'Failed to update the proctoring data' });
  }
}

const getProctoringData = async (quizId, userId) => {
  try {


    if (!quizId) throw new Error("Quiz ID is required");
    // if (!userId) throw new Error("User ID is required");

    let data = global.revaluationData[quizId] || {};
    if( userId ){
      data = quizRevluationData[userId] || {};
      return res.json(data)
    } 



    if (!valid) throw new Error("No valid data found for the user");

    res.json({ valid, invalid });
  } catch (error) {
    console.log('Error getting proctoring image:', error);
    res.status(500).json({ error: error.message || 'Failed to get the proctoring data' });
  }
};


module.exports = {
  handleProctoring,
  handleProctoringUpdation,
  updateNewProctoringdata,
  getProctoringData, // Export the new function
};

