const services = require('../Services');
const libs = require("../Lib");
const logger = console

var getQuizName = function (criteria, projections, options, callback) {
    if (!( criteria && projections && options )) {
        logger.debug("invalid parameters getQuizName criteria-", criteria, " projections-", projections, " options-", options);
        logger.error("invalid parameters getQuizName");
        callback({ 'error' : "invalid parameters getOneQuizName"});
        return;
    }
    return services.QuizNameService.getQuizName(criteria, projections, options, callback);
};

var getOneQuizName = function (criteria, projections, options = {} , callback) {
    if (!( criteria && projections && options )) {
        logger.debug("invalid parameters getOneQuizName criteria-", criteria, " projections-", projections, " options-", options);
        logger.error("invalid parameters getOneQuizName");
        callback({ 'error' : "invalid parameters getOneQuizName"});
        return;
    }
    return services.QuizNameService.getOneQuizName(criteria, projections, options, callback);
};

var updateQuizName = function (criteria, updateObj, options, callback) {
    if (!( criteria && updateObj && options )) {
        logger.debug("invalid parameters updateQuizName criteria-", criteria, " updateObj-", updateObj, " options-", options);
        logger.error("invalid parameters updateQuizName");
        callback({ 'error' : "invalid parameters updateQuizName"});
        return;
    }
    return services.QuizNameService.updateQuizName(criteria, updateObj, options, callback);
};

var performAggregation = function (aggregationArray, callback) {
    return services.QuizNameService.aggregate(aggregationArray, callback);
};

var addQuizName = function (obj, callback) {
    return services.QuizNameService.addQuizName(obj, callback);
};

var checkQuizNameAvailability = async (quizName, callback) => {
    const isCB = callback && typeof callback === 'function';
    try {
        if (!quizName) quizName = Date.now();
        if (!libs.regex.quizNameRegex.test(quizName)) throw new Error('Quiz link is not valid');
        quiz = await getOneQuizName({'name': quizName}, {'quizId': 1}, {'autoPopulate': false});
        if (quiz?._id) throw new Error(libs.messages.quizName.inUse);
        if(isCB) return callback(null, quizName);
        return quizName;
    } catch (error) {
        if(isCB) return callback(error?.messages ?? error);
        throw new Error(error?.messages ?? error);
    }
};

var assignNameToQuiz = async function (quizName, quizId, sessionObj, callback) {
    const isCB = callback && typeof callback === 'function';
    try {
        if (!(quizName && quizId)) {
            logger.debug(`assignNameToQuiz- parameters not valid ! quizName- ${quizName} quizId- ${quizId}`);
            logger.error(`assignNameToQuiz- parameters not valid`);
            throw new Error('Parameters not valid');
        }
        let createObj = { 'name': quizName, 'quizId': quizId, 'createdBy': sessionObj.userId };
        let oldQuizNameObj = await getOneQuizName({ 'quizId': quizId }, { quizId: 1, name: 1 }, {})
        await removeQuizName({ 'quizId': quizId })
        if (oldQuizNameObj) {
            let resultRedis = await services.RedisServices.redis('del', oldQuizNameObj.name)
        }
        let createdQuizName = await addQuizName(createObj)
        if (!createdQuizName) {
            console.log(`assignNameToQuiz quizName not created`, createObj);
            console.log(`assignNameToQuiz  quizName not created`);
            throw new Error('error in creating quizName');
        } else {
            if(isCB)    return callback(null, { 'msg': 'Test Name Assigned Successfully', quizId });
            else return { 'msg': 'Test Name Assigned Successfully', quizId }
        }
    } catch (e) {
        console.log("Error while creating quiz link", e);
        if(isCB)    return callback({ 'error': 'error in creating quiz name' });
        throw new Error('error in creating quiz name');
    }
};

var removeQuizName = function (criteria, callback) {
    return services.QuizNameService.removeQuizName(criteria, callback);
}


module.exports = {
    getQuizName: getQuizName,
    getOneQuizName: getOneQuizName,
    updateQuizName: updateQuizName,
    performAggregation: performAggregation,
    addQuizName: addQuizName,
    checkQuizNameAvailability: checkQuizNameAvailability,
    assignNameToQuiz: assignNameToQuiz,
    removeQuizName: removeQuizName
};