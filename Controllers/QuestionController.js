const services = require('../Services');
const ejs = require('ejs');
const multer = require('multer');
const tagController = require('../Controllers/TagController');
const async = require("async");
const logger = console
const path = require('path');
const mongoose = require('mongoose');
const axios = require('axios');
const fse = require('fs-extra');
const libs = require("../Lib");
const dtFrmt = libs.dateFormats;
const { constants } = libs
const { roleNumberFromString } = constants
var request = require('request');
const indexController = require("./IndexController");
const AwsSpaceController = require('./AwsSpaceController');


const getOneQuestion = function (criteria, projection, options = {}, callback) {
    return services.QuestionService.getOneQuestion(criteria, projection, options, callback);
};
const getQuestion = function (criteria, projections, options, callback) {
    return services.QuestionService.getQuestion(criteria, projections, options, callback);
};
const updateOneQuestion = function (criteria, updateData, options, callback) {
    return services.QuestionService.updateQuestion(criteria, updateData, options, callback);
};
const updateQuestions = function (criteria, updateData, options, callback) {
    return services.QuestionService.updateQuestions(criteria, updateData, options, callback);
};
const questionCountQueryResult = function (criteria, callback) {
    return services.QuestionService.countQueryResult(criteria, callback);
};

var getQuestionTypeProperties = function (questionType) {
    let questionTypeProperties = libs.constants.questionTypeProperties;

    if (Object.prototype.hasOwnProperty.call(questionTypeProperties, ( questionType )))
        return questionTypeProperties[questionType];

    logger.debug("Invalid question type", questionType);
    logger.error("Invalid question type");
    return undefined;
};

var setModelValuesMCQ = function (payload, newQues) {
    newQues.options = [];
    newQues.correctAnswers = [];
    newQues.answer = payload.mcqExplanation;
    for (let i = 1; i <= 8; i += 1) {
        if (Object.prototype.hasOwnProperty.call(payload, 'txtOpt' + i)) {
            newQues.options.push(payload['txtOpt' + i].trim());
            if (payload['chkOpt' + i])
                newQues.correctAnswers.push(i - 1);
        }
    }
    return true;
};

var setHeadBodyTailCoding = function (payload, newQues) {
    let codeComponentObject;
    try {
        codeComponentObject = JSON.parse(payload.codecomponent);
    }
    catch (e) {
        logger.debug("setHeadBodyTailCoding payload parse error", payload.codecomponent, "error---", e)
        logger.error("setHeadBodyTailCoding payload parse error", payload.codecomponent)
        return false;
    }

    let codeLanguageArray = [];

    if (payload.type === '5') {
        return true;
        if (!( Object.prototype.hasOwnProperty.call(payload, 'type') &&
            Object.prototype.hasOwnProperty.call(payload, 'stepwiseLang') &&
            payload.stepwiseLang.length )) {
            logger.debug("setHeadBodyTailCoding payload does not have all mandatory properties", payload);
            logger.error("setHeadBodyTailCoding payload not valid", payload);
            return false;
        }
        codeLanguageArray = [payload.stepwiseLang];
    }
    else if (!Object.prototype.hasOwnProperty.call(payload, 'progLang')) {
        logger.debug("setHeadBodyTailCoding payload does not have property progLang ", payload);
        logger.error("setHeadBodyTailCoding payload does not have property progLang");
        return false;
    }
    else if (!( payload.progLang instanceof Array ))
        codeLanguageArray = payload.progLang.split(',');
    else
        codeLanguageArray = payload.progLang;
    if (!codeComponentObject || Object.keys(codeComponentObject).length !== codeLanguageArray.length) {
        logger.debug(" codeComponentObject is invalid setHeadBodyTailCoding", codeComponentObject);
        logger.error("codeComponentObject is invalid setHeadBodyTailCoding");
        logger.error(" Object.keys(codeComponentObject).length -", Object.keys(codeComponentObject).length, " codeLanguageArray.length-", codeLanguageArray.length)
        return false;
    }

    codeLanguageArray.forEach(function (language) {
        let code = codeComponentObject[language].head + '\n' + codeComponentObject[language].body + '\n' + codeComponentObject[language].tail;
        newQues.codeproglang.push({
            'language': language,
            'codeComponents': {
                'head': codeComponentObject[language].head,
                'body': codeComponentObject[language].body,
                'tail': codeComponentObject[language].tail,
                'solution': codeComponentObject[language].solution
            },
            'defaultTrimmedCode' : code.replace(/^\s*$[\n\r]{1,}/gm, '')
        });
    });
    return true;
}
var setModelValuesCoding = function (payload, newQues, oldQues) {
    newQues.codeproglang = [];
// TODO - remove head body tail form multiple answer type
    if (Object.prototype.hasOwnProperty.call(payload, 'txtExpectedOutput') &&
        Object.prototype.hasOwnProperty.call(payload, 'txtInputParams') &&
        Object.prototype.hasOwnProperty.call(payload, 'scoreip') &&
        setHeadBodyTailCoding(payload, newQues)) {

        newQues.testCase = oldQues && oldQues.questionTypeCoding ? oldQues.questionTypeCoding.testCase : [];

        if (payload.type == libs.constants.questionTypeNumeric.questionTypeStepwise) {
            newQues.testCase = [];
            payload.txtInputParams = payload.txtInputParams.replace(/[\r]/g, '');
            payload.txtExpectedOutput = payload.txtExpectedOutput.replace(/[\r]/g, '');

            newQues.testCase.push({
                'codeproginputparams': payload.txtInputParams.trim(),
                'codeprogexpectedoutput': payload.txtExpectedOutput.trim(),
                'scoreip': payload.scoreip || 0,
                'attemptInMultiLine': payload.attemptInMultiLine ? true : false,
                'sampleTest': payload.sampleTest ? true : false
            });

            for (i = 1; i <= libs.constants.maximumTestCases; i += 1) {
                if (Object.prototype.hasOwnProperty.call(payload, 'txtInputParams' + i)) {
                    payload['txtInputParams' + i] = payload['txtInputParams' + i].replace(/[\r]/g, '');
                    payload['txtExpectedOutput' + i] = payload['txtExpectedOutput' + i].replace(/[\r]/g, '');

                    newQues.testCase.push({
                        'codeproginputparams': payload['txtInputParams' + i].trim(),
                        'codeprogexpectedoutput': payload['txtExpectedOutput' + i].trim(),
                        'scoreip': payload['scoreip' + i],
                        'attemptInMultiLine': payload['attemptInMultiLine' + i] ? true : false ,
                        'sampleTest': payload['sampleTest' + i] ? true : false
                    });
                }
            }
            newQues.multipleTestCases = payload.multipleTestCases ? true : false;
        }
        return true;
    }
    logger.error(" test case parameters are not present", payload);
    return false;
};

const setModelValuesWeb = function (payload, newQues) {
    console.log('\n\n\n', payload, '\n\n\n');
    newQues.html = payload.html || "";
    newQues.css = payload.css || "";
    newQues.js = payload.js || "";
    
    newQues.isHtmlAllowed = parseInt(payload.isHtmlAllowed) ? true : false;
    newQues.isCssAllowed = parseInt(payload.isCssAllowed) ? true : false;
    newQues.isJsAllowed = parseInt(payload.isJsAllowed) ? true : false;

    newQues.isReactQuestion = parseInt(payload.isReactQuestion) ? true : false;
    newQues.reactRendererCode = payload.reactRendererCode || '';
    newQues.testRunnerNeeded = newQues.isReactQuestion ? true : false;
    if(newQues.testRunnerNeeded) {
      newQues.testRunnerFileContent = payload.testRunnerFileContent || "";
    } else { 
      newQues.testRunnerFileContent = "";
    }

    newQues.testCase = [];
    if ( payload.txtInputParams && payload.txtExpectedOutput ) {
        newQues.testCase.push({
            'description': payload.txtInputParams.trim().replace(/[\r]/g, ''),
            'evaluator': payload.txtExpectedOutput.trim().replace(/[\r]/g, ''),
            'scoreip': payload.scoreip || 0,
            'sampleTest': payload.sampleTest ? true : false
        })
        for (let i = 1; i <= libs.constants.maximumTestCases; i += 1) {
            if ( ! ( Object.prototype.hasOwnProperty.call(payload, 'txtInputParams' + i) && Object.prototype.hasOwnProperty.call(payload, 'txtExpectedOutput' + i) ) )    break;
            newQues.testCase.push({
                'description': payload['txtInputParams' + i].trim().replace(/[\r]/g, ''),
                'evaluator': payload['txtExpectedOutput' + i].trim().replace(/[\r]/g, ''),
                'scoreip': payload['scoreip' + i],
                'sampleTest': payload['sampleTest' + i] ? true : false
            });
        }
    } else if (payload.txtInputParams) {
        newQues.testCase.push({
            'description': payload.txtInputParams.trim().replace(/[\r]/g, ''),
            'evaluator': '',
            'scoreip': payload.scoreip || 0,
            'sampleTest': payload.sampleTest ? true : false
        })
        for (let i = 1; i <= libs.constants.maximumTestCases; i += 1) {
            if (Object.prototype.hasOwnProperty.call(payload, 'txtInputParams' + i) && Object.prototype.hasOwnProperty.call(payload, 'txtExpectedOutput' + i)) {
                newQues.testCase.push({
                    'description': payload['txtInputParams' + i].trim().replace(/[\r]/g, ''),
                    'evaluator': '',
                    'scoreip': payload['scoreip' + i],
                    'sampleTest': payload['sampleTest' + i] ? true : false
                });
            }
        }
    }
    return true;
};
var setModelValuesSubjective = function (payload, newQues) {
    if (Object.prototype.hasOwnProperty.call(payload, 'txtSub')) {
        newQues.subjectiveanswer = payload.txtSub;
        newQues.isFileUpload = parseInt(payload.isFileUpload) ? true : false;
        return true;
    }
    logger.debug("txtSub not present ", payload);
    logger.error("txtSub not present");
    return false;
};

var setModelValues = function (payload, type, newQues, oldQuesObj) {
    if ( ! ( payload && type && newQues ) ) {
        logger.debug("setmodelvalues param are undefined", "payload-", payload, "type-", type, "newQues-", newQues);
        logger.error("setmodelvalues param are undefined");
        return false;
    }

    switch (type) {
        case libs.constants.questionTypeNumeric.questionTypeMCQ:
            return setModelValuesMCQ(payload, newQues);
        case libs.constants.questionTypeNumeric.questionTypeSubjective:
            return setModelValuesSubjective(payload, newQues);
        case libs.constants.questionTypeNumeric.questionTypeCoding:
        case libs.constants.questionTypeNumeric.questionTypeStepwise:
            return setModelValuesCoding(payload, newQues, oldQuesObj);
        case libs.constants.questionTypeNumeric.questionTypeWeb:
            return setModelValuesWeb(payload, newQues);
        default:
            console.log("Question Type is invalid in setModelValues, Type = ", type);
            return false;
    }
};

var setQuestionTypeId = function (newQues, type, questionTypeObj) {
    newQues.questionTypeCoding = newQues.questionTypeSubjective = 
        newQues.questionTypeMCQ = newQues.questionTypeWeb = null;
    if (type === libs.constants.questionTypeNumeric.questionTypeMCQ) {
        newQues.questionTypeMCQ = questionTypeObj;
    }
    else if (type === libs.constants.questionTypeNumeric.questionTypeSubjective) {
        newQues.questionTypeSubjective = questionTypeObj; // nedd to change afterwards
    }
    else if (type === libs.constants.questionTypeNumeric.questionTypeCoding ||
        type === libs.constants.questionTypeNumeric.questionTypeStepwise) {
        newQues.questionTypeCoding = questionTypeObj;
    }
    else if (type === libs.constants.questionTypeNumeric.questionTypeWeb ) {
        newQues.questionTypeWeb = questionTypeObj;
    }
    else {
        console.log("invalid model");
        return undefined;
    }
};

var createQuestionObject = function (payload, newQues, questionTypeObj, type, oldQuesObj) {
    
    if (!payload || !newQues || !questionTypeObj || !type ||
        !Object.prototype.hasOwnProperty.call(payload, 'type') ||
        !Object.prototype.hasOwnProperty.call(payload, 'txtQues') ||
        !Object.prototype.hasOwnProperty.call(payload, 'score') ||
        !Object.prototype.hasOwnProperty.call(payload, 'negativeScore') ||
        !Object.prototype.hasOwnProperty.call(payload, 'txtQuesTitle') ||
        !Object.prototype.hasOwnProperty.call(payload, 'txttag')) {
        logger.debug(" payload does not have all mandatory properties :createQuestionObject", payload, "type-", type, "newQues-", newQues, "questionTypeObj-", questionTypeObj);
        logger.error(" payload does not have all mandatory properties :createQuestionObject");
        return false;
    }
    if ( payload.quesId )   newQues._id = mongoose.Types.ObjectId( payload.quesId );
    newQues.type = payload.type;
    newQues.score = parseInt(payload.score);
    newQues.negativeScore = payload.negativeScore;
    newQues.text = payload.txtQues;
    newQues.title = payload.txtQuesTitle.trim();
    newQues.tags = payload.txttag;
    if( payload.isCqDocument )newQues.isCqDocument = true ;
    if( payload.files &&  payload.files.length ){
        newQues.files = [];
        payload.files.forEach( ( fileObj)=>{
            if( fileObj.filename.indexOf(' ') === -1 ) {
                newQues.files.push( fileObj.filename );
                if ( fileObj.path )     services.RedisServices.redis("hdel", libs.constants.redisKeys.filesMd5Hash, fileObj.path);
            }
            else{
                logger.debug(" file name contain spaces Question--not allowed---", fileObj.filename);
                return false;
            }
        })
    }
    if ( oldQuesObj && oldQuesObj.files && oldQuesObj.files.length ) {
        if ( ! ( newQues.files && newQues.files.length ) )  newQues.files = oldQuesObj.files;
        else {
            oldQuesObj.files.map(filePath => {
                if ( newQues.files.indexOf(filePath) == -1 )    newQues.files.push(filePath);
            })
        }
    }
    newQues.askconf = ( payload.askConf === 'on' || payload.askConf === 1
        || payload.askConf === '1' ) ? 1 : 0;

    newQues.explanation = payload.txtExpl;
    newQues.hint = payload.txtHint;
    newQues.showhint = payload.isShowhint;
    newQues.referenceLinks = payload.referenceLinks;
    newQues.difficultyLevel = payload.difficultyLevel;
    newQues.executionTime = payload.executionTime ? payload.executionTime : 15;
    if ( payload.subType )      newQues.subType = payload.subType;
    newQues.isPremium = Boolean(parseInt(payload.isPremium));

    if (payload.bloomTaxonomy) {
        newQues.bloomTaxonomy = payload.bloomTaxonomy;
    }
    if (payload.courseOutcomes) {
        newQues.courseOutcomes = payload.courseOutcomes;
    }
    if (payload.topic) {
        newQues.topic = payload.topic;
    }
    setQuestionTypeId(newQues, type, questionTypeObj);
    return libs.validation.validateQuestionData(newQues, false);
};

var updateQuestion = async function (id, req, callback) {
    const isCB = callback && typeof callback === 'function';
    const payload = req.body;
    try{
        let ques = await getQuestionFromId( id );
        if(!ques) throw new Error(`Can't Find Question`);

        if(!libs.util.checkPermissionForContentModification(ques,req.session))
            throw new Error(`Dont have permission to update question`);

        payload.type = ques.type;
        if ( ques.isLocked ) {
            throw new Error('Locked questions edit not allowed.');
        }else {
            const result = await updateQuestionOnly(payload, ques)
            const user   = await services.UserService.getOneUser({'_id': result.createdBy}, {'role': 1}, {});
            if ( user && user.role ) {
                let isAdmin = user.role == libs.constants.roleNumberFromString.admin ||
                              user.role == libs.constants.roleNumberFromString.subAdmin ||
                              user.role == libs.constants.roleNumberFromString.superOrg;

                let adminId = result.orgId;
                const resultAddTag = await tagController.addTag(libs.constants.constantString.constantStringTagTypeQuestion,
                    { 'tagTypeId': result._id, 'tags': result.tags, 'adminId': adminId }, 
                );
                if(isCB)    return  callback(null,resultAddTag);
                return result;
            }
            else {
                throw new Error('Error Updating Question- user is null');
            }
        }
    }
    catch(err){
        console.log(err?.message ?? err);
        if(isCB)    return callback({error: err?.message ?? err})
        throw new Error(err?.message ?? err)
    }
};

var updateQuestionOnly = function (payload, quesObj, callback) {
    var questionTypeObj = {};
    let quesId = quesObj && quesObj._id;
    
    if (!setModelValues(payload, payload.type, questionTypeObj, quesObj)) {
        callback({ 'error' : "invalid payload" }, null);
        return;
    }
    var newQues = {};
    if (!quesId) {
        logger.error(" question id is not valid ", quesId);
        callback({ 'error' : " question id is not valid"}, null);
        return;
    }
    if (!createQuestionObject(payload, newQues, questionTypeObj, payload.type, quesObj)) {
        callback({ 'error' : "invalid field in payload "}, null);
        return;
    }

    if (quesObj.type === libs.constants.questionTypeNumeric.questionTypeCoding) {
        let previousCodeProgrammingLang = {};
        (( quesObj.questionTypeCoding && quesObj.questionTypeCoding.codeproglang ) || []).forEach((element) => {
            previousCodeProgrammingLang[element.language] = element.codeComponents.solution;
        });
        (newQues?.questionTypeCoding?.codeproglang ?? []).forEach((element) => {
            if (previousCodeProgrammingLang[element.language]) {
                element.codeComponents.solution = previousCodeProgrammingLang[element.language];
            }
        })
    }

    newQues.updatedBy = payload.userId;
    return services.QuestionService.updateQuestion({_id: quesId}, {$set: newQues}, {}, callback);
};

const createQuestionObjToSave = (payload, session) => {
    let newQues = {};
    var questionTypeObj = {};

    if (!setModelValues(payload, payload.type, questionTypeObj)) {
        throw new Error("invalid payload");
    }

    if (!createQuestionObject(payload, newQues, questionTypeObj, payload.type)) {
        throw new Error("invalid filed in payload");
    }
    newQues.createdBy = session.userId;
    if( session.parentId )  newQues.parentIdOfCreator = session.parentId;
    newQues.orgId = session.orgId;
    return newQues;
}

var addQuestion = function (payload, session) {
    const newQues = createQuestionObjToSave(payload, session);
   return services.QuestionService.addQuestion(newQues);
};

var addQuestionInDb = async (payload, session) => {
    const result = await addQuestion(payload, session);
    const isAdminRole = session.role == libs.constants.roleNumberFromString.admin ||
                    session.role == libs.constants.roleNumberFromString.subAdmin ||
                    session.role == libs.constants.roleNumberFromString.superOrg;
    const adminId = isAdminRole ? session.userId : session.parentId;
    await tagController.addTag(libs.constants.constantString.constantStringTagTypeQuestion,
        {'tagTypeId': result._id, 'tags': result.tags, 'adminId': adminId});
    return result;
};

const getQuestionsFromId = async (ids, callback) => {
    const isCB = callback && typeof callback === 'function';
    if (!ids.length) {
        if (isCB) return callback(null, []);
        return [];
    }
    try {
        const questions = await services.QuestionService.getQuestion({_id: ids}, {}, {});
        if (!questions.length) {
            return [];
        }
        const setOfUserToGet = new Set();
        questions.forEach(question => setOfUserToGet.add(question.createdBy.toString()));
        const users = await services.UserService.getUser({_id: Array.from(setOfUserToGet)}, {displayname: 1, profileLink: 1}, {});
        const userMap = new Map();
        (users ?? []).forEach(user => userMap.set(user._id.toString(), user));
        questions.forEach((question) => {
            const user = userMap.get(question.createdBy.toString());
            question.createdBy = question.createdBy || {};
            question.createdBy.displayname = user && ( user.displayname || libs.constants.defaultDisplayName ); 
            question.createdBy.profileLink = user && ( user.profileLink || libs.constants.defaultProfileLink );
        });
        return questions;
    } catch (error) {
        console.log(error);
        if (isCB) return callback(error?.message);
        throw error;
    }
}

const getQuestionFromId = async (id, callback) => {
    const isCB = callback && typeof callback === 'function';
    if (!id) {
        if (isCB) return callback("Id is required");
        else throw Error('Id is required');
    }
    try{
        const question = await services.QuestionService.getOneQuestion({_id: mongoose.Types.ObjectId(id)}, {}, {});
        if( !question ){
            logger.debug(`error in finding question with id ${id}`);
            logger.error(`error in finding question getQuestionFromId`);
            throw new Error("Question not found");
        }
        let user = await services.UserService.getOneUser({_id: question.createdBy}, {displayname: 1, profileLink: 1}, {});
        question.createdBy = question.createdBy || {};
        question.createdBy.displayname = user && ( user.displayname || libs.constants.defaultDisplayName ); 
        question.createdBy.profileLink = user && ( user.profileLink || libs.constants.defaultProfileLink );
        if(isCB) return callback(null,question );
        else return question;
    }
    catch(err){
        if(isCB) callback({error:err?.message ?? err});
        else throw new Error(err?.message ?? err);
    }
};

var deleteQuestion = async function (id, sessionObj, callback) {
    let userId = sessionObj.userId;
    id = (Array.isArray(id))?id:[id];
    const isCB = callback && typeof callback === 'function';
    try {
        const validQuestions = [];

        const ques = await getQuestionsFromId(id);
        if(!ques) {
            throw new Error('Invalid question id');
        }
        ques.forEach((question) => {
            try {
                if(!libs.util.checkPermissionForContentModification(question, sessionObj)) throw new Error('Not Authorized');
                validQuestions.push(question._id.toString());
            } catch (error) {
                console.log(`Error while deleting question Error = `, error);
            }
        })
        await services.QuestionService.updateQuestions(
            {_id: validQuestions},
            {$set: {'displaystatus': libs.constants.displayStatus.deleted, 'courseId': [], 'deletedBy' : userId } },
            {},
        )
        await services.RedisServices.redis('del',...id);
        return validQuestions;
    } catch (error) {
        if(isCB) return callback(error,null);
        throw error;
    }
};

var getListWithPagination = function (req, callback) {
    if(req.body.fltrCreatedBy){
        for( var ind = 0 ; ind < req.body.fltrCreatedBy.length ; ind++){
            if( req.body.fltrCreatedBy[ind] === req.session.userId ){
                req.body.fltrCreatedByMe = true ;
            }
            req.body.fltrCreatedBy[ind] = mongoose.Types.ObjectId( req.body.fltrCreatedBy[ind] ) ;
        }
    }
    if (req.session.role == libs.constants.roleNumberFromString.superOrg && req.body.myOrgList) {
        req.body.orgIdsArray = [...req.session.orgIdsAssigned ?? [], req.session.orgId]
    }
    if (
        req.session.role == libs.constants.roleNumberFromString.custom
        &&
        req.session.roleObj.containsOrg
        &&
        req.body.myOrgList
    ) {
        req.body.orgIdsArray = [...req.session.orgIdsAssigned ?? []]
    }
    if (req.body.superOrgList && req.session.isSubOrg) {
        req.body.orgIdsArray = req.session.superOrgIds ?? [];
    }

    if (!req.body.myList) {
        if (
            !(
                req.session.role === libs.constants.roleNumberFromString.admin
                ||
                req.session.role === libs.constants.roleNumberFromString.superOrg
                ||
                (
                    req.session.role === libs.constants.roleNumberFromString.custom
                    &&
                    req.session.roleObj.containsOrg
                )
            )
        ) {
            req.body.isPublic = true;
        }
    }
    req.body.userId = req.session.userId;
    req.body.orgId = req.session.orgId;
    req.body.role = req.session.role ;
    req.body.superAdminId = global.superAdminId;
    req.body.isCq = req.session.orgId == global.superAdminId;
    req.body.isPremiumUser = req.session.isPremium;
    if (!req.body.quizId)    req.body.languagesAllowed = req.session.languagesAllowed;
    return services.PaginationServices.serverSidePagination(req, libs.constants.modelForPagination.Questions,callback);
};

var uploadFileToQuestion = function ( req, res, questionId, callback ) {
    let targetfilename = '';
    const storage = multer.diskStorage({
        destination(req, file, cb) {
            cb(null, './Public/uplaods/Questions/');
        },
        filename(req, file, cb) {
            if( questionId)
            targetfilename = `${questionId}`;
            else
                targetfilename = `${file.originalname}`
            cb(null, targetfilename);
        },
    });
    const upload = multer({
        storage
    }).single('upldFile');
    // res.end("File is uploaded");
    upload(req, res, (err) => {
        logger.error(`UPLOAD - ${req}`);
        if (err) {
            logger.debug(`File not Uploaded  uploadFileToQuestion`,err);
            logger.error(`File not Uploaded uploadFileToQuestion`);
            req.flash('error', 'Error! File not uploaded. Try again');
            callback( 'Error! File not uploaded. Try again' );
            return;
        }
        logger.error(`File Uploaded and the path is : ${storage}  ${targetfilename}`);
        callback()
    });
}

var updateQuestionAccessStatus = async function( payload  ){
    if( !( payload && payload.questionId && payload.userId && payload.role) ) {
        logger.debug("Invalid payload updateQuestionAccessStatus ", payload);
        logger.error("Invalid payload updateQuestionAccessStatus");
        throw new Error('Invalid Payload');
    }
    let updateQuery = {'_id': payload.questionId};
    

    if (payload.role !== libs.constants.roleNumberFromString.admin) {
        updateQuery.$or = [
                {'parentIdOfCreator' : new mongoose.Types.ObjectId(payload.userId)},
                {'createdBy'         : new mongoose.Types.ObjectId(payload.userId)}
            ]
    }
    const question = await updateOneQuestion( updateQuery , { $set : { 'isPublic' : payload.isPublic ? true : false } } , {});
    if(!question) throw new Error('Error while changing status');
    return ;
};

var cloneQuestion = async function( questId, questObj, session, callback){
    const isCB = callback && typeof callback === 'function';
    if(questObj){
        let oldQuestionId = questObj._id;
        if(questObj._id)   delete questObj._id;
        if ( questObj.createdAt )    delete questObj.createdAt;
        if ( questObj.updatedAt )    delete questObj.updatedAt;
        questObj.createdBy = session.userId;
        questObj.title = questObj.title+" Clone";
        questObj._id = mongoose.Types.ObjectId();
        
        if (session.parentId)
            questObj.parentIdOfCreator = session.parentId;
	    let isCqDoc = session.role == libs.constants.roleNumberFromString.admin || session.role == libs.constants.roleNumberFromString.contentCreator;
	    if ( isCqDoc )          questObj.isCqDocument = true;
	    else delete questObj.isCqDocument;
	    delete questObj.isPublic;
        delete questObj.isLocked;
        delete questObj.isPremium;
	    questObj.orgId = session.orgId;
        if (questObj.type === libs.constants.questionTypeNumeric.questionTypeCoding) {
            const copyFilesPromiseArray = [];
            questObj.questionTypeCoding.testCase.forEach((ele) => {
                const oldTestCaseId = ele._id;
                const newId = mongoose.Types.ObjectId();
                ele._id = newId;
                if (ele.onCloud) {
                    const oldFilePath = libs.constants.awsS3Constants.getTestCaseStoragePath(oldQuestionId, oldTestCaseId);
                    const newFilePath = libs.constants.awsS3Constants.getTestCaseStoragePath(questObj._id, ele._id);
                    copyFilesPromiseArray.push(
                        AwsSpaceController.copyFile(oldFilePath, newFilePath, {
                            sameBucket: true,
                        })
                    )
                }
            });
            if (copyFilesPromiseArray.length) {
                await Promise.all(copyFilesPromiseArray);
            }
            // questObj.questionTypeCoding.testCase = await handleTestCaseSaveToCloud(questObj, questObj.questionTypeCoding.testCase);
        }
        return services.QuestionService.addQuestion(questObj, callback);
    } else {
        if(isCB) return callback("question Id is required", null);
        throw new Error('question Id is required', null);
    }
};

const populateCloudTestCase = async (questObj) => {
    const promiseArray = [];
    try {
        questObj.questionTypeCoding.testCase.forEach((testCase) => {
            if (testCase.onCloud) {
                promiseArray.push((async () => {
                    const fileContent = libs.constants.awsS3Constants.getTestCaseStoragePath(questObj._id, testCase._id);
                    const content = await AwsSpaceController.getContent(fileContent);
                    testCase.codeproginputparams = content;
                })());
            }
        });
    } catch (error) {
        console.error(error);
    }
    await Promise.all(promiseArray);
    return questObj.questionTypeCoding.testCase;
} 

/**
 * 
 * @param {{questionId: string, sessionObj: {[key: string]: any}}} param0 
 */
const exportQuestionWithQuestionId = async ({questionId, sessionObj}) => {
    try {
        let questObj = await getOneQuestion({ '_id': questionId }, {}, {})
        if (questObj.contentUrl) {
            const result = await axios.get(questObj.contentUrl);
            questObj.description = result;
        }

        if ( ! questObj )   throw new Error("Question not found");

        if (questObj.createdAt) delete questObj.createdAt;
        if (questObj.updatedAt) delete questObj.updatedAt;
        if (questObj.type === libs.constants.questionTypeNumeric.questionTypeCoding) {
            await populateCloudTestCase(questObj);
        }

        let isAdminRole = sessionObj.role == libs.constants.roleNumberFromString.admin ||
            sessionObj.role == libs.constants.roleNumberFromString.subAdmin;
        questObj.adminId = isAdminRole ? sessionObj.userId : sessionObj.parentId;
        let url = `${libs.util.getExportQuestionUrl()}/api/quest/exportQuestion`;
        const response = await axios.post(url, questObj);
        return  (response && response.data) ? response.data : { msg : "No response from the receipient"};
    } catch (e) {
        logger.debug("Error in exporting a question", e, questionId);
        return { 'error': 'error in exporting question' };
    }
};

const exportQuestion = async function (req, res ) {
    try {
        let questId = req.params.id, session = req.session;
        const response = await exportQuestionWithQuestionId({questionId: questId, sessionObj: req.session})
        res.json( (response && response.data) ? response.data : { msg : "No response from the receipient"})
    } catch (e) {
        logger.debug("Error in exporting a question", e, req.params);
        res.json({ 'error': 'error in exporting question' });
        return;
    }

};

const saveExportedQuestion = async function (req, res ) {
    try {
        let payload = req.body, session;
        let { _id } = payload || {}
        let { adminId, ...data } = payload || {}

        if( !_id ){
            logger.debug("Question id not present", data );
            res.json({ 'error': "Question id not present" });
            return;
        }

        let questObj = await getOneQuestion({ _id }, { _id : 1, displaystatus : 1 }, {})

        console.log(" export question ", data);
        if (questObj) {
            let updatedQuestion = await services.QuestionService.updateQuestion( 
                { _id }, { $set :  data  }, {} )
            services.RedisServices.redis('del', libs.constants.redisKeys.questionString + ":" + _id.toString());

        }else {
            let newQuestion = await services.QuestionService.addQuestion( data );
        }
        if (data.files?.length) {
            const promiseArray = [];
            const basePath = path.join(__dirname, `../Public/uploads/Questions`);
            await fse.ensureDir(basePath);
            data.files.forEach((file) => {
                promiseArray.push(
                    new Promise(async (resolve, reject) => {
                        try {
                            let isResolved = false;
                            const writer = fse.createWriteStream(path.join(basePath, file));
                            const response = await axios.get(`http://${process.env.EXPORT_QUESTION_LINK}/uploads/Questions/${data._id}/${file}`, {
                                responseType: 'stream'
                            });
                            response.data.pipe(writer);
                            writer.on('error', (error) => {
                                isResolved = true;
                                writer.close();
                                reject(error);
                            })
                            writer.on('close', () => {
                                if (!isResolved) {
                                    writer.close();
                                    resolve();
                                }
                            })
                        } catch (error) {
                            reject('Unable to export file');
                        }

                    })
                )
            })
            await Promise.all(promiseArray);
            console.log(data.files);
        }
        res.json({ msg : `Question successfully exported to ${global.serverAddress}` });
        tagController.addTag(libs.constants.constantString.constantStringTagTypeQuestion,
            {'tagTypeId': _id, 'tags': data.tags, adminId});
    } catch (e) {
        logger.debug("Error in exporting a question", e, req.params);
        res.json({ 'error': 'error in exporting question' });
        return;
    }

};
var cloneQuestionOfQuiz = function( indexOfQuest, questObj, session, callback){
    if(questObj){
        if(questObj._id)   delete questObj._id;
        if ( questObj.createdAt )    delete questObj.createdAt;
        if ( questObj.updatedAt )    delete questObj.updatedAt;
        questObj.createdBy = session.userId;
        questObj.tags = 'testclone , set-b';
        questObj.title = "MCQ-"+indexOfQuest;
        if (session.parentId)
            questObj.parentIdOfCreator = session.parentId;
	delete questObj.isPublic;
	    let isCqDoc = session.role == libs.constants.roleNumberFromString.admin || session.role == libs.constants.roleNumberFromString.contentCreator;
	    if ( isCqDoc )          questObj.isCqDocument = true;
	    else delete questObj.isCqDocument;
        services.QuestionService.addQuestion(questObj, callback);
    } else {
        callback("question Id is required", null);
    }
};

var removeQuestionFromServersData = function (payload = {}) {
    let { questionId } = payload;
    let { processMapName,type } = libs.constants.cachingKeys.question
    if (!questionId) {
        console.log("Question id not present", payload);
        return;
    }
    let obj = JSON.stringify({ id : questionId,type })
    indexController.removeDataFromRAM(obj);
    services.RedisServices.redis('publish', libs.constants.redisKeys.ramUpdateChannel, obj)
}

const addTestCaseInCodingQuestion = async function(payload, sessionObj) {
    let {quesId, testId, input, output, score, attemptInMultiLine, sampleTest} = payload;
    if ( ! quesId )     throw new Error("Question is null");

    let findObj = {
        _id: quesId,
        type: libs.constants.questionTypeNumeric.questionTypeCoding,
    }
    if ( testId ) {
        testId = mongoose.Types.ObjectId(testId)
        findObj['questionTypeCoding.testCase._id'] = testId;
    }

    let quesObj = await getOneQuestion(findObj, {_id: 1, questionTypeCoding: 1,createdBy:1 ,parentIdOfCreator:1, orgId: 1  }, {});
    if ( ! quesObj )    throw new Error("Ques not exist");

    if(!libs.util.checkPermissionForContentModification(quesObj,sessionObj)){
        throw new Error('Dont have permission to modify');
    }

    let testCaseObj = {
        '_id': testId || mongoose.Types.ObjectId(),
        'codeproginputparams': input,
        'codeprogexpectedoutput': output,
        'scoreip': score,
        'attemptInMultiLine': attemptInMultiLine ? true : false,
        'sampleTest': sampleTest,
    }
    if ( libs.util.checkIfShouldInputGoToCloud(input) ) {
        testCaseObj['codeproginputparams'] = input.slice(0, 100) + '...';
        testCaseObj['onCloud']  = true;
        let testCasePath = path.join(libs.constants.awsS3Constants.getTestCaseStoragePath(quesId, testCaseObj._id.toString())).replace(/\\/g,'/');
        console.log("TestCasePath = ", testCasePath);
        await AwsSpaceController.putFile(testCasePath, {
            content: input,
            ContentType: 'input/text',
            publicRead: false,
        });
    }
    const outputSizeInKb = Buffer.from(output).length / 1_000;
    if (outputSizeInKb > 51) {
        throw new Error('Output size of test case should not exceed 50kb');
    }
    let updateObj = {};
    let scoreToIncr = parseInt(score) || 0;
    let testCaseArr = ( quesObj.questionTypeCoding && quesObj.questionTypeCoding.testCase ) || [];
    let totalScore = 0;
    for (let index = 0; index < testCaseArr.length; index++) {
        let testObj = testCaseArr[index];
        if ( testObj && testObj._id ) {
            if (testId && testObj._id.toString() == testId.toString())    testObj.scoreip = scoreToIncr;
            totalScore += parseInt(testObj.scoreip) || 0;
        }
    }
    
    if(totalScore  > libs.constants.quizScoreUpperLimit){
        throw new Error('Invalid Score,Total question score cannot be higher than 9999');
    }

    if ( testId ) {
        updateObj = { $set: { 'questionTypeCoding.testCase.$': testCaseObj, 'score': totalScore } };
    }
    else {
        testId = testCaseObj._id;
        updateObj = { $push: {'questionTypeCoding.testCase': testCaseObj } };
        totalScore += scoreToIncr;
        updateObj['$set'] = { 'score': totalScore };
    }
    quesObj = await updateOneQuestion(findObj, updateObj,  {});
    return {msg: 'success', quesObj, testId};
}

const removeTestCaseFromCodingQuestion = async function(payload, sessionObj) {
    let {quesId, testId} = payload;
    if ( ! quesId )     throw new Error("Question Id is null");
    if ( ! testId )     throw new Error("Test Id is null");

    let findObj = {
        _id: mongoose.Types.ObjectId(quesId),
        'questionTypeCoding.testCase._id': mongoose.Types.ObjectId(testId),
        type: libs.constants.questionTypeNumeric.questionTypeCoding,
    }

    let quesObj = await getOneQuestion(findObj, {questionTypeCoding: 1, createdBy: 1, parentIdOfCreator: 1, orgId: 1}, {});
    if ( ! quesObj )    throw new Error("Ques or test case not exist");
    if(!libs.util.checkPermissionForContentModification(quesObj,sessionObj))    throw new Error('Dont have permission to modify.');

    let scoreToDecr = 0;

    let testCaseArr = ( quesObj.questionTypeCoding && quesObj.questionTypeCoding.testCase ) || [];
    for (let index = 0; index < testCaseArr.length; index++) {
        let testCaseObj = testCaseArr[index];
        if ( testCaseObj._id && testCaseObj._id.toString() == testId ) {
            if ( testCaseObj.onCloud ) {
                let testCasePath = path.join(libs.constants.awsS3Constants.getTestCaseStoragePath(quesId, testId)).replace(/\\/g,'/');
                console.log("TestCasePath = ", testCasePath);
                await AwsSpaceController.deleteObjects(testCasePath);
            }
            if (typeof(testCaseObj.scoreip) != 'number')  testCaseObj.scoreip = parseInt(testCaseObj.scoreip);        // parseInt fails if number of exponent form like parseInt(1e+74) returns 1
            scoreToDecr = ( -1 * testCaseObj.scoreip ) || 0;
            break;
        }        
    }
    let updateObj = {
        $pull: {
            'questionTypeCoding.testCase': {
                _id: mongoose.Types.ObjectId(testId)
            }
        },
        $inc: { 'score': scoreToDecr },
    }

    quesObj = await updateOneQuestion({_id: mongoose.Types.ObjectId(quesId)}, updateObj, {});
    return {msg: 'deleted', quesObj};
}

const getInputOfTestCaseInCodingQuestion = async function(payload) {
    let {quesId, testId} = payload;
    if ( ! quesId )     throw new Error("Question is null");

    let findObj = {
        _id: quesId,
        type: libs.constants.questionTypeNumeric.questionTypeCoding,
    }
    let quesObj = await getOneQuestion(findObj, {_id: 1, questionTypeCoding: 1}, {});
    if ( ! quesObj )    throw new Error("Ques not exist");

    let testCasesArray = ( quesObj.questionTypeCoding && quesObj.questionTypeCoding.testCase ) || [];
    let testCaseObj;
    for (let index = 0; index < testCasesArray.length; index++) {
        const element = testCasesArray[index];
        if ( element && element._id && element._id.toString() == testId ) {
            testCaseObj = element;
            break;
        }
    }

    if ( ! testCaseObj )    throw new Error("Test case obj is bot found in question");

    let input = testCaseObj.codeproginputparams;
    if ( testCaseObj.onCloud == true ) {
        let testCasePath = path.join(libs.constants.awsS3Constants.getTestCaseStoragePath(quesId, testId)).replace(/\\/g,'/');
        console.log("TestCasePath = ", testCasePath);
        input = await AwsS3Controller.getFile(testCasePath);
    }
    return input
}

const updateSolution = async (payload, sessionObj) =>{
    let quesId = null;
    let langCode = null;
    let solution = null;
    let multiLanguage = null;
    let isSingleLang = false;
    if (!(payload && typeof payload == 'object')) {
        throw new Error('Payload is not valid');
    }
    quesId = payload.quesId;
    if (!quesId) {
        throw new Error('Question Id is missing');
    }
    if ('langCode' in payload) {
        isSingleLang = true;
        langCode = payload.langCode;
    }
    if (isSingleLang) {
        if (!('solution' in payload)) {
            throw new Error('Payload is not valid solution is missing');
        }
        solution = payload.solution;
    }

    if (!isSingleLang ) {
        if (!('multiLanguage' in payload)) {
            throw new Error('Payload is not valid');
        }
        multiLanguage = new Map();
        Object.entries(payload.multiLanguage).forEach(([lang, sol]) => {
            if (lang) {
                multiLanguage.set(lang, sol);
            }
        })
    }

    let findObj = {
        _id: quesId,
        type: libs.constants.questionTypeNumeric.questionTypeCoding,
    }

    let quesObj = await getOneQuestion(findObj, {_id: 1, questionTypeCoding: 1, parentIdOfCreator: 1, createdBy: 1, orgId: 1 }, {});
    if(!quesObj) throw new Error("Ques not exist");

    if( !libs.util.checkPermissionForContentModification(quesObj,sessionObj) ){
        throw new Error('Dont have permission to modify');
    }

    const bulkWriteArray = [];

    let codeproglangArr = ( quesObj.questionTypeCoding && quesObj.questionTypeCoding.codeproglang ) || [];
    for(let i =0 ;i<codeproglangArr.length;i++){
        let toBeUpdated = false;
        const codeprogObj = codeproglangArr[i];
        if (langCode) {
            if(codeprogObj.language == langCode){
                codeprogObj.codeComponents.solution = solution;
                toBeUpdated = true;
            }
        }
        if (multiLanguage) {
            if(multiLanguage.has(codeprogObj.language)) {
                toBeUpdated = true;
                codeprogObj.codeComponents.solution = multiLanguage.get(codeprogObj.language);
            }

        }
        if (toBeUpdated) {
            bulkWriteArray.push({
                updateOne: {
                    filter: {
                        _id: quesId,
                        type: libs.constants.questionTypeNumeric.questionTypeCoding,
                        ['questionTypeCoding.codeproglang.language']: codeprogObj.language,
                    },
                    update: {
                        $set: {
                            'questionTypeCoding.codeproglang.$': codeprogObj
                        }
                    }
                }
            })
            if (isSingleLang) {
                break;
            }
        }
    }

    const response = await services.QuestionService.bulkWrite(bulkWriteArray, {ordered: false}); 
    return {msg:'updated'};
}

const removeFileFromQuestion = async (payload) => {
    const {quesId, fileName} = payload;
    if ( ! quesId )     throw new Error("QuestionId is null");
    if ( ! fileName )   throw new Error("fileName is null");

    const quesObj = await updateOneQuestion({_id: quesId}, {$pull: {files: fileName}}, {});
    if ( ! ( quesObj && quesObj._id ) )     throw new Error("Question file not removed");

    const filePath = path.join(libs.constants.questionFileUploadPath, fileName);
    await fse.remove(filePath);
    return {msg: "success"};
}

const updateIsLockedInQuestion = async (payload) => {
    try {
        let {quesId, isLocked} = payload;
        if ( ! quesId )     throw new Error("QuesId is null");

        isLocked = Boolean(parseInt(isLocked));
        const updateObj = {$set: {isLocked}};
        if ( ! isLocked )   updateObj['$set']['isPublic'] = false;
        const quesObj = await updateOneQuestion({_id: quesId}, updateObj, {});
        if ( ! ( quesObj && quesObj._id ) )     throw new Error("Question lock not updated");

        return {msg: "success"};
    }
    catch (error) {
        console.log("ERror = ", error);
        return {error: error.message};
    }
}

const checkClonePermission = async (questObj, sessionObj) => {
    let adminId = await  libs.util.getSuperAdminIdFromRedis();
    if (sessionObj.isSubOrg && sessionObj.superOrgIds.indexOf(questObj.orgId.toString()) !== -1 && questObj.isPublic ) {
        return true;
    }
    if( sessionObj.role === libs.constants.roleNumberFromString.admin || questObj.createdBy.toString() === adminId ){
        return true;
    } else if (questObj.createdBy.toString() === sessionObj.userId ) {
        return true;
    } else if (
        sessionObj.role == libs.constants.roleNumberFromString.superOrg
        && (
            (sessionObj.orgIdsAssigned ?? []).indexOf(questObj.orgId.toString()) !== -1 || sessionObj.orgId === questObj.orgId.toString()
        )
    ) {
        return true;
    }
    else {
        return questObj.isPublic && (questObj.isCqDocument || questObj.orgId.toString() == sessionObj.orgId );
    }
}

const bulkWrite = (bulkWriteArray, config = {}) => {
    return services.QuestionService.bulkWrite(bulkWriteArray, config);
}

const getOneTestCase = async (questionId, testCaseId) => {
    try {
        const questionAggrigation = [
            {
                $match: {
                    _id: mongoose.Types.ObjectId(questionId),
                },
            },
            {
                $addFields: {
                    testCase: {
                        $filter: {
                            input: "$questionTypeCoding.testCase",
                            as: "testCase",
                            cond: {
                                $eq: ["$$testCase._id", mongoose.Types.ObjectId(testCaseId)],
                            }
                        }
                    }
                },
            }
        ];
        const aggResult = await services.QuestionService.aggregiation(questionAggrigation);
        console.log(aggResult);
        return aggResult;
    } catch (error) {
        console.error(error);
    }
}

const handleTestCaseSaveToCloud = async (questionObj, testCases) => {
    const questionId = questionObj._id;
    if (!questionId) {
        throw new Error("QuestionId not defined");
    }
    if (questionObj.type !== libs.constants.questionTypeNumeric.questionTypeCoding) {
        return [];
    }
    for (let testCase of testCases) {
        const shouldGoToCloud = libs.util.checkIfShouldInputGoToCloud(testCase.codeproginputparams);
        if (shouldGoToCloud) {
            const content = testCase.codeproginputparams;
            testCase['codeproginputparams'] = testCase.codeproginputparams?.slice(0, 100) + '...';
            const savePath = libs.constants.awsS3Constants.getTestCaseStoragePath(questionId, testCase._id);
            testCase['onCloud']  = true;
            await AwsSpaceController.putFile(savePath, {
                content: content,
                ContentType: "text/plain",
                publicRead: false,
            })
        }
    }
    return testCases;
}

module.exports = {
    addQuestion: addQuestion,
    getQuestionFromId: getQuestionFromId,
    getQuestionsFromId: getQuestionsFromId,
    getQuestion: getQuestion,
    updateQuestion: updateQuestion,
    bulkWrite,
    updateOneQuestion: updateOneQuestion,
    updateQuestions: updateQuestions,
    deleteQuestion: deleteQuestion,
    addQuestionInDb: addQuestionInDb,
    getQuestionTypeProperties: getQuestionTypeProperties,
    createQuestionObject: createQuestionObject,
    setModelValues: setModelValues,
    updateQuestionOnly: updateQuestionOnly,
    getOneQuestion: getOneQuestion,
    getListWithPagination: getListWithPagination,
    uploadFileToQuestion : uploadFileToQuestion,
    updateQuestionAccessStatus : updateQuestionAccessStatus,
    cloneQuestion : cloneQuestion ,
    cloneQuestionOfQuiz : cloneQuestionOfQuiz,
    questionCountQueryResult,
    removeQuestionFromServersData,
    exportQuestion,
    saveExportedQuestion,
    addTestCaseInCodingQuestion,
    removeTestCaseFromCodingQuestion,
    getInputOfTestCaseInCodingQuestion,
    updateSolution,
    removeFileFromQuestion,
    updateIsLockedInQuestion,
    checkClonePermission,
    createQuestionObjToSave,
    exportQuestionWithQuestionId,
    getOneTestCase,
    handleTestCaseSaveToCloud,
    populateCloudTestCase,
};
