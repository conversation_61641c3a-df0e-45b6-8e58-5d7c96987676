const dayjs = require('dayjs');
const Services = require('../Services');
const libs = require('../Lib');

/**
 * 
 * @param {string} userId 
 * @param {string} quizId 
 * @returns 
 */
const getOneJitsiToken = async (userId, quizId) => {
    const previouslyAssignedJitsiCred = await Services.JitsiBucketService.update({
        'userId': userId,
        'quizId': quizId,
    },{
        $set: {
            'assignedAt': new Date(),
            'status': libs.constants.jitsiBucketConstants.inUse,
        }
    }, {});

    if (previouslyAssignedJitsiCred) {
        return previouslyAssignedJitsiCred;
    }

    const dateThreshold = dayjs().subtract(1, 'day');
    const findObj = {
        $or: [
            {'status': libs.constants.jitsiBucketConstants.active},
            {'assignedAt': {$lt: dateThreshold.toDate()}},
        ]
    };
    console.log(findObj);
    const result = await Services.JitsiBucketService.update(findObj, {
        $set: {
            'status': libs.constants.jitsiBucketConstants.inUse,
            'userId': userId,
            'quizId': quizId,
            'assignedAt': new Date(),
        }
    }, {});
    if (result) {
        return result;
    }
    return null;
}

/**
 * 
 * @param {string | undefined} id 
 * @param {{callStatsUserName: string, jitsiMeetId: string}} data 
 * @param {{userId: string, quizId: string}} userData 
 * @returns 
 */
const updateJitsiCredentials = async (id, data, userData) => {
    if (!data.callStatsUserName || !data.jitsiMeetId) {
        console.log('Something went wrong data is empty. Ignoring the save request');
        return false;
    }
    let result = null;
    if (!id) {
        const objToSave = {
            'status': libs.constants.jitsiBucketConstants.inUse,
            'callStatsUserName': data.callStatsUserName,
            'jitsiMeetId': data.jitsiMeetId,
            'assignedAt': new Date(),
            'userId': userData.userId,
            'quizId': userData.quizId,
        }
        const saveResult =  await Services.JitsiBucketService.save(objToSave);
        result = {
            id: saveResult._id.toString(),
            callStatsUserName: saveResult.callStatsUserName,
            jitsiMeetId: saveResult.jitsiMeetId,
        }
    } else {
        const updateObj = {
            'status': libs.constants.jitsiBucketConstants.inUse,
            'callStatsUserName': data.callStatsUserName,
            'jitsiMeetId': data.jitsiMeetId,
            'assignedAt': new Date(),
            'userId': userData.userId,
            'quizId': userData.quizId,
        }
        const updateResult = await Services.JitsiBucketService.update({
            _id: id,
        }, updateObj, {});
        result = {
            id: updateResult._id.toString(),
            callStatsUserName: updateResult.callStatsUserName,
            jitsiMeetId: updateResult.jitsiMeetId,
        }
    } 
    if (!result) {
        console.log('Something went wrong result is empty!');
        return;
    }
    console.log(`Jitsi token :${result._id} is assigned to ${result.userId} for ${result.quizId}`);
    return result;
}

const releaseJitsiToken = async (userId, quizId) => {
    try {
        const ongoingQuizKey = libs.constants.redisKeys.getOnGoingQuiz(userId, quizId);
        const jitsiMeetId = await Services.RedisServices.redis('hget', ongoingQuizKey, libs.constants.redisKeys.jitsiBucketId);
        const redisPipeline = Services.RedisServices.getRedisInstance('redis').pipeline();
        redisPipeline.hdel(ongoingQuizKey, libs.constants.redisKeys.jitsiBucketId);
        redisPipeline.hdel(ongoingQuizKey, libs.constants.redisKeys.jitsiMeetId);
        redisPipeline.hdel(ongoingQuizKey, libs.constants.redisKeys.callStatsUserName);
        await redisPipeline.exec();
        console.log(jitsiMeetId);
        if (jitsiMeetId) {
            await Services.JitsiBucketService.updateMany({_id: jitsiMeetId}, {
		$unset: {
		    userId: 1,
		    quizId: 1,
		},
                $set: {
                    status: libs.constants.jitsiBucketConstants.active,
                    assignedAt: Date.now(),
                }
            })
        }
    } catch (error) {
        console.error(error);
    }
}

const countJitsiToken = async (criteria, projection, options = {}) => {
    try {
        const count = await Services.JitsiBucketService.find(criteria, projection, options).count();
        return count;
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            error: error?.message ?? error,
        });
    }
};

const cleanJitsiBucket = async () => {
    return Services.JitsiBucketService.deleteMany({}, {}, {});
}

module.exports = {
    countJitsiToken,
    cleanJitsiBucket,
    getOneJitsiToken,
    releaseJitsiToken,
    updateJitsiCredentials,
}
