const services = require('../Services');
const libs = require("../Lib");
const userAttemptController = require("../Controllers/UserAttemptController");
const questionController = require("../Controllers/QuestionController");
// const courseController = require("./delete_CourseController");
// const tutorialController = require("./delete_TutorialController");
const quizController = require("../Controllers/QuizController");
// const projectController = require("./delete_ProjectController");
const userQuizSubmittedSegmentController = require("../Controllers/UserQuizSubmittedSegmentController");

const mongoose = require('mongoose');
const { logger, constants } = libs;
const  { contentTypeString, modelString } = constants;
const _ = require('lodash')

var getUserAnswer = function (criteria, projections, options, callback) {
    return services.UserAnswerService.getUserAnswer(criteria, projections, options, callback);
};

var getOneUserAnswer = function (criteria, projections, options, callback) {
    return services.UserAnswerService.getOneUserAnswer(criteria, projections, options, callback);
};

var addUserAnswer = function (userAnswer, callback){
    return services.UserAnswerService.addUserAnswer( userAnswer, callback)
};

var updateUserAnswer = function (criteria, options, updateObj, callback) {
    return services.UserAnswerService.updateUserAnswer(criteria, options, updateObj, callback);
};

var updateManyUserAnswer = function (criteria, options, updateObj, callback) {
    return services.UserAnswerService.updateManyUserAnswer(criteria, options, updateObj, callback);
};

var performAggregation = function (aggregationArray, callback) {
    return services.UserAnswerService.aggregate(aggregationArray, callback);
}

var isPrevQuesAttempted = function (courseObj, questionId, userId, callback) {
    let prevQuesId = courseObj.courseContent[0].id.toString();
    if (prevQuesId == questionId.toString()) {
        callback(null, true);
        return;
    }
    const { segmentData } = courseObj;

    if (!segmentData)
        return callback({ error: "Segment data not present" });

    const { segmentObj } = segmentData

    let isCurrQuesFound = false;

    if (segmentData.contentIndex === segmentObj.startIndex - 1 || segmentObj.skipAllowed ) {
         //checking if the question is first question of segment because in this case we will only check trigger id or its from the skip allowed segment
        if (!segmentObj.triggerId)
            return callback(null, true);
        else {
            prevQuesId = segmentData.segmentObj.triggerId;
            isCurrQuesFound = true;
        }
    }  else {
        for (let index = 1; index < courseObj.courseContent.length; index++) {
            let currQuesId = courseObj.courseContent[index].id.toString();
            if (currQuesId != questionId) prevQuesId = currQuesId;
            else {
                isCurrQuesFound = true;
                break;
            }
        }
    }
    if (isCurrQuesFound) {
        let findQueryObj = {
            courseId: courseObj._id,
            userId: userId,
            $or: [{ tutorialId: mongoose.Types.ObjectId(prevQuesId) },
            { questionId: mongoose.Types.ObjectId(prevQuesId) },
	    { projectId: mongoose.Types.ObjectId(prevQuesId) },
            { courseQuizId : mongoose.Types.ObjectId(prevQuesId) },]
        };
        getUserAnswer(findQueryObj, { completed: 1 }, {}, (err, userAnswer) => {
            if (err) {
                callback(err);
                return;
            }
            callback(null, userAnswer && userAnswer[0] && userAnswer[0].completed ? true : false);
            return;
        });
        // });
    }
    else {
        callback({ 'error': "ques not found in course content" });
    }
}

var startCourseAttempt = function (payload, courseId, questionId, sessionObj, callback) {
    let userId = sessionObj.userId;
    if (!( payload && courseId && questionId && userId && callback && typeof callback === 'function' )) {
        logger.error("parameters invalid startCourseAttempt ", "payload-", payload, "course-", courseId, "questionId-", questionId, "userId-", userId);
        logger.error("parameters invalid startCourseAttempt");
        callback({ 'error' : "error in parametrs "});
        return false;
    }
    courseController.getOneCourse({'_id': courseId},
        {
            'languagesAllowed': 1,
            '_id': 1,
            'title': 1,
            'courseContent': 1,
            'courseSegments': 1,
            'attemptInSequence': 1,
            'disableCopyPaste': 1
        }, { }, (errorCourse, course) => {
            if (errorCourse) {
                logger.debug("Error getting course startCourseAttempt" , errorCourse, "courseId--", courseId);
                logger.error("Error getting course startCourseAttempt");
                callback({ 'error' : "course id is invalid startCourseAttempt"});
                return;
            } else if (!course) {
                logger.debug(" course id is invalid startCourseAttempt" , "courseId--", courseId);
                logger.error(" course id is invalid startCourseAttempt");
                callback("course id is invalid startCourseAttempt");
                return;
            }

            if ( course.attemptInSequence &&
                 sessionObj.role == libs.constants.roleNumberFromString.user ) {
                let segmentData = libs.util.getSegmentFromIdOfContent(course , questionId);
                const { segmentObj } = segmentData;
                course.segmentData = segmentData;
                isPrevQuesAttempted(course, questionId, sessionObj.userId, (err, isAllowed) => {
                    if (err) {
                        logger.debug("Error getting course isPrevQuesAttempted", err, "courseId--", courseId);
                        logger.error("Error getting course isPrevQuesAttempted");
                        callback({ 'error': "Error getting course isPrevQuesAttempted" });
                        return;
                    }

                    if (!isAllowed) {
                        logger.debug("Error Prev ques is not attempted courseId--", courseId);
                        logger.error("Error Prev ques is not attempted");
                        callback({ 'error': "Error Prev ques is not attempted" });
                        return;
                    }

                    getContentForQuestionAttempt(questionId, course, sessionObj, false, callback);
                })
            }
            else {
                getContentForQuestionAttempt(questionId, course, sessionObj, false, callback);
            }
        });
};

var attemptCourseTutorial = function (courseId, tutorialId, req, callback) {
    courseController.getOneCourse({'_id': courseId},
        {
            'languagesAllowed': 1,
            '_id': 1,
            'title': 1,
            'courseContent': 1,
            'courseSegments': 1,
            'attemptInSequence': 1,
            'disableCopyPaste': 1
        }, {}, (error, course) => {
            if (error || (!course)) {
                logger.error(" course id is invalid " + error, "courseId--", courseId);
                callback(" course id is invalid " + error);
                return;
            }
            getContentForTutorialAttempt(tutorialId, course, req.session, false, callback);
        });
}

var createUserTutorialAttempt = function (userAnswerId, outputObj, data, callback) {
    if (!( outputObj && userAnswerId && callback && typeof callback === 'function' )) {
        logger.debug("createUserTutorialAttempt parameters invalid---", "outputObj", outputObj, "userAnswerId-", userAnswerId, "callback-");
        logger.error("createUserTutorialAttempt parameters invalid");
        callback("error in parametrs ", null);
        return;
    }
    userAttemptController.createUserAttempt(userAnswerId, outputObj, data, libs.constants.questionTypeNumeric.questionTypeCoding, function (error, result) {
        if (error) {
            callback(error, null);
        }
        else {
            let obj = {};
            callback(error, obj);
        }
    });

}

var setQuestionValuesFromUserAttempt = function (userAttempt, question, courseId, callback) {
    if (question.type == '3' || question.type == '4' || question.type == '5') {
        let counter = 0;
        let tempObj;

        question.questionTypeCoding.codeproglang.forEach((langObj) => {
            if (langObj.language === userAttempt.attemptData[0].userLanguage) {
                question.questionTypeCoding.codeproglang = [langObj];
                question.questionTypeCoding.codeproglang[0].codeComponents.body = userAttempt.attemptData[0].userProgram;
                return;
            }
            counter += 1;
        });

        if (question.type == '4') {
            courseController.getOneCourse({'_id': courseId, courseContent: {$elemMatch: {'id': question._id}}}, {
                'courseContent.$': 1
            }, {}, (error, course) => {
                if (error) {
                    callback(error);
                    return;
                }
                question.questionTypeCoding.executionType = course.courseContent[0].executionType;
                callback(error);
            });
        } else {
            callback(null);
        }
    } else {
        callback(null);
    }
};

var saveSubjectiveAttempt = function (sessionObj, payload, containerId, qId, callback) {
    let queryObj = {'questionId': qId, 'userId': sessionObj.userId};
    if (payload.quizId) {
        queryObj.quizId = containerId;
        // queryObj.finalSubmission = true;
    } else {
        queryObj.courseId = containerId;
    }
    getOneUserAnswer(
        queryObj, {}, {}, function (errorUserAnswer, userAnswer) {
            if (errorUserAnswer) {
                logger.debug("saveSubjectiveAttempt error getting user answer ", queryObj);
                logger.error("saveSubjectiveAttempt error getting user answer");
                callback(errorUserAnswer, null);
            }
            else {
                if (!userAnswer) {
                    queryObj.completed = true;
                    queryObj.finalSubmission = payload.quizId;
                    services.UserAnswerService.addUserAnswer(queryObj, function (errorCreate, resultCreate) {
                        if (errorCreate) {
                            logger.debug("saveSubjectiveAttempt error in creating useranswer ", errorCreate);
                            logger.error("saveSubjectiveAttempt error in creating useranswer");
                            callback(errorCreate, null);
                        }
                        createSubjectiveAttemptInDb(resultCreate, payload, callback);
                    });
                } else {
                    services.UserAnswerService.updateUserAnswer(queryObj, {upsert: true, setDefaultsOnInsert: true},
                        {$set: {'completed': true, 'finalSubmission': true}},
                        function (errorUpdate, resultUpdate) {
                            if (errorUpdate) {
                                logger.debug("saveSubjectiveAttempt error in updating useranswer-", errorUpdate);
                                logger.error("saveSubjectiveAttempt error in updating useranswer");
                                callback(errorCreate, null);
                            }
                            createSubjectiveAttemptInDb(resultUpdate, payload, callback);
                        });
                }
            }
        });
};

var createSubjectiveAttemptInDb = function (userAnswer, obj, callback) {
    obj.payload = {};
    obj.payload.isTutorialQuestion = false;
    obj.payload.questionSubmission = userAnswer.quizId;
    userAttemptController.createUserAttempt(userAnswer._id, obj, obj,
        libs.constants.questionTypeNumeric.questionTypeSubjective, (err, resultUserAttempt) => {
            if (err) {
                logger.debug(" user attempt subjective not saved successfully-", err, "obj-", obj);
                logger.error(" user attempt subjective not saved successfully");
            }
            getOneUserAnswer({'_id': userAnswer._id}, {}, {}, (error, userAnswerObj) => {
                if (error) {
                    logger.debug(" user attempt subjective not saved  createSubjectiveAttemptInDb", error);
                    logger.error("user attempt subjective not saved createSubjectiveAttemptInDb");
                    callback(err, resultUserAttempt);
                    return;
                }
                let linksObj = {};
                userAnswerObj.courseId.attemptInSequence = userAnswerObj.userId.role === libs.constants.roleNumberFromString.user ?
                    userAnswerObj.courseId.attemptInSequence : false;
                getNextAndPreviousQuestionAttemptLink(userAnswerObj.courseId, userAnswerObj.questionId._id, true, linksObj, (err) => {
                    resultUserAttempt.linksObj = linksObj;
                    callback(error, resultUserAttempt);
                });
            })
        });

};

const  getTitleQuery = function( contentObj ){
    let obj = {}
    switch( contentObj.contentType ){
        case contentTypeString.contentTypeTutorial : 
            obj.titleQuery = tutorialController.getOneTutorial
            obj.projectionObj = { title : 1 }
            obj.titleProp = "title"
            break;
        case contentTypeString.contentTypeQuiz : 
            obj.titleQuery = quizController.getOneQuiz
            obj.projectionObj = { title : 1 }
            obj.titleProp = "title"
            break;
        case contentTypeString.contentTypeProject : 
            obj.titleQuery = projectController.getOneProject
            obj.projectionObj = { name : 1 }
            obj.titleProp = "name"
            break;
        default : 
            obj.titleQuery = questionController.getOneQuestion
            obj.projectionObj = { title : 1, type : 1 }
            obj.titleProp = "title"
            obj.typeProp = "type"
            break;
    }
    return obj;
}

var getNextAndPreviousQuestionAttemptLink = async function (container, currentQuestion, isCompleted, linksObj, callback) {
    try{
    linksObj.nextLink = "";
    linksObj.previousLink = "";
    let contentObj = null;
    let containerContent = container.isQuiz ? container.quizContent : container.courseContent;
    let containerSegments = container.isQuiz ? container.quizSegments : container.courseSegments;
    let { segmentData } = container;
    let i;

    if( segmentData ){
        i = segmentData.contentIndex;
         contentObj = containerContent[i];
    } else {
        for (i = 0; i < containerContent.length; i += 1) {
            contentObj = containerContent[i];
            if (contentObj.id.equals(currentQuestion)) {
                break;
            }
        }
        segmentData = libs.util.getSegmentFromIndexOfContent( i );
    }
     linksObj.contentObj = contentObj;
    if (callback && typeof callback !== 'function') {
        return;
    }
    if (i < containerContent.length) {
        if (i !== containerContent.length - 1) {
            if (container.attemptInSequence && segmentData.segmentObj && !segmentData.segmentObj.skipAllowed) {
                if (isCompleted)
                    linksObj.nextLink = getAttemptLink(containerContent[i + 1].contentType,
                        containerContent[i + 1].id, container._id, container.isQuiz);
                else
                    linksObj.nextLink = "";
            } else {
                linksObj.nextLink = getAttemptLink(containerContent[i + 1].contentType,
                    containerContent[i + 1].id, container._id, container.isQuiz);
            }
        }
        if (i !== 0) {
            linksObj.previousLink = getAttemptLink(containerContent[i - 1].contentType,
                containerContent[i - 1].id, container._id, container.isQuiz);
        }
        linksObj.segmentData = segmentData;
        let nextTitleObj, preTitleObj;
        if( linksObj.nextLink )
            nextTitleObj = getTitleQuery( containerContent[i + 1] )
        if( linksObj.previousLink )
            preTitleObj = getTitleQuery( containerContent[i - 1] )
        
        if( nextTitleObj && nextTitleObj.titleQuery ){
            
            let nextContent = await nextTitleObj.titleQuery( { '_id': containerContent[i + 1].id }, nextTitleObj.projectionObj || { 'title' :1 }, {})
            if( nextContent ){
                linksObj.nextTitle = nextContent[ nextTitleObj.titleProp ]
                linksObj.nextType = containerContent[i + 1].contentType || nextContent[ nextTitleObj.typeProp ] 
            }
        }

        
        if( preTitleObj && preTitleObj.titleQuery ){
            preContent = await preTitleObj.titleQuery( { '_id': containerContent[i - 1].id }, preTitleObj.projectionObj || { 'title' :1 }, {})
            if( preContent ){
                linksObj.previousTitle = preContent[ preTitleObj.titleProp ]
                linksObj.previousType = containerContent[i - 1].contentType || preContent[ preTitleObj.typeProp ] 
            }
        }

        callback( null );
     
    } else {
        callback({ error : "nothing is there to show" });
        return;
    } 
} catch( e ){
    console.log("Error while fetching next and previous link", e )
    callback({ error : "Error while fetching next and previous link"})
}
};

var getAttemptLink = function (contentType, contentId, containerId, isQuiz) {
    if (!( contentId && containerId && contentType )) {
        logger.error(" contentId-", contentId, " containerId-", containerId, " contentType-", contentType);
        return "";
    }
    let attemptLink = "/attempt/";
    if (contentType == libs.constants.contentTypeString.contentTypeQuiz ) {
        attemptLink += "attemptquiz/"
    }
    else if (contentType == libs.constants.contentTypeString.contentTypeProject ) {
        attemptLink += "attemptproject/"
    }
    else if (contentType == libs.constants.contentTypeString.contentTypeTutorial ) {
        attemptLink += "attempttutorial/"
    } 
    else {
        attemptLink += "attemptquestion/"
    }
    if (isQuiz)
        attemptLink += "quiz/"
    attemptLink += containerId + "/" + contentId;
    return attemptLink;
};

var getNextContentLinkCourse = function (sessionObj, courseId, contentId, callback) {
    courseController.getOneCourse({'_id': courseId},
        {'languagesAllowed': 1, 'courseContent': 1, '_id': 1, 'attemptInSequence': 1, 'courseSegments' : 1}, {}, (error, course) => {
            if (error || (!course)) {
                logger.debug("getNextContentLinkCourse course id is invalid ", error, "courseId--", courseId);
                logger.error("getNextContentLinkCourse course id is invalid");
                callback({'error' : "course id is invalid "});
                return;
            }
            let linksObj = {};
            course.attemptInSequence = sessionObj.role === libs.constants.roleNumberFromString.user ?
                course.attemptInSequence : false;
            course.isQuiz = false;
            getNextAndPreviousQuestionAttemptLink(course, contentId, true, linksObj, (err) => {
                callback(err, linksObj);
            });

        });

};

var getUserAnswerPagination = function (req, userId, callback) {
    if (req.body.search.value) {
        searchRegex = new RegExp(req.body.search.value, 'i');
        courseId = mongoose.Types.ObjectId(req.body.courseId);
        courseController.getOneCourse( { '_id' : courseId }, { 'questionId' : 1 }, {}, ( errCourse, course)=>{
            if( errCourse ){
                logger.debug("getUserAnswerPagination Error while finding course ", errCourse, " courseId ", payload );
                logger.error("getUserAnswerPagination Error while finding course");
                callback({ 'error' : "Course not found"});
                return;
            }
            if( !( course && course.questionId && course.questionId.length) ){
                callback( errCourse , { 'data' : []});
                return;
            }
            var payload = {};
            payload.title = searchRegex;
            payload._id = { $in : course.questionId };
            questionController.getQuestion(payload, {'_id': 1}, {}, (error, result) => {
                if (error) {
//error to fetch question list
                    return callback({ 'error' : "error to fetch question list"});
                } else {
//question list is fetched now find user answers
                    let criteria = {};
                    criteria.questionId = {$in: result};
                    criteria.userId = mongoose.Types.ObjectId(userId);
                    criteria.courseId = mongoose.Types.ObjectId(req.body.courseId);
                    req.body.criteria = criteria;
                    getAnswersofUserWithPagination(req, callback);
                }

            });
        })
    } else {
        req.body.criteria = {
            'userId': mongoose.Types.ObjectId(userId),
            'courseId': mongoose.Types.ObjectId(req.body.courseId), questionId: {$exists: true}
        };
        getAnswersofUserWithPagination(req, callback);

    }
}
var getAnswersofUserWithPagination = function (req, callback) {
    //services.UserAnswerService.getUserAnswer(criteria, {}, { sort:{lastActive:"desc"} ,skip: start , limit: length }, callback);
    services.PaginationServices.serverSidePagination(req, libs.constants.modelString.UserAnswerModelString,
        (error, result) => {
        if (error) {
            return callback(error);
        }
        else {
            let previousQId  , removingAnswer = [];
            for(let i=0 ; i < result.data.length ; i++){
                let record = result.data[i] ;
                let compilationError = 0, testcaseFailed = 0, successful = 0;
                let question = record.questionId[0];
                if(record.userattempts) {
                    if (question.type == libs.constants.questionTypeNumeric.questionTypeCoding || question.type == libs.constants.questionTypeNumeric.questionTypeStepwise) {

                        // record.userattempts.forEach((attemptCollection) => {
                        record.userattempts.attemptData.forEach((attempt) => {
                            if (attempt.userCompilationError)
                                compilationError += 1;
                            else {
                                let attemptResult;
                                if (question.type == libs.constants.questionTypeNumeric.questionTypeCoding)
                                    attemptResult = libs.util.compareTestCaseAndResult(question.questionTypeCoding.testCase, attempt.userOutputCoding);
                                else {
                                    attemptResult = libs.util.compareTestCaseAndResult(question.questionTypeCoding.testCase, attempt.userOutputCoding, 1);
                                }
                                //check success or failure
                                if (attemptResult && attemptResult.totalTestCases == attemptResult.testCasePassed)
                                    successful += 1;
                                else testcaseFailed += 1;

                            }
                        });
                        // });

                        record.reportCard = {
                            "testcaseFailed": testcaseFailed,
                            "successful": successful,
                            "compilationError": compilationError
                        };
                    }
                    else if (question.type == libs.constants.questionTypeNumeric.questionTypeMCQ) {
                        //  record.userattempts.forEach((attemptCollection) => {
                        record.userattempts.attemptData.forEach((attempt) => {
                            if (question.questionTypeMCQ.correctAnswers[0] == attempt.userInputMCQ) successful += 1;
                            else testcaseFailed += 1;
                        });
                        // });

                        record.reportCard = {
                            "testcaseFailed": testcaseFailed,
                            "successful": successful,
                            "compilationError": compilationError
                        };

                    }
                    delete record.userattempts;
                }else{
                    record.reportCard = {
                        "testcaseFailed": testcaseFailed,
                        "successful": successful,
                        "compilationError": compilationError ,
                        "notAttempted" : 1,
                    };
                }
                record.questionId[0] = {_id: question._id, title: question.title, type: question.type};
                // record.courseId[0] = {_id: record.courseId[0]._id, title: record.courseId[0].title};
                if(previousQId && previousQId.toString() == record.questionId[0]._id.toString()){
                    record.reportCard.successful += result.data[i-1].reportCard.successful ;
                    record.reportCard.testcaseFailed += result.data[i-1].reportCard.testcaseFailed ;
                    record.reportCard.compilationError += result.data[i-1].reportCard.compilationError ;
                    removingAnswer.push(i-1) ;
                }
                previousQId = record.questionId[0]._id ;
            }
            let count = 0 ;
            removingAnswer.forEach((remove)=>{
                result.data.splice(remove-count, 1);
                count++ ;
            })
            return callback(null, result);
        }
    });
};

var getContentForQuestionAttempt = function (questionId, containerObj, sessionObj, isQuiz, callback) {
    let userId = sessionObj.userId;
    containerObj.isQuiz = isQuiz;
    if (!isQuiz) {
        questionController.getQuestionFromId(questionId, function (error, ques) {
            let queryObj = {'questionId': questionId, 'userId': userId};
            isQuiz ? queryObj.quizId = containerObj._id : queryObj.courseId = containerObj._id;
            if (error) {
                logger.debug(`getContentForQuestionAttempt Error getting Question`);
                logger.error(`getContentForQuestionAttempt Error getting Question`);
                callback(error, null);
            } else {
                updateUserAnswer(
                    queryObj, {'upsert': true, 'setDefaultsOnInsert': true}, {$set: {'lastActive': Date.now()}},
                    function (errorUserAnswer, userAnswer) {
                        if (errorUserAnswer) {
                            logger.debug("getContentForQuestionAttempt error getting user answer ", queryObj);
                            logger.error("getContentForQuestionAttempt error getting user answer ");
                            callback(errorUserAnswer, null);
                        }
                        else {
                            let obj = {};
                            obj.ques = ques;
                            isQuiz ? obj.quizId = containerObj._id : obj.courseId = containerObj._id;
                            obj.container = containerObj;
                            obj.userAnswerId = userAnswer._id;
                            if (userAnswer) {
                                obj.isCompleted = userAnswer.completed;
                                obj.userAnswerId = userAnswer._id;
                            }
                            else
                                obj.isCompleted = false;
                            if (ques.type !== '1' && ques.type !== '2' && ques.questionTypeCoding.codeproglang)
                                obj.stringifyQues = JSON.stringify(ques.questionTypeCoding.codeproglang);
                            else
                                obj.stringifyQues = {};
                            let codeproglangArray = [];
                            if (ques.type === libs.constants.questionTypeNumeric.questionTypeCoding ||
                                ques.type === libs.constants.questionTypeNumeric.questionTypeStepwise) {
                                ques.questionTypeCoding.codeproglang.forEach((languagueObj) => {
                                    if (containerObj.languagesAllowed.indexOf(languagueObj.language) !== -1)
                                        codeproglangArray.push(languagueObj);
                                });
                                ques.questionTypeCoding.codeproglang = codeproglangArray;
                            }
                            let linksObj = {};
                            containerObj.attemptInSequence = sessionObj.role === libs.constants.roleNumberFromString.user ?
                                containerObj.attemptInSequence : false;
                            getNextAndPreviousQuestionAttemptLink(containerObj, ques._id,
                                obj.isCompleted, linksObj, (err) => {
                                    obj.attemptLinks = linksObj;
                                    if (ques.type === libs.constants.questionTypeNumeric.questionTypeCoding ||
                                        ques.type === libs.constants.questionTypeNumeric.questionTypeStepwise)
                                        ques.questionTypeCoding.executionType = linksObj.contentObj.executionType;
                                    else if (ques.type === libs.constants.questionTypeNumeric.questionTypeMCQ)
                                        ques.questionTypeMCQ.showExplanation =
                                            linksObj.contentObj.executionType === libs.constants.codingQuestionExecutionType.resultWithTestCases ?
                                                true : false;
                                    if (linksObj.contentObj.files && linksObj.contentObj.files.length) {
                                        obj.stringifyFiles = JSON.stringify(linksObj.contentObj.files)
                                    }
                                    ques.showHead = linksObj.contentObj.showHead;
                                    ques.showTail = linksObj.contentObj.showTail;
                                    ques.showCustomInput = linksObj.contentObj.showCustomInput;
                                    callback(errorUserAnswer, obj);
                                });
                        }
                    });
            }
        });
    } else {
        getQuestionFromDb(questionId, (err, ques) => {
        })
    }

}

var getContentForProjectAttempt = function (payload, callback) {
    let { projectId, courseId, req, session } = payload;
    let { userId }  = session;

    let queryObj = { 'projectId': projectId, 'userId': userId, courseId };
    getOneUserAnswer(queryObj, { _id: 1, completed: 1, isReviewPending: 1}, {}, (err, userAnswerObj) => {
        if (err) {
            logger.debug("getContentForQuestionAttempt error getting user answer 1 ", queryObj);
            callback(err, null);
            return ;
        }
        if ( userAnswerObj && userAnswerObj._id ) {
            //console.log("user answer exists, userAnswer = ", userAnswerObj);
            let updateObj = { $set: { 'lastActive': Date.now() } };
            let updateConfParams = { 'upsert': true, 'setDefaultsOnInsert': true };
            updateUserAnswer(queryObj, updateConfParams, updateObj, (errorUserAnswer, userAnswer) => {
                if (errorUserAnswer) {
                    logger.debug("getContentForQuestionAttempt error getting user answer ", queryObj);
                    callback(errorUserAnswer, null);
                }
                const projectStatus = userAnswerObj.completed ? libs.constants.projectStatus.complete : 
                                      ( userAnswerObj.isReviewPending ? libs.constants.projectStatus.inReview : libs.constants.projectStatus.inProgress );
                                        
                const redirectStr = '/project/getProject?' + 
                                    'project=' + userAnswer.projectName + 
                                    '&userId=' + userId.toString() +
                                    '&courseId=' + courseId.toString() +
                                    '&quesId=' + projectId.toString() + 
                                    '&status=' + projectStatus;
                callback(null, { 'redirect': redirectStr });
            });
        }
        else {
            console.log("Forking starts");
            projectController.getOneProject({ _id : projectId },{}, {}, function (error, projectObj) {
                if (error) {
                    logger.debug(`getContentForQuestionAttempt Error getting Question`);
                    logger.error(`getContentForQuestionAttempt Error getting Question`);
                    callback(error, null);
                    return ;
                } 
                if ( ! projectObj ) {
                    logger.debug(`getContentForQuestionAttempt Error getting Question`);
                    logger.error(`getContentForQuestionAttempt Error getting Question`);
                    callback({error: "Project Obj is null"});
                    return ;
                }
                let obj = {
                    'req': req,
                    'projectObj': projectObj,
                    'payload': payload
                }
                projectController.cloneProject(obj, (error, clonedProjectObj) => {
                    if (error) {
                        logger.debug(`getContentForQuestionAttempt cloneProject Error getting Question`);
                        logger.error(`getContentForQuestionAttempt cloneProject Error getting Question`);
                        callback(error, null);
                        return ;
                    }
    
                    if ( ! clonedProjectObj ) {
                        logger.debug(`getContentForQuestionAttempt clonedProjectObj is null`);
                        logger.error(`getContentForQuestionAttempt clonedProjectObj is null`);
                        callback({error: "Cloned project Obj is null"});
                        return ;
                    }
    
                    queryObj = { projectId, userId, courseId };
                    let updateObj = {
                        $set: {
                            'projectId': projectId,
                            'attemptProjectId': clonedProjectObj._id,
                            'courseId': courseId,
                            'userId': userId,
                            'projectName': clonedProjectObj.name,
                            'lastActive': Date.now() 
                        }
                    }
                    let updateConfParams = { 'upsert': true, 'setDefaultsOnInsert': true };
                    updateUserAnswer(queryObj, updateConfParams, updateObj, (errorUserAnswer, userAnswer) => {
                        if (errorUserAnswer) {
                            logger.debug("getContentForQuestionAttempt error getting user answer ", queryObj);
                            callback(errorUserAnswer, null);
                        }
                        const redirectStr = '/project/getProject?' + 
                                            'project=' + userAnswer.projectName + 
                                            '&userId=' + userId.toString() +
                                            '&courseId=' + courseId.toString() + 
                                            '&quesId=' + projectId.toString() + 
                                            '&status=' + libs.constants.projectStatus.inProgress;
                        callback(null, { 'redirect': redirectStr });
                    });
                })
            });
        }
    })
}

var getQuestionFromDb = async function (questionId, callback) {
    let isCB = callback && typeof callback === 'function';
    try {
        if (!(questionId)) {
            console.log(`getQuestionFromDb- parameters invalid---${questionId}`);
            throw new Error("error in parametrs ");
        }
        let hash = libs.constants.redisKeys.questionString + ":" + questionId;
        let { processMapName, controlKey } = libs.constants.cachingKeys.question

        let isQuestionPresentInMap = Object.prototype.hasOwnProperty.call(global[processMapName], questionId)

        if (!global[controlKey] && isQuestionPresentInMap) {
            let questionObj = (global[processMapName][questionId]);
            questionObj.lastUsed = Date.now()
            questionObj = _.cloneDeep(questionObj);
            if (isCB) callback(null, questionObj);
            return questionObj;
        }

        let redisQuestionKeys = libs.constants.redisQuestionKeys,
            redisQuestionKeysRequiredParsing = libs.constants.redisQuestionKeysRequiredParsing;
        const { text, tags, type, files, title, score, languagesAllowed, questionTypeSubjective,
            questionTypeMCQ, questionTypeCoding, _id, questionTypeWeb } = redisQuestionKeys;
        let questionMGETArray = [
            _id.key,
            text.key,
            tags.key,
            type.key,
            title.key,
            score.key,
            files.key,
            questionTypeSubjective.key,
            questionTypeMCQ.key,
            questionTypeCoding.key,
            questionTypeWeb.key,
        ]
        let resultRedis = await services.RedisServices.redis("hmget", hash, questionMGETArray)
        if (resultRedis && resultRedis.length && resultRedis[0]) {
            let questionObj = {}
            questionMGETArray.map((key, i) => {
                if (redisQuestionKeysRequiredParsing[key]) {
                    try {
                        questionObj[key] = resultRedis[i] ? JSON.parse(resultRedis[i]) : {}
                    } catch (e) {
                        console.log(" error ", e);
                        questionObj[key] = {}
                    }
                } else {
                    questionObj[key] = resultRedis[i]
                }
            })
            // let questionObj = JSON.parse(resultRedis);
            if (!isQuestionPresentInMap) {
                questionObj.lastUsed = Date.now()
                global[processMapName][questionId] = _.cloneDeep(questionObj);
            }

            if (isCB) callback(errRedis, questionObj);
            return questionObj;
        }
        let projectionObj = {
            'createdby': 1, 'questionTypeCoding': 1, 'questionTypeMCQ': 1, 'title': 1, 'text': 1, 'type': 1,
            'score': 1, 'explanation': 1, 'questionTypeSubjective': 1, 'files': 1, 'quizId': 1,
            'questionTypeWeb': 1
        }
        let ques = await questionController.getOneQuestion({ '_id': questionId }, projectionObj, {});

        if (!ques) throw new Error("Question not present")

        ques.lastUsed = Date.now()
        global[processMapName][questionId] = _.cloneDeep(ques);

        services.RedisServices.redis('hmset', hash,
            _id.key, ques._id,
            text.key, ques.text,
            tags.key, ques.tags,
            type.key, ques.type,
            title.key, ques.title,
            score.key, ques.score,
            files.key, JSON.stringify(ques.files || []),
            questionTypeSubjective.key, JSON.stringify(ques.questionTypeSubjective || {}),
            questionTypeMCQ.key, JSON.stringify(ques.questionTypeMCQ || {}),
            questionTypeCoding.key, JSON.stringify(ques.questionTypeCoding || {}),
            questionTypeWeb.key, JSON.stringify(ques.questionTypeWeb || {})
        )

        services.RedisServices.redis("expire", hash, 60 * 60 * 24);
        if( isCB)   callback(null, ques);
        return ques;
    } catch (e) {
        console.log("Error while getting question from db", e);
        if (isCB) callback({ error: e.message });
        return false;
    }
};

var getContentForTutorialAttempt = function (tutorialId, containerObj, sessionObj, isQuiz, callback) {
    let userId = sessionObj.userId;
    containerObj.isQuiz = isQuiz;
    tutorialController.previewTutorial(tutorialId, sessionObj.displayname, sessionObj.role, (err, tutorial) => {
        if (err) {
            logger.debug(`getContentForTutorialAttempt Error getting Tutorial id  ${tutorialId}`, err );
            logger.error(`getContentForTutorialAttempt Error getting Tutorial id`);
            callback(" tutorial id is invalid ");
            return;
        }
        else {
            let insertObj = {'tutorialId': tutorialId, 'userId': userId,'score':0};
            isQuiz ? insertObj.quizId = containerObj._id : insertObj.courseId = containerObj._id;
            isQuiz ? tutorial.quiz = containerObj : tutorial.course = containerObj;
            tutorial.role = sessionObj.role;
            isQuiz ? tutorial.quizId = containerObj._id : tutorial.courseId = containerObj._id;
            tutorial.email = sessionObj.email;
            if (!tutorial.isCodeComponentPresent) {
                updateUserAnswer(insertObj, {'upsert': true}, {
                    $set: {
                        'completed': true,
                        'lastActive': Date.now()
                    }
                }, (errorUserAnswer, userAnswer) => {
                    if (errorUserAnswer) {
                        logger.debug("getContentForTutorialAttempt error in finding userAnswer  with id ", errorUserAnswer, "queryObj-", insertObj);
                        logger.error("getContentForTutorialAttempt error in finding userAnswer");
                        tutorial.isCompleted = false;
                    }
                    else if (!userAnswer) {
                        logger.debug("getContentForTutorialAttempt upsert failed in useranswer queryObj-", insertObj);
                        logger.error("getContentForTutorialAttempt upsert failed in useranswer");
                        tutorial.isCompleted = false;
                    } else
                        tutorial.isCompleted = userAnswer.completed;
                    callback(err, tutorial);
                });
            } else {
                let linksObj = {};
                tutorial.isCompleted = false;
                // callback(err, tutorial);
                updateUserAnswer(insertObj, {
                        upsert: true,
                        'setDefaultsOnInsert': true
                    }, {$set: {'lastActive': Date.now()}},
                    (errorGetUserAnswer, userAnswerResult) => {
                        if (errorGetUserAnswer || ( !userAnswerResult )) {
                            logger.debug("getContentForTutorialAttempt error in finding userAnswer  with id ", errorGetUserAnswer, "queryObj-", insertObj);
                            logger.error("getContentForTutorialAttempt error in finding userAnswer");
                            tutorial.isCompleted = false;
                            callback(errorGetUserAnswer, tutorial);
                            return;
                        } else
                        tutorial.isCompleted = userAnswerResult.completed;
                        tutorial.userAnswerId = userAnswerResult._id;
                        if (userAnswerResult) {
                            userAttemptController.getDistinctUserAttempts({$match: {'userAnswerId': userAnswerResult._id}},
                                {
                                    $group: {
                                        '_id': {userAnswerId: '$userAnswerId', segmentId: '$tutorialSegmentId'},
                                        'count': {$sum: 1}
                                    }
                                },
                                (errorUserAttempt, result) => {
                                    if (errorUserAttempt) {
                                        logger.debug("getContentForTutorialAttempt error getting user attempts ", errorUserAttempt);
                                        logger.error("getContentForTutorialAttempt error getting user attempts");
                                        callback(errorUserAttempt, tutorial);
                                        return;
                                    }

                                    if (!(result instanceof Array ))
                                        result = [result];

                                    if (result) {
                                        for (let i = 0; i < tutorial.tutorial.segments.length; i += 1) {
                                            let tutroialSegmentObj = tutorial.tutorial.segments[i];
                                            if (Object.prototype.hasOwnProperty.call(tutroialSegmentObj, 'codeproglang') &&
                                                tutroialSegmentObj.codeproglang.length) {
                                                for (let j = 0; j < result.length; j += 1) {
                                                    if (tutroialSegmentObj._id.equals(result[j]._id.segmentId)) {
                                                        result.splice(j, 1);
                                                        tutroialSegmentObj.isAttempted = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    callback(err, tutorial);
                                });
                        }
                    })
            }
        }
    });
};

var startQuizAttempt = async function (payload, quizId, questionId, sessionObj, callback) {
    try {
        let isCB = callback && typeof callback === 'function';
        let { userId, courseInfo } = sessionObj;
        if (!(payload && quizId && questionId && userId)) {
            console.log("startQuizAttempt parameters invalid---", "payload-", payload, "quiz-", quizId, "questionId-", questionId, "userId-", userId);
            throw new Error("error in parametrs")
        }

        const { allowedIP, quizSegments, quizContent, languagesAllowed, quizTime,
            copyPasteAllowed, revisitAllowed, title, tabSwitchAllowed, isWebCamAllowed } = libs.constants.quizParamsHMSET;

        let resultQuiz = await quizController.getQuizFromDb({
            id: quizId,
            projections: [quizSegments.key,
            quizContent.key, languagesAllowed.key, quizTime.key,
            copyPasteAllowed.key, revisitAllowed.key, title.key,
            tabSwitchAllowed.key, allowedIP.key, isWebCamAllowed.key]
        });

        if (!resultQuiz) throw new Error("Quiz not present");
        let queryObj = { 'quizId': quizId, 'userId': sessionObj.userId };

        if (courseInfo && courseInfo.courseId) queryObj.courseId = courseInfo.courseId;

        let quizAttemptObj = await userQuizSubmittedSegmentController.getUserQuizSubmittedSegmentFromDb(queryObj, { 'questionId': questionId })
        
        if (!quizAttemptObj) {
            console.log('startQuizAttempt- Quiz accessed thorugh invalid path email', sessionObj);
            throw new Error(`Test accessed thorugh invalid path`);
        } else if (quizAttemptObj.endTime) {
            console.log("startQuizAttempt- Test Already Submitted ");
            throw new Error(`Test Already Submitted`);
        }
        quizAttemptObj.quizId = resultQuiz;
        let quiz = quizAttemptObj ? quizAttemptObj.quizId : null;

        if (!quiz) {
            console.log("startQuizAttempt quiz id is invalid startquizAttempt", errorQuiz, "quizId--", quizId);
            throw new Error("quiz id is invalid startquizAttempt");
        }

        quiz.userQuizSubmittedSegment = quizAttemptObj;
        quiz.quizSubmittedQuestion = quizAttemptObj[libs.constants.redisKeys.submittedQuestions];
        quiz.isQuiz = true;

        let ques = await getQuestionFromDb(questionId)

        if( !ques ) throw new Error("Question not present")
        let obj = {};
        obj.ques = ques;
        obj.quizId = quiz._id;
        obj.container = quiz;
        obj.isCompleted = false;
        if (ques.type == libs.constants.questionTypeNumeric.questionTypeWeb) {
            obj.stringifyQues = JSON.stringify(ques.questionTypeWeb);
        } else if (ques.type !== libs.constants.questionTypeNumeric.mcq &&
            ques.type !== libs.constants.questionTypeNumeric.questionTypeSubjective
            && ques.questionTypeCoding && ques.questionTypeCoding.codeproglang) {
            obj.stringifyQues = JSON.stringify(ques.questionTypeCoding.codeproglang);
        } else {
            obj.stringifyQues = {};
        }
        let codeproglangArray = [];
        if (ques.type === libs.constants.questionTypeNumeric.questionTypeCoding ||
            ques.type === libs.constants.questionTypeNumeric.questionTypeStepwise) {
            ques.questionTypeCoding.codeproglang.forEach((languagueObj) => {
                if (quiz.languagesAllowed.indexOf(languagueObj.language) !== -1)
                    codeproglangArray.push(languagueObj);
            });
            ques.questionTypeCoding.codeproglang = codeproglangArray;
        }
        quiz.attemptInSequence = sessionObj.role === libs.constants.roleNumberFromString.user ?
            quiz.attemptInSequence : false;
        // getNextAndPreviousLinkFromReorderedContent( )
        if (!quizAttemptObj.startTime) {
            logger.debug("startQuizAttempt start time of quiz not present ");
            callback({ 'error': "start time of quiz not present" });
            return;
        }
        obj.quizObj = quiz;
        obj.endOfSegmentOrCourse = (quiz.quizTime * 60 * 1000) - (Date.now() - (quizAttemptObj.startTime)) +
            (quizAttemptObj.extraTime ? parseInt(quizAttemptObj.extraTime) * 60 * 1000 : 0) +
            (quizAttemptObj.idleTime ? quizAttemptObj.idleTime * 60 * 1000 : 0);
        let contentObj = null;
        for (let contentIndex = 0; contentIndex < quiz.quizContent.length; contentIndex += 1) {
            contentObj = quiz.quizContent[contentIndex];
            if (contentObj.id.toString() === questionId) {
                if (ques.type === libs.constants.questionTypeNumeric.questionTypeCoding ||
                    ques.type === libs.constants.questionTypeNumeric.questionTypeStepwise)
                    ques.questionTypeCoding.executionType = contentObj.executionType;
                else if (ques.type === libs.constants.questionTypeNumeric.questionTypeMCQ)
                    ques.questionTypeMCQ.showExplanation =
                        contentObj.executionType === libs.constants.codingQuestionExecutionType.resultWithTestCases ?
                            true : false;
                if (contentObj.files && contentObj.files.length) {
                    obj.stringifyFiles = JSON.stringify(contentObj.files)
                }
                ques.showHead = contentObj.showHead;
                ques.showTail = contentObj.showTail;
                ques.showCustomInput = contentObj.showCustomInput;
            }
        }
        if( isCB )  callback(errQues, obj);
        return obj;
    } catch (e) {
        console.log("Error while starting quiz attempt", e);
        if (isCB) callback({ error: e.message });
        return false;
    }
};

var attemptQuizTutorial = function (quizId, tutorialId, req, callback) {
    quizController.getOneQuiz({'_id': quizId},
        {
            'languagesAllowed': 1,
            'quizContent': 1,
            '_id': 1,
            'attemptInSequence': 1,
            'revisitAllowed': 1,
            'quizSegments': 1
        },
        {}, (error, quiz) => {
            if (error || (!quiz)) {
                logger.error(" quiz id is invalid " + error, "quizId--", quizId);
                callback(" quiz id is invalid " + error);
                return;
            }
            getContentForTutorialAttempt(tutorialId, quiz, req.session, true, callback);
        });
};

var getQuizSubmittedAttempt = function (sessionObj, quizId, questionId, callback) {
    let findObj = {
        'quizId': mongoose.Types.ObjectId(quizId), 'questionId': mongoose.Types.ObjectId(questionId),
        'userId': mongoose.Types.ObjectId(sessionObj.userId)
    };
    let aggregateArray = [];
    aggregateArray.push({$match: findObj});
    aggregateArray.push({
        $lookup: {
            from: "userattempts",
            localField: "_id",
            foreignField: "userAnswerId",
            as: "attemptDocs"
        }
    });
    aggregateArray.push({
        $lookup: {
            'from': 'questions',
            'localField': 'questionId',
            'foreignField': '_id',
            'as': 'ques'
        }
    });
    aggregateArray.push({
        $lookup: {
            'from': 'quizzes',
            'localField': 'quizId',
            'foreignField': '_id',
            'as': 'quizId'
        }
    });
    performAggregation(aggregateArray, (err, userAnswer) => {
        if (err) {
            logger.debug("getQuizSubmittedAttempt error getting userAnswer for quiz attempt--finObj---", quizId, " qId-", questionId, "err ", err );
            logger.error("getQuizSubmittedAttempt error getting userAnswer for quiz attempt");
            callback(err);
            return;
        } else if (!userAnswer) {
            logger.debug("getQuizSubmittedAttempt useranswer not present for attempt----finObj---", quizId, " qId-", questionId);
            logger.error("getQuizSubmittedAttempt useranswer not present");
            callback({ 'error' : "useranswer not present for attempt"});
            return;
        }
        userAnswer = userAnswer[0];
        if (!( userAnswer.attemptDocs && userAnswer.attemptDocs.length )) {
            logger.debug("getQuizSubmittedAttempt userattempt not present for this question ", questionId);
            logger.error("getQuizSubmittedAttempt userattempt not present for this question");
            callback({ 'error' : "userattempt not present for this question"});
            return;
        } else {
            let finalSubmissionObj = null;
            for (let i = 0; i < userAnswer.attemptDocs.length; i += 1) {
                let attemptedObjBucket = userAnswer.attemptDocs[i];
                for (let j = 0; j < attemptedObjBucket.attemptData.length; j += 1) {
                    let attemptObj = attemptedObjBucket.attemptData[j];
                    if (attemptObj.finalSubmission) {
                        finalSubmissionObj = attemptObj;
                    }
                }
            }

            userAnswer.ques = userAnswer.ques[0];
            userAnswer.quizId = userAnswer.quizId[0];
            userAnswer.quizId.isQuiz = true;
            delete userAnswer.attemptDocs;
            if (finalSubmissionObj) {
                userAnswer.userAttempt = {'attemptData': [finalSubmissionObj]};
                userAttemptController.setQuestionValuesFromUserAttemptAndContainer(sessionObj.role, userAnswer.userAttempt,
                    userAnswer.ques, userAnswer.quizId);
                callback(err, userAnswer);
            }
            else {
                logger.debug("getQuizSubmittedAttempt question not submitted----finObj---", quizId, " qId-", questionId);
                logger.error("getQuizSubmittedAttempt question not submitted");
                callback({ 'error' : "question not submitted"});
                return;
            }
        }
    });
};

var getNextAndPreviousLinkFromReorderedContent = function (quiz, currentQuestionId, isPreviousRequired, isNextRequired, callback) {
    if (!quiz) {
        logger.error(`quiz not present`);
        return null;
    }
    isPreviousRequired = true, isNextRequired = true;
    let hash = libs.constants.redisKeys.questionString + ":" + quiz._id.toString();

    let reOrderedContent = quiz.quizContent;
    let segments = quiz.quizSegments;
    let quizId = quiz._id;
    if (!(reOrderedContent && reOrderedContent.length && currentQuestionId && quizId)) {
        logger.debug(`getNextAndPreviousLinkFromReorderedContent parameters not valid `, reOrderedContent, " id ", currentQuestionId, ` quizId ${quizId}`);
        logger.error(`getNextAndPreviousLinkFromReorderedContent parameters not valid`);
        callback({ 'error': 'parameters not valid' });
        return;
    }
    let isFound, i, segmentData, linksObj = {
			quiz: {
				updatedAt: quiz.updatedAt
			}
		}, contentObj;

    for (i = 0; i < reOrderedContent.length; i += 1) {
        if (reOrderedContent[i]._id === currentQuestionId) {
            contentObj = reOrderedContent[i]
            isFound = true;
            break;
        }
    }
    if (isFound) {
        linksObj.segmentData = libs.util.getSegmentFromIndexOfContent(segments, i);
        // linksObj.contentObj = contentObj
    } else {
        callback(null, null);
        return;
    }
    if (i !== reOrderedContent.length) {
        if (isNextRequired && i !== reOrderedContent.length - 1)
            linksObj.nextLink = libs.util.getAttemptLink(libs.constants.contentTypeString.question, reOrderedContent[i + 1]._id, quizId, true);

        if (isPreviousRequired && i !== 0)
            linksObj.previousLink = libs.util.getAttemptLink(libs.constants.contentTypeString.question, reOrderedContent[i - 1]._id, quizId, true);

        let contentIndexInSegment = linksObj.segmentData.contentIndexInSegment;
        if ((!quiz.revisitAllowed) && (!linksObj.segmentData.isError &&
            contentIndexInSegment + 1 === linksObj.segmentData.segmentObj.count)) {
            if (Object.prototype.hasOwnProperty.call(linksObj, 'nextLink'))
                delete linksObj.nextLink;
        }
    } else {
        callback(null, linksObj);
        return;
    }
    services.RedisServices.redis("hset", hash, libs.constants.redisKeys.links + ':' + currentQuestionId, JSON.stringify(linksObj));
    callback(null, linksObj);
};

var saveProjectSubmissionAttempt = function ( payload, sessionObj, callback ) {
    let queryObj = {'adminProjectId': payload.adminProjectId, 'userId': sessionObj.userId };
    getOneUserAnswer(
        queryObj, {}, {}, function (errorUserAnswer, userAnswer) {
            if (errorUserAnswer) {
                logger.debug("saveProjectSubmissionAttempt error getting user answer ", queryObj, " error ", errorUserAnswer);
                logger.error("saveProjectSubmissionAttempt error getting user answer");
                callback(errorUserAnswer, null);
            }
            else {
                if (!userAnswer) {
                    services.UserAnswerService.addUserAnswer(queryObj, function (errorCreate, resultCreate) {
                        if (errorCreate) {
                            logger.debug("saveProjectSubmissionAttempt error in creating useranswer-", errorCreate);
                            logger.error("saveProjectSubmissionAttempt error in creating useranswer");
                            callback(errorCreate, null);
                        }
                        createProjectAttemptInDb(resultCreate._id, payload, callback);
                    });
                } else {
                    createProjectAttemptInDb(userAnswer._id, payload, callback);
                }
            }
        });
};

var createProjectAttemptInDb = function( userAnswerId, payload, callback){
    if (!( payload && userAnswerId && callback && typeof callback === 'function' )) {
        logger.debug("createProjectAttemptInDb parameters invalid---", "payload", payload, "userAnswerId-", userAnswerId );
        logger.error("createProjectAttemptInDb parameters invalid");
        callback("error in parametrs ", null);
        return;
    }
    userAttemptController.createProjectAttempt(userAnswerId, payload, function (error, result) {
        if (error) {
            callback(error, null);
        }
        else {
            callback(error, result);
        }
    });
};

var getDocCount = function ( criteria, callback) {
    services.UserAnswerService.getDocCount( criteria, callback )
};

var attemptCourseQuiz = function ( payload = {} , callback) {
    let { quizId, courseId, req, session } = payload;
    let { userId } = session;
    payload.userId = userId;
    let { reAttempt } = req.query;
    if (!( payload && courseId && quizId && userId && callback && typeof callback === 'function' )) {
        logger.error("parameters invalid attemptCourseQuiz ", "payload-", payload, "course-", courseId, "quizId-", quizId, "userId-", userId);
        callback({ 'error' : "error in parametrs "});
        return false;
    }
    courseController.getOneCourse({'_id': courseId},
        {
            'languagesAllowed': 1,
            '_id': 1,
            'title': 1,
            'courseContent': 1,
            'courseSegments': 1,
            'attemptInSequence': 1,
            'disableCopyPaste': 1
        }, { }, (errorCourse, course) => {
            if (errorCourse) {
                logger.debug("Error getting course attemptCourseQuiz" , errorCourse, "courseId--", courseId);
                callback({ 'error' : "course id is invalid attemptCourseQuiz"});
                return;
            } else if (!course) {
                logger.debug(" course id is invalid attemptCourseQuiz" , "courseId--", courseId);
                callback("course id is invalid startCourseAttempt");
                return;
            }

            if ( course.attemptInSequence &&
                 session.role == libs.constants.roleNumberFromString.user ) {
                let segmentData = libs.util.getSegmentFromIdOfContent(course , quizId);
                const { segmentObj } = segmentData;
                course.segmentData = segmentData;
                isPrevQuesAttempted(course, quizId, session.userId, (err, isAllowed) => {
                    if (err) {
                        logger.debug("Error getting course attemptCourseQuiz", err, "courseId--", courseId);
                        callback({ 'error': "Error getting course isPrevQuesAttempted" });
                        return;
                    }

                    if (!isAllowed) {
                        logger.debug("Error Prev ques is not attempted courseId--", courseId);
                        callback({ 'error': "Error Prev ques is not attempted" });
                        return;
                    }
                    if( reAttempt && !session.reAttemptTime ){
                        //TODO - Add counter or other things in reattempt
                        session.reAttemptTime = Date.now();
                    }
                    getContentForQuizAttempt(payload, callback);
                })
            }
            else {
                if (reAttempt && !session.reAttemptTime) {
                    session.reAttemptTime = Date.now();
                }
                    getContentForQuizAttempt(payload, callback);
            }
        });
};

var attemptCourseProject = function ( payload = {} , callback) {
    let { projectId, courseId, req, session } = payload;
    let { userId } = session;
    payload.userId = userId;
    //if ( req.query && req.query.fork )   payload.fork = true;
    if (!( payload && courseId && projectId && userId && callback && typeof callback === 'function' )) {
        logger.error("parameters invalid attemptCourseProject ", "payload-", payload, "course-", courseId, "projectId-", projectId, "userId-", userId);
        callback({ 'error' : "error in parametrs "});
        return false;
    }
    courseController.getOneCourse({'_id': courseId},
        {
            'languagesAllowed': 1,
            '_id': 1,
            'title': 1,
            'courseContent': 1,
            'courseSegments': 1,
            'attemptInSequence': 1,
            'disableCopyPaste': 1
        }, { }, (errorCourse, course) => {
            if (errorCourse) {
                logger.debug("Error getting course attemptCourseProject" , errorCourse, "courseId--", courseId);
                callback({ 'error' : "course id is invalid attemptCourseProject"});
                return;
            } else if (!course) {
                logger.debug(" course id is invalid attemptCourseProject" , "courseId--", courseId);
                callback("course id is invalid startCourseAttempt");
                return;
            }

            if ( course.attemptInSequence &&
                 session.role == libs.constants.roleNumberFromString.user ) {
                let segmentData = libs.util.getSegmentFromIdOfContent(course , projectId);
                const { segmentObj } = segmentData;
                course.segmentData = segmentData;
                isPrevQuesAttempted(course, projectId, session.userId, (err, isAllowed) => {
                    if (err) {
                        logger.debug("Error getting course attemptCourseProject", err, "courseId--", courseId);
                        callback({ 'error': "Error getting course isPrevQuesAttempted" });
                        return;
                    }

                    if (!isAllowed) {
                        logger.debug("Error Prev ques is not attempted courseId--", courseId);
                        callback({ 'error': "Error Prev ques is not attempted" });
                        return;
                    }
                    
                    getContentForProjectAttempt(payload, callback);
                })
            }
            else {
                getContentForProjectAttempt(payload, callback);
            }
        });
};

var updateParamsInQuizSubmittedSegmentFOrReattempt = function( payload, cb ){
    const { quizId, courseId, userId } = payload;

    userQuizSubmittedSegmentController( { userId, courseId, courseQuizId : quizId }, {}, { $set : { oldQuizAttempt : true }} , ( err, res )=>{
        if( err ){
            console.error("Error while remove old quiz attempts in course");
            return cb( err )

        }
        cb(err, res );
    })
}

var getContentForQuizAttempt = function (payload, callback) {
    let { quizId, courseId, req, session } = payload;
    let { userId }  = session;
 ///test/startQuiz/
    quizController.getOneQuiz({ _id : quizId },{}, {}, function (error, ques) {
        let queryObj = { 'courseQuizId': quizId, 'userId': userId, courseId };
        if (error) {
            logger.debug(`getContentForQuestionAttempt Error getting Question`);
            logger.error(`getContentForQuestionAttempt Error getting Question`);
            callback(error, null);
        } else {
            updateUserAnswer(
                queryObj, { 'upsert': true, 'setDefaultsOnInsert': true }, { $set: { 'lastActive': Date.now() } },
                function (errorUserAnswer, userAnswer) {
                    if (errorUserAnswer) {
                        logger.debug("getContentForQuestionAttempt error getting user answer ", queryObj);
                        callback(errorUserAnswer, null);
                    }
                    else {
                       if( userAnswer.completed ){
                            logger.debug("Quiz Already completed");
                            callback({ error : "Quiz Already completed, Please attempt next item.", code : 1 });
                            return;
                       } else {
                            session.quizId = quizId;
                            session.lastSubmittedSegment = -1;
                            let queryObj = { userId, quizId, courseId }, logout = false, quizTimeInSec;

                           userQuizSubmittedSegmentController.getOneUserQuizSubmittedSegment(
                               queryObj, { 'startTime': 1, 'endTime': 1, 'extraTime': 1, 'quizSubmittedSegments': 1 }, {}, (errUserQuiz, resultUserQuiz) => {
                                   if (errUserQuiz) {
                                       logout = true;
                                       logger.error(`getContentForQuizAttempt in getting getOneUserQuizSubmittedSegment `, errUserQuiz, ` queryObj is`, queryObj);
                                   }
                                   if (resultUserQuiz && resultUserQuiz.hasOwnProperty('extraTime')) {
                                       quizTimeInSec += resultUserQuiz.extraTime * 60;
                                   }
                                   if (resultUserQuiz && ( resultUserQuiz.endTime || 
                                     ( (req.currentTime || Date.now()) - new Date(resultUserQuiz.startTime) )  >= quizTimeInSec * 1000 ) ) {
                                       let updateObj = { $set: { startTime: Date.now(), quizSubmittedSegments: [] }, $unset: { endTime: "" } }
                                       delete session.reStartTime;
                                       userQuizSubmittedSegmentController.updateUserQuizSubmittedSegment(queryObj, updateObj, {}, (errUpdate, resultUpdate) => {
                                           if (errUpdate) {
                                               console.error("Error while resetting userquizsubmitted segment", errUpdate)
                                               return callback(errUpdate)
                                           }
                                           callback(errUpdate, { redirect: '/test/startQuiz/' + req.session.quizId + '?reset=true' })

                                       })
                                   } else {


                                       if ((!logout && resultUserQuiz && resultUserQuiz.startTime)) {
                                           req.session.lastSubmittedSegment = (resultUserQuiz.quizSubmittedSegments &&
                                               resultUserQuiz.quizSubmittedSegments.length) ?
                                               resultUserQuiz.quizSubmittedSegments[resultUserQuiz.quizSubmittedSegments.length - 1] :
                                               -1;

                                           timeElapsedInMilliSec = (req.currentTime || Date.now()) - new Date(resultUserQuiz.startTime);
                                           if (timeElapsedInMilliSec >= (quizTimeInSec * 1000)) {
                                               logout = true;
                                               logger.error("Trying to open test after test time has finished.");
                                               quizController.submitQuiz({ quizId,userId, courseId }, Date.now(), (errSubmit, resultSubmit) => {
                                               });
                                           } else {
                                               callback(errUserQuiz, { redirect: '/test/startQuiz/' + req.session.quizId })
                                           }

                                       } else {
                                           callback(errUserQuiz, { redirect: '/test/startQuiz/' + req.session.quizId })
                                       }
                                   }
                                });
                       }
                    }
                });
        }
    });
} 

module.exports = {
    getUserAnswer: getUserAnswer, attemptCourseQuiz, getContentForQuizAttempt, 
    getOneUserAnswer: getOneUserAnswer,
    updateUserAnswer: updateUserAnswer,
    startCourseAttempt: startCourseAttempt,
    createUserTutorialAttempt: createUserTutorialAttempt,
    saveSubjectiveAttempt: saveSubjectiveAttempt,
    setQuestionValuesFromUserAttempt: setQuestionValuesFromUserAttempt,
    getNextAndPreviousQuestionAttemptLink: getNextAndPreviousQuestionAttemptLink,
    attemptCourseTutorial: attemptCourseTutorial,
    getNextContentLinkCourse: getNextContentLinkCourse,
    performAggregation: performAggregation,
    getAttemptLink: getAttemptLink,
    getUserAnswerPagination: getUserAnswerPagination,
    startQuizAttempt: startQuizAttempt,
    attemptQuizTutorial: attemptQuizTutorial,
    getQuizSubmittedAttempt: getQuizSubmittedAttempt,
    getNextAndPreviousLinkFromReorderedContent: getNextAndPreviousLinkFromReorderedContent,
    saveProjectSubmissionAttempt :saveProjectSubmissionAttempt,
    getDocCount : getDocCount,
    attemptCourseProject,
    getQuestionFromDb,
    updateManyUserAnswer,
    addUserAnswer,
};
