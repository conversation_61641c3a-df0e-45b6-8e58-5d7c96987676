let request = require('request');
let libs = require("../Lib")
let services = require("../Services")
let async = require('async');

const removeDataFromRAM = async function (payload = "{}") {
    try {

        payload = JSON.parse( payload);
        let { id, type } = payload;
        const { cachingKeys } = libs.constants;

        if (!id) {
            res.send({ msg: "id not present in request" });
            return;
        }

        if (!type) {
            res.send({ msg: "type not present in request" });
            return;
        }
        let cachingKeyObj;
        switch (type) {
            case cachingKeys.question.type:
                cachingKeyObj = cachingKeys.question;
                break;
            case cachingKeys.quiz.type:
                cachingKeyObj = cachingKeys.quiz;
                break;
            default:
                console.error("invalid caching key type", type)
        }

        if (!cachingKeyObj)
            return res.json({ error: 'key type not valid' })

        console.log("caching key obj", cachingKeyObj, "id = ", id, " type = ", type )
        const { processMapName, redisKeyPrefix } = cachingKeyObj
        let redisKey = `${redisKeyPrefix}${id}`
        await services.RedisServices.redis('del', redisKey)
        if (!Object.prototype.hasOwnProperty.call(global[processMapName], id)) {
            console.log("id not present in process ram")
            return ({ msg: "id not present in process ram" });
        }

        delete global[processMapName][id];
        console.log("Content removed successfully")
        return ({ msg : "success"});
    } catch (e) {
        console.error("Error while removing data from ram", e);
        return ({ error: e.message });
    }
}

const removeProcessData = function ({ req, res, next, payload } ) {
    let { id, type } = req ? req.query || {} : payload;
    const { cachingKeys } = libs.constants;
    if (!id) {
        console.log("Id not present in request");
        if( res )
            res.send({ msg: "id not present in request" });
        return;
    }

    if (!type) {
        console.log("type not present in request");
        if( res )
            res.send({ msg: "type not present in request" });
        return;
    }
    let cachingKeyObj;
    switch (type) {
        case cachingKeys.question.type:
            cachingKeyObj = cachingKeys.question;
            break;
        case cachingKeys.quiz.type:
            cachingKeyObj = cachingKeys.quiz;
            break;
        default:
            console.error("invalid caching key type", type)
    }

    if (!cachingKeyObj)
        return res ? res.json({ error: 'key type not valid' }) : ""

    console.log("caching key obj", cachingKeyObj);
    let obj = JSON.stringify({ id,type})
    removeDataFromRAM(obj);
    console.log("publishing", libs.constants.redisKeys.ramUpdateChannel , obj )
    services.RedisServices.redis('publish', libs.constants.redisKeys.ramUpdateChannel, obj )
    return;
}

module.exports = {
    removeProcessData,
    removeDataFromRAM
}
