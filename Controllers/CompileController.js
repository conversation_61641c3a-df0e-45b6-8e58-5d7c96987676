const path = require('path');
const fs = require('fs');
const async = require('async');
const md5File = require('md5-file')
var exec = require('child_process').exec;
var request = require('request');
const mongoose = require('mongoose');
const uniqid = require('uniqid');
const _ = require('lodash');

const questionController = require("../Controllers/QuestionController");
const userAttemptController = require("../Controllers/UserAttemptController");
const userController = require("../Controllers/UserController");
const userAnswerController = require("../Controllers/UserAnswerController");
const userQuizSubmittedSegmentController = require("../Controllers/UserQuizSubmittedSegmentController");
const sessionController = require("../Controllers/SessionController");
const quizController = require("../Controllers/QuizController");
const crypto = require('crypto');

const services = require('../Services/');
const libs = require("../Lib");
const AwsSpaceController = require('./AwsSpaceController');

const {constants} = libs;
logger = console;
const {redisKeys} = constants;

const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
};
const options = {
    'url': '/compile',
    'interactiveUrl': '/compileInteractive',
    'method': 'POST',
    'headers': headers,
};

var compileQuestion = function (payload, socketObj) {
    let obj = { "payload": payload }
    if ( socketObj ) {
        obj.socketId = socketObj.id ;
        obj.payload.socketId = socketObj.id;
    }
    sendCompileRequestAfterTimeout(obj);
};

var isValidMCQData = function (obj) {
    return Object.prototype.hasOwnProperty.call(obj.payload, 'questionId');
};

var getMCQResult = function (data, socket, cb ) {
    let obj = {};
    obj.payload = data;
    obj.socketId = socket && socket.id;
    let isCallback = cb && typeof cb === 'function';
    let onlyResultRequired = data && data.onlyResultRequired;           // Only Result required, no db write and socket emission
    if (!isValidMCQData(obj)) {
        logger.debug("getMCQResultAfterTimeout object payload was invalid ", obj.payload);
        logger.error("getMCQResultAfterTimeout object payload was invalid ");
        if( isCallback ){
            cb({ error : "Invalid payload"});
        }
        if ( ! onlyResultRequired ){
            sendNotificationToAdmin(obj);
            sendDataToSocket(io, obj.socketId, 'ERROR', "payload was invalid ", libs.constants.questionTypeNumeric.questionTypeMCQ);
        }
        return;
    }
    if (!obj.payload.inputMCQ && !obj.payload.isInvalidAttempt) {
        logger.debug("getMCQResultAfterTimeout inputMCQ object payload was invalid ", obj.payload);
        logger.error("getMCQResultAfterTimeout inputMCQ object payload was invalid");
        obj.payload.inputMCQ = obj.payload.chk || "";
        // sendNotificationToAdmin(obj);
    }
    services.RedisServices.redis("get", obj.payload.questionId, (err, resultMCQ) => {
        if (err) {
            logger.error("error in getting result MCQ corresponding to quetion id " + obj.payload.questionId);
            if( isCallback )
                cb({ error : "Error while fetching mcq result"});
            return;
        }
        try {
            if (resultMCQ) {
                resultMCQ = JSON.parse(resultMCQ);
                let resultMCQCopy = {"result": resultMCQ.result};
                resultMCQCopy.inputMCQ = obj.payload.inputMCQ;
                if (resultMCQCopy.inputMCQ) {
                    let valueMCQ = resultMCQCopy.inputMCQ.replace("chkOpt", "");
                    let selectedOption = parseInt(valueMCQ, 10) - 1;
                    // let negativeScore = resultMCQ.negativeScore 
                    // ? ( isNaN( resultMCQ.negativeScore ) ? 0 : -resultMCQ.negativeScore) 
                    //  : 0
                    let negativeScore = -1 * ( parseInt(resultMCQ.negativeScore) || 0 );
                    obj.isMCQCorrect = resultMCQ.result.indexOf(selectedOption) !== -1;
                    obj.testCaseComparisonResult = {'totalScore': ( obj.isMCQCorrect ? resultMCQ.score : negativeScore )};
                } else {
                    logger.debug("No input chosen ", obj);
                    obj.testCaseComparisonResult = {'totalScore': 0};
                }
                if ( ! onlyResultRequired ) {
                    if (!obj.payload.questionSubmission)
                        sendDataToSocket(io, obj, 'getmcqresult', resultMCQCopy, libs.constants.questionTypeNumeric.questionTypeMCQ);
                    else
                        saveAttemptInDb(obj, resultMCQCopy, libs.constants.questionTypeNumeric.questionTypeMCQ, cb );
                }
                if (isCallback)     cb(null, obj);
                return;
            }
        } catch (exception) {
            var msgStr = `exception mesaage-\n : ${exception.message} \nstackTrace-${exception.stack} \nresultMCQ : ${resultMCQ.inputMCQ}`;
            logger.debug("-----------", "Error in parsing result from redis resultMCQ :", resultMCQ, "userObj : ", obj, "time : ", Date.now(), "-------");
            logger.error("Error in parsing result from redis resultMCQ :" );
            if( isCallback ){
                cb({ error : "Error while parsing result from redisMCQ"})
            }
            libs.util.sendEmail('<EMAIL>', ' Error in Parsing MCQ Data', msgStr, process.env.DEFAULT_EMAIL,(errMail, resultMail) => {
                if (errMail) {
                    logger.debug("error in sending mail for mcq ",errMail);
                    logger.error(`error in sending mail for mcq`);
                    return;
                }
            });
            return;
        }
        questionController.getQuestionFromId(obj.payload.questionId, (errorQues, question) => {
            if (errorQues) {
                if ( ! onlyResultRequired && ! obj.payload.questionSubmission )
                    sendDataToSocket(io, obj, 'ERROR', "question id is not valid", libs.constants.questionTypeNumeric.questionTypeMCQ);
                else {
                    if (isCallback) cb({error : "Error while fetching mcq question"}) 
                }
                return;
            }
            else if (!question) {
                if ( ! onlyResultRequired && ! obj.payload.questionSubmission )
                    sendDataToSocket(io, obj, 'ERROR', "question not present", libs.constants.questionTypeNumeric.questionTypeMCQ);
                else {
                    if (isCallback) cb({error : "Error while fetching mcq question"}) 
                }
                return;
            }
            let result = {"result": question.questionTypeMCQ.correctAnswers};
            let resultCopy = {
                "result": question.questionTypeMCQ.correctAnswers,
                'explanation': question.questionTypeMCQ.answer,
                'score': question.score,
                'negativeScore' : question.negativeScore,
            };
            result.inputMCQ = obj.payload.inputMCQ;
            if (result.inputMCQ) {
                let valueMCQ = result.inputMCQ.replace("chkOpt", "");
                let selectedOption = parseInt(valueMCQ, 10) - 1;
                let negativeScore = -1 * ( parseInt(resultCopy.negativeScore) || 0 );
                obj.isMCQCorrect = resultCopy.result.indexOf(selectedOption) !== -1;
                obj.testCaseComparisonResult = {'totalScore': obj.isMCQCorrect ? resultCopy.score : negativeScore};
            } else {
                obj.testCaseComparisonResult = {'totalScore': 0};
            }
            if ( ! onlyResultRequired ) {
                if (!obj.payload.questionSubmission)
                    sendDataToSocket(io, obj, 'getmcqresult', result, libs.constants.questionTypeNumeric.questionTypeMCQ);
                else
                    saveAttemptInDb(obj, result, libs.constants.questionTypeNumeric.questionTypeMCQ, cb);
            }
            services.RedisServices.redis("set", question._id.toString(), JSON.stringify(resultCopy)); // Looks redundant
            if (isCallback)     cb(null, obj);
            return obj;
        });
    });
};

var isValidData = function (obj) {
    let result = ( Object.prototype.hasOwnProperty.call(obj.payload, 'questionId') &&
        ( Object.prototype.hasOwnProperty.call(obj.payload, 'isStepwise') ||
            Object.prototype.hasOwnProperty.call(obj.payload, 'isTutorialQuestion') )  );
    if (!obj.payload.isStepwise) {
        result = Object.prototype.hasOwnProperty.call(obj.payload, 'language') &&
            ( Object.prototype.hasOwnProperty.call(obj.payload, 'code') 
            || Object.prototype.hasOwnProperty.call(obj.payload, 'js') || 
            Object.prototype.hasOwnProperty.call(obj.payload, 'html') || 
            Object.prototype.hasOwnProperty.call(obj.payload, 'css'));
    }
    return result;
};

var sendCompileRequestAfterTimeout = function (obj) {
    //services.RedisServices.redis("set", libs.constants.redisKeys.isCompilationResponsePending + global.workerIndex, libs.constants.redisKeys.compilationInProgress);
    // checking if type of question is multiple answer and we already have its result in cache
    if (obj.payload.isStepwise) {
        services.RedisServices.redis("get", obj.payload.questionId, (errStepwise, result) => {
            if (errStepwise) {
                logger.debug("error in getting result corresponding to key sendCompileRequestAfterTimeout ", obj.payload.questionId, "error--", errStepwise);
                logger.error("sendCompileRequestAfterTimeout error in getting result corresponding to key " + obj.payload.questionId);
                services.RedisServices.redis("set", libs.constants.redisKeys.isCompilationResponsePending + global.workerIndex, libs.constants.redisKeys.emptyString);
                return;
            }
            if (result) {
                result = JSON.parse(result);
                result.outputArray = obj.payload.stepwiseUserInput;
                result.txtCodeAt = obj.payload.code;
                result.progLang = obj.payload.language;
                obj.testCaseComparisonResult = libs.util.compareTestCaseAndResult(result.testCase, obj.payload.stepwiseUserInput, "1");
                if (obj.payload.executionType === libs.constants.codingQuestionExecutionType.resultOnly) {
                    if (!obj.payload.questionSubmission)
                        sendDataToSocket(io, obj, 'compile', obj.testCaseComparisonResult, libs.constants.questionTypeNumeric.questionTypeStepwise);
                    else
                        saveAttemptInDb(obj, obj.testCaseComparisonResult, libs.constants.questionTypeNumeric.questionTypeStepwise, null)
                }
                else {
                    if (!obj.payload.questionSubmission)
                        sendDataToSocket(io, obj, 'compile', result, libs.constants.questionTypeNumeric.questionTypeStepwise);
                    else
                        saveAttemptInDb(obj, result, libs.constants.questionTypeNumeric.questionTypeStepwise, null)
                }
                // if (obj.payload.executionType === libs.constants.codingQuestionExecutionType.resultOnly)
                //     sendDataToSocket(io, obj, 'compile', obj.testCaseComparisonResult, libs.constants.questionTypeNumeric.questionTypeStepwise);
                // else
                //     sendDataToSocket(io, obj, 'compile', result, libs.constants.questionTypeNumeric.questionTypeStepwise);
                services.RedisServices.redis("set", libs.constants.redisKeys.isCompilationResponsePending + global.workerIndex, libs.constants.redisKeys.emptyString);
                return;
            } else {
                    compilePayloadObj(obj);
            }
        });
    } else
        compilePayloadObj(obj);
};

var compilePayloadObj = async function (obj) {
    // checking validity of obj
    if (!isValidData(obj)) {
        logger.debug(" object payload was invalid compilePayloadObj", obj.payload);
        logger.error(" object payload was invalid compilePayloadObj");
        sendNotificationToAdmin(obj);
        sendDataToSocket(io, obj, 'ERROR', "payload was invalid ", libs.constants.questionTypeNumeric.questionTypeStepwise);
        services.RedisServices.redis("set", libs.constants.redisKeys.isCompilationResponsePending + global.workerIndex, libs.constants.redisKeys.emptyString);
        return;
    }
    obj.payload.codeId = obj.payload.codeId || crypto.randomUUID();
    obj.payload.codeType = libs.constants.compileServerConstants.codeType.MAX;
    if (1) {

        if (obj.payload.isAddingInDb) {
            obj.payload.codeType = libs.constants.compileServerConstants.codeType.DEFAULT_CODE;
        }
        if (obj.payload.isIdeRequest) {
            obj.payload.codeType = libs.constants.compileServerConstants.codeType.IDE_CODE;
            compileIdeRequest(obj);
            return;
        }
        let question = await getCodingQuestionFromDb(obj)
        if (!question) {
            console.log("question not present " + obj.payload.questionId);
            sendDataToSocket(io, obj, 'ERROR', "question not present");
            return;
        }

        if (obj.payload.role === libs.constants.roleNumberFromString.admin)
            obj.payload.executionType = libs.constants.codingQuestionExecutionType.resultWithTestCases;

        obj.txtCodeAt = obj.payload.code;
        if (!setPayloadForCompilation(obj, question)) {
            sendDataToSocket(io, obj, 'ERROR', "Sample Test case not present for question");
            return;
        }
        if (obj.payload.isDefaultCode && !obj.payload.reevaluate) {
            let outputObj = obj.payload.defaultResult;
            outputObj.langid = obj.payload.language;
            outputObj.code = obj.payload.code;
            setOutputDataAfterCompilation(obj, outputObj, (err, question) => {
                if (err) {
                    sendDataToSocket(io, obj, 'ERROR', "Error in fething question data");
                    return;
                };
                sendDataToSocket(io, obj, 'compile', outputObj, question.type);
                services.RedisServices.redis("set", libs.constants.redisKeys.isCompilationResponsePending + global.workerIndex, libs.constants.redisKeys.emptyString);
            });
            return;
        }

        // obj.question = {
        //     questionTypeCoding: {
        //         tc: question.questionTypeCoding.testCase,
        //         multipleTestCases: question.questionTypeCoding.multipleTestCases
        //     }, type: question.type
        // }; //tc ---> testCase

        let { codeId, codeObj, courseId, quizId, questionId, executionType, isCompleted, isCustomInput, isStepwise, language,
            role, sid, userId, userAnswerId, questionSubmission, time, runSampleTC, js, html, css, isInvalidAttempt } = obj.payload;


        let code, txtCodeAt;
        if (questionSubmission) {
            code = obj.payload.code
            txtCodeAt = obj.txtCodeAt;
        }
        // Reference to old payload
        let temp = obj.payload;

        // Creating New Payload with only necessary keys
        obj.payload = {
            codeId, codeObj, courseId, questionId, executionType, isCompleted, isCustomInput, isStepwise, language, quizId,
            role, sid, userId, userAnswerId, questionSubmission, code, time, txtCodeAt, runSampleTC, onlyResultRequired: obj.payload.onlyResultRequired
        }

        if (js || css || html) {
            obj.payload.js = js || "";
            obj.payload.css = css || "";
            obj.payload.html = html || "";
            isInvalidAttempt ? obj.payload.isInvalidAttempt = true : "";
        }

        if (temp.isAddingInDb) {
            obj.payload.isAddingInDb = true;
            obj.question = { _id: question._id };
        }
        let key = libs.constants.redisKeys.codePayload + ":" + obj.payload.codeId;
        if (questionSubmission && !temp.reevaluate) {
            let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${obj.payload.quizId}:${obj.payload.userId}:${obj.payload.questionId}`;
            try {
                let submissionId = mongoose.Types.ObjectId()
                console.log("Saving Question 404, UserId = ", userId, " Ques Id = ", questionId, " Container Id = ", quizId || courseId, ` SubmissionId = ${submissionId}`, ` CodeId = ${codeId}`);
                let redisObj = {
                    'userProgram': obj.txtCodeAt,
                    'userLanguage': obj.payload.language,
                    'time': obj.payload.time,
                    'userCompilationError': "404",
                    submissionId,
                    questionType :  libs.constants.questionTypeNumeric.questionTypeCoding,
                    _id : mongoose.Types.ObjectId(),
                    codeId: codeId,
                }
                obj.payload.submissionId = submissionId;
                if (obj.payload.language == constants.langStringToLangCode['React-Jest']) {
                    redisObj.html = obj.payload.html || '';
                    redisObj.css = obj.payload.css || '';
                    redisObj.js = obj.payload.js || '';
                    redisObj.questionType =  libs.constants.questionTypeNumeric.questionTypeWeb
                }
                let submittedQuestionKeys = redisKeys.ongoingQuiz + ":" + obj.payload.userId + ":" + obj.payload.quizId + ":" + redisKeys.submittedQuestions;
                await  services.RedisServices.redis('sadd', submittedQuestionKeys, obj.payload.questionId)
                services.RedisServices.redis2('rpush', userAttemptListByQuesRedisKey, JSON.stringify(redisObj))
            } catch (e) {
                console.log("ERror in redisObj = ", e);
            }

            // saveAttemptInDb(obj, { txtCodeAt: obj.txtCodeAt, progLang: obj.payload.language, errors: "404" },
            //     question.type, (err, resultSubmit) => {
            //         if (err) {
            //             console.error("Error while saving submission before compilation = ", obj);
            //             sendDataToSocket(io, obj, 'ERROR', "Error while saving submission");
            //             return
            //         }

            saveSubmittedQuestion(obj, question.type);
            // const { userAttemptObj, result } = resultSubmit;
            // if (userAttemptObj && userAttemptObj._id) {
            //     obj.userAttemptId = result._id
            //     obj.attemptDataId = userAttemptObj._id;
            //     delete obj.txtCodeAt;
            //     delete obj.payload.txtCodeAt;



            await services.RedisServices.redis("set", key, JSON.stringify(obj))
            sendPostRequest(temp, options.url, options.headers, function (error, response, body) {
                if (error) {
                    // console.log("Error in sending code to master compile server, Error = ", error);
                    services.RedisServices.redis("del", key);
                    sendDataToSocket(io, obj, 'ERROR', "error while sending data to compilation");
                    return;
                }
                console.log("Code sent to master compile server. Code Id = ", obj.payload.codeId);
            });

        } else {
            if (temp.reevaluate) {
                obj.payload.reevaluate = temp.reevaluate,
                    obj.payload.reevaluateObj = temp.reevaluateObj
            }
            services.RedisServices.redis("set", key, JSON.stringify(obj), (err) => {
                sendPostRequest(temp, options.url, options.headers, function (error, response, body) {
                    if (error) {
                        // console.log("Error in sending code to master compile server, Error = ", error);
                        services.RedisServices.redis("del", key);
                        sendDataToSocket(io, obj, 'ERROR', "error while sending data to compilation");
                        return;
                    }
                    console.log("Code sent to master compile server. Code Id = ", obj.payload.codeId);

                });
            });
        }

    }
};

var getCodingQuestionFromDb = async function (obj, callback) {
    let isCB = callback && typeof callback === 'function';
    try {
        let hash = libs.constants.redisKeys.questionString + ":" + (obj.payload.quizId ? obj.payload.quizId : obj.payload.courseId);
        let resultRedis = await services.RedisServices.redis("hget", hash, obj.payload.questionId )
        if (resultRedis) {
            let questionObj = JSON.parse(resultRedis);
            if( isCB )  callback(null, questionObj);
            return questionObj;
        }
            
        let question = await questionController.getOneQuestion( { _id : obj.payload.questionId }, {}, {} )
                   
        if (!question)
            throw new Error("question not present getCodingQuestionFromDb" + obj.payload.questionId)
        
        if( isCB)    callback(null, question);
        return question;
    } catch (e) {
        console.log("Error while getting question from db", e);
        if (!obj.payload.questionSubmission)
            sendDataToSocket(io, obj, 'ERROR', "question id is not valid", libs.constants.questionTypeNumeric.questionTypeCoding);
        return false;
    }
};

var setOutputDataAfterCompilationForWeb = function (data = {}) {
    let { obj, outputObj, question, cb } = data;
    let { payload } = obj;
    let { questionTypeWeb } = question;
    let { testCase , isReactQuestion } = questionTypeWeb || {};
    testCase = testCase ||  []
    // outputObj.progLang = langId;
    if (outputObj.errors) {
        return cb(null, question);
    }
    let counter = 0;
    let outputArray = [];
    try{
    outputObj.output = JSON.parse( outputObj.output || "{}") 
    } catch ( e ){
        console.log("Error while parsing response", outputObj );
        for(let  i = 0; i < questionTypeWeb.testCase.length; i+=1 )
            outputArray[i] = false;
        outputObj.outputArray = outputArray;
        return cb( null, question);
    }
    outputArray = [...(outputObj.output.output || [] )];
    if( outputArray.length < questionTypeWeb.testCase.length ){
        for( let i = outputArray.length; i < questionTypeWeb.testCase.length ; i += 1 ){
            outputArray[i] = false;
        }
    }
    let testCaseComparisonResult = libs.util.compareTestCaseAndResult(testCase, outputArray, false, {  quesType : constants.questionTypeNumeric.questionTypeWeb })
    if (testCaseComparisonResult === null) {
        outputObj.errors = "Something wrong with outputobj and testCases ";
        outputObj.outputArray = outputArray;
    } else {
        outputObj.outputArray = outputArray;
    }
    delete outputObj.output;
    obj.testCaseComparisonResult = testCaseComparisonResult;
    outputObj.testCase = testCase;
    payload.html = payload.html || questionTypeWeb.html || "";
    payload.js = payload.js || "";
    payload.css = payload.css || questionTypeWeb.css || "";
    cb( null, question)
}

var setOutputDataAfterCompilation = function (obj, outputObj, cb) {

    getQuestionForOutputManipulation(obj.payload, (err, question) => {
        try {
            if (err){
                return cb( err );
            }
            let langId = obj.payload.language;
            outputObj.txtCodeAt = obj.txtCodeAt;
            outputObj.codeId = obj.payload.codeId
            let outputArray = [];
            let { questionTypeCoding, questionTypeWeb, type } = question;

            if( langId == libs.constants.langStringToLangCode["React-Jest"] ){
                setOutputDataAfterCompilationForWeb( { obj, outputObj, question, cb });
                return;
            }

            let { multipleTestCases, tc } = questionTypeCoding;
            tc = tc || (questionTypeCoding.testCase || []);
            
            let { payload } = obj;
            let { executionType } = payload;

            if( 0 && payload.runSampleTC ){              // #2731 - Sample Test Cases Fail although code is Correct
                tc = tc.filter( testCaseObj =>{
                    return testCaseObj.sampleTest 
                })
            }
            outputObj.progLang = langId;

            try {
                if ( false && outputObj?.errors && typeof outputObj.errors === 'string') {
                    if (question?.questionTypeCoding && question.questionTypeCoding.testCase?.length) {
                        question.questionTypeCoding.testCase.forEach((element) => {
                            if (element?.codeproginputparams &&  typeof element.codeproginputparams === 'string') {
                                const allInputs = element.codeproginputparams.split('\n');
                                allInputs.forEach((singleInput) => {
                                    if (singleInput && typeof singleInput === 'string') {
                                        outputObj.errors = outputObj.errors.replace(singleInput.trim(), "");
                                        const elements = singleInput.split(' ');
                                        elements.forEach((singleElement) => {
                                            if (singleElement && typeof singleElement === 'string') {
                                                outputObj.errors = outputObj.errors.replace(singleElement.trim(), "");
                                            }
                                        });
                                    }
                                })
                                outputObj.errors = outputObj.errors.replace(element.codeproginputparams, "");
                            }
                        })
                    }
                }
            } catch (error) { 
                console.log('Error while removing input from the output', error);
            }


            if (outputObj.errors) {
                if (outputObj.errors.indexOf(libs.constants.coreDumpedString) !== -1 ||
                    outputObj.errors.indexOf(libs.constants.segmentationFaultString) !== -1
                ) {
                    outputObj.errors = "Segmentation Fault";
                }
            }
            if (outputObj.errors && (!outputObj.isCachedResult)) {
                if (langId == '8' || langId == '77' || langId == '7') {
                    arrangeFileNumber(obj, outputObj);
                }
                if (Object.prototype.hasOwnProperty.call(outputObj, 'code'))
                    delete outputObj.code;
                if (Object.prototype.hasOwnProperty.call(outputObj, 'output'))
                    outputObj.output = libs.util.encrypt(outputObj.output);
                
                return cb( null, question );
            }
            outputArray = [];
            if (!outputObj.errors) {
                outputArray = outputObj.output.split("OUTPUT:");
                outputArray.splice(0, 1);
            }
            if (question.questionTypeCoding.multipleTestCases) {
                outputArray = outputArray[0].split(libs.constants.endOfIterationString);
            }

            delete outputObj.code;
            // if custom input is given dont check flags and test cases
            if (obj.payload.isCustomInput) {
                outputObj.outputArray = [{ userOutput: outputArray[0], 'score': 0 }];
                return cb(null, question );
            }

            if (1 || obj.payload.executionType !== libs.constants.codingQuestionExecutionType.onlyCompile) {
                for (let i = 0; i < outputArray.length; i += 1) {
                    outputArray[i] = outputArray[i].replace(/[\r]/g, '');
                }
                let testCaseComparisonResult = libs.util.compareTestCaseAndResult(tc, outputArray)
                if (testCaseComparisonResult === null) {
                    outputObj.errors = "Something wrong with outputobj and testCases ";
                    outputObj.outputArray = outputArray;
                }
                else if ( obj.payload.executionType == libs.constants.codingQuestionExecutionType.onlyCompile) {
                    outputObj.outputArray = " Compilation Successful ";
                    obj.outputArray = outputArray;
                    outputObj.isCompleted = true;
                }
                else {
                    outputObj.outputArray = testCaseComparisonResult.userOutputArray;
                    delete testCaseComparisonResult.userOutputArray;
                    outputObj.isCompleted = testCaseComparisonResult && !payload.runSampleTC 
                    ? testCaseComparisonResult.totalTestCases === testCaseComparisonResult.testCasePassed : true;
                }
                obj.testCaseComparisonResult = testCaseComparisonResult;
                if (executionType != libs.constants.codingQuestionExecutionType.onlyCompile) {
                    outputObj.testCase = tc;

                    if (executionType == libs.constants.codingQuestionExecutionType.resultOnly) {
                        obj.outputArray = outputArray
                        outputObj.testCase = tc.map((testCaseObj, i ) => {
                            if( testCaseObj.sampleTest ) {
                                if( !payload.runSampleTC ){
                                    if( outputObj.outputArray[i] )
                                        outputObj.outputArray[i].userOutput = "" //clearing sample test case value
                                }
                                return { ...testCaseObj }
                            } else {
                                if( outputObj.outputArray[i] ){
                                    outputObj.outputArray[i].userOutput = "";
                                }

                            }
                            
                            return  { attemptInMultiLine : testCaseObj.attemptInMultiLine, _id: testCaseObj._id }
                        });
                    }
                }
            } else {
                outputObj.outputArray = " Compilation Successful ";
                obj.testCaseComparisonResult = null;
                obj.outputArray = outputArray;
            }
            if (Object.prototype.hasOwnProperty.call(outputObj, 'output') && outputObj.progLang != libs.constants.langStringToLangCode.JavaScript)
                outputObj.output = libs.util.encrypt(outputObj.output);
            cb( null, question );
        } catch (error) {
            logger.debug(" error is there setOutputDataAfterCompilation", obj);
            console.log(" Error = ", error);
            obj.testCaseComparisonResult = { testCasePassed: 0, totalScore: 0 }
            cb( null, { type : libs.constants.questionTypeNumeric.questionTypeCoding } );
        }
    })
};

var isValidResponseFromServer = function (outputObj) {
    return ( Object.prototype.hasOwnProperty.call(outputObj, 'output') &&
        Object.prototype.hasOwnProperty.call(outputObj, 'errors') &&
        Object.prototype.hasOwnProperty.call(outputObj, 'code') &&
        Object.prototype.hasOwnProperty.call(outputObj, 'langid') &&
        Object.prototype.hasOwnProperty.call(outputObj, 'time') );
};

var setInputStringForPayload = function (obj, question) {
    let isSampleTCPresent = false;
    let { payload } = obj;
    if (payload.language == libs.constants.langStringToLangCode["React-Jest"]) {
        payload.code = `${question.questionTypeWeb.testRunnerFileContent}${payload.js || payload.code}`
    } else {
        // if (!question.questionTypeCoding.multipleTestCases)
        //     inpuSeparationString = libs.constants.endOfInputString;
        // else {
        //     obj.payload.stdin += question.questionTypeCoding.testCase.length;
        //     obj.payload.stdin += " "
        // }
        obj.payload.testInputArr = [];
        obj.payload.testCaseCloudId = [];
        question.questionTypeCoding.testCase.forEach((testCaseObj) => {
            if (obj.payload.runSampleTC) {
                if (testCaseObj.sampleTest) {
                    isSampleTCPresent = true;
                    // obj.payload.stdin += testCaseObj.codeproginputparams;
                    // obj.payload.stdin += inpuSeparationString;
                }
            } else {
                // obj.payload.stdin += testCaseObj.codeproginputparams;
                // obj.payload.stdin += inpuSeparationString;
            }
            if ( testCaseObj.onCloud ) {
                let quesId = question && question._id && question._id.toString();
                let testId = testCaseObj._id && testCaseObj._id.toString();
                let testCasePath = path.join(libs.constants.awsS3Constants.getTestCaseStoragePath(quesId, testId));
                const locationUrl = AwsSpaceController.getLocation(testCasePath);
                obj.payload.testCaseCloudId.push(locationUrl);
                obj.payload.testInputArr.push(libs.constants.cloudTestCaseInputString || '\n');
                obj.payload.stdin += libs.constants.cloudTestCaseInputString;
            } else {
                obj.payload.testInputArr.push(testCaseObj.codeproginputparams || '\n');
            }
        });
        // if (question.questionTypeCoding.multipleTestCases)
        // obj.payload.stdin += libs.constants.endOfInputString;
    }

    return obj.payload.runSampleTC ? isSampleTCPresent : true;
};

var setPayloadForCompilation = function (obj, question) {
    let isValid = true;
    let { payload } = obj;
    const { executionType, questionSubmission } = payload;

    if (payload.language == libs.constants.langStringToLangCode["React-Jest"]) {
        payload.code = `${ question.questionTypeWeb.testRunnerFileContent}${payload.js || payload.code}`;
        return isValid;
    }
    if (payload.isStepwise) {
        payload.code = question.questionTypeCoding.codeprogcode;
        payload.stdin += libs.constants.endOfInputString;
    }
    else {
        if (payload.isCustomInput) {
            payload.testInputArr = [payload.stdin || '\n'];
            payload.isCustomInput = true;
            // payload.stdin += " ";
            // payload.stdin += libs.constants.endOfInputString;
        }

        question.questionTypeCoding.codeproglang.forEach((codeObj) => {
            if (codeObj.language === payload.language ) {
                
                let redisCodeObj = { };
                redisCodeObj.h = codeObj.codeComponents.head ?  codeObj.codeComponents.head.split('\n').length : 1;
                redisCodeObj.t = codeObj.codeComponents.tail ? codeObj.codeComponents.tail.split('\n').length : 1;
                payload.codeObj = redisCodeObj;

                let trimmedCode = payload.code;
                // trimmedCode.replace(/^\s*$[\n\r]{1,}/gm, '');
                if( payload.language == libs.constants.languageCodes.SQL || payload.language == libs.constants.languageCodes.MySql || payload.language == libs.constants.languageCodes.Oracle){
                    payload.code = '';
                    ( question.questionTypeCoding.testCase || [] ).forEach( ( testCaseObj,i ) => {
                        console.log(" index ", i , payload.code )
                        payload.code += codeObj.codeComponents.head + "\n" + 
                            trimmedCode + "\n" + 
                            codeObj.codeComponents.tail + "\n" + 
                            testCaseObj.codeproginputparams + "\n" + 
                            libs.constants.endOfInputString + "\n";
                    } )
                } else {
                    payload.code = codeObj.codeComponents.head + "\n" + payload.code + "\n" + codeObj.codeComponents.tail;
                }
                if ( 0 && codeObj.defaultCompileResult && codeObj.language != libs.constants.languageCodes.SQL
                    && codeObj.defaultTrimmedCode &&
                    codeObj.codeComponents.head + "\n" + trimmedCode + "\n" + codeObj.codeComponents.tail === codeObj.defaultTrimmedCode) {
                    payload.isDefaultCode = true;
                    payload.defaultResult = codeObj.defaultCompileResult;
                }
            }
        });
        if (!payload.isCustomInput) {
            if ( executionType !== libs.constants.codingQuestionExecutionType.onlyCompile) {
                isValid = setInputStringForPayload(obj, question);
            } else if ( questionSubmission ) {
                isValid = setInputStringForPayload(obj, question);
            } else {
                isValid = setInputStringForPayload(obj, question);
            }
        }
        payload.files = [];
        if (payload.courseFiles && payload.courseFiles.length) {
            payload.courseFiles.forEach((fileName) => {
                let filePath = path.join(libs.constants.PublicFolderString, libs.constants.uploadsFolderString,
                    libs.constants.courseFolderString, fileName);
                payload.files.push(filePath);
            })
        }
        if (question.files && question.files.length) {
            question.files.forEach((fileName) => {
                let filePath = path.join(libs.constants.PublicFolderString, libs.constants.uploadsFolderString,
                    libs.constants.questionFolderString, payload?.questionId?.toString(), fileName);
                payload.files.push(filePath);
            })
        }
    }
    return isValid;
};

var startTimer = function () {
    // setInterval(checkCompilationStatus, 200);
    global.dbSubmissionCounter = 0;
    let processData = [ libs.constants.cachingKeys.question, libs.constants.cachingKeys.quiz ]
    processData.map( ( cachingKeyObj)=>{
        global[ cachingKeyObj.processMapName] = {}
    })
    //it stores the number of submissions happended in partciular frame of time e.g. libs.constants.submissionQueueData.writeInterval
   
    global.proceeDataRemoverIntervalId = setInterval( function(){
        processData.map( ( cachingKeyObj )=>{
            let { processMapName, idlePeriod } = cachingKeyObj;
            console.log(`=========${processMapName}===========`)
            let processDataObj = global[ processMapName ];
            Object.keys( ( processDataObj || {} )).map( key =>{
                let obj = processDataObj[key]
                if( Date.now() > obj.lastUsed + ( idlePeriod ) ){
                    console.log(`Clearing key ${key} which is last used at ${ obj.lastUsed }`)
                    delete processDataObj[key];
                }
            })
            console.log(`=====================`)

        })
    }, libs.constants.processDataRemovalInterval)
    if ( parseInt(process.env.SUBMISSION_QUEUE_MANAGER) ) {
        console.log("Instance runnnig as Submission queue manager")
        startIntervals();
     
        // This interval is used for maintaining the speed of saving the submissions in db
        global.modifySubmissionIntervalId = setInterval(
            function () {
                modifySubmissionInterval()
            }, libs.constants.submissionQueueData.modifySubmissionInterval
        )

        // This interval is used for clearing the old counter data in different time slices.
        global.oldSlicesClearingIntervalId = setInterval(
            function () {
                clearOldTimeSlices()
            }, libs.constants.submissionQueueData.oldIntervalCleaningInterval
        )
    }  else {
        // This interval is used for sending the submission counter of instance
        console.log("Instance runnnig as main server")
    }

    setInterval(
        function(){
            let counter =  global.dbSubmissionCounter || 0;
            updateTimeBasedCounter( libs.constants.submissionQueueData.timeBasedCounters, counter )
        }, libs.constants.submissionQueueData.writeInterval
    )
};

var sendDataToSocket = function (io, obj, event, data, questionType) {
    try {
        if (!io) {
            logger.error('sendDataToSocket io server is invalid');
            return false;
        }
        let socketId = obj.socketId || obj.payload.socketId;
        
        if (!socketId) {
            logger.error('sendDataToSocket socketId is invalid');
            logger.debug('sendDataToSocket socketId is invalid', obj, "Data ", data, " questionType ", questionType);
            return false;
        }
        if (!event) {
            logger.error('sendDataToSocket event is invalid ' + event);
            return false;
        }
        // let socket = io.sockets.sockets.get(obj.socketId);

        if (event !== "ERROR") {
            // socket.emit(event, data);
            io.to(socketId ).emit(event, data);
            if(!obj.payload.onlyResultRequired) {
                saveAttemptInDb(obj, data, questionType);
            }
        } else {
            io.to(socketId).emit(event, data);
            if(!obj.payload.onlyResultRequired) {
                saveAttemptInDb(obj, { txtCodeAt : obj.txtCodeAt, progLang : obj.payload.language, errors : "Error while compiling code" },
                libs.constants.questionTypeNumeric.questionTypeCoding);
            }
        }
    }
    catch (err) {
        logger.debug("error occured sendDataToSocket", err);
        logger.error("error occured sendDataToSocket");
        return false;
    }
    return true;
};

var createAttemptInDb = function (userAnswerId, obj, data, questionType, socket) {
    let isCB = socket && typeof socket === 'function';
    userAttemptController.createUserAttempt(userAnswerId, obj, data, questionType, (err, resultUserAttempt) => {
        if (err) {
            logger.debug("createAttemptInDb user attempt not saved successfully-", err, "obj-", obj, "data-", data);
            logger.error("createAttemptInDb user attempt not saved successfully");
        } else {
            let { result, userAttemptObj } = resultUserAttempt
            if (obj.payload.questionSubmission) {
                if (questionType == libs.constants.questionTypeNumeric.questionTypeCoding || (
                    questionType == libs.constants.questionTypeNumeric.questionTypeWeb && 
                    obj.payload.language == libs.constants.langStringToLangCode["React-Jest"]
                )) {
                    setValuesInRedisAfterSubmission(obj.payload, questionType, (err) => {
                        if (isCB) {
                            socket(err, resultUserAttempt)
                        }
                    })
                } else
                    saveSubmittedQuestion(obj, questionType, {}, socket);
            } else if( isCB) {
                socket( null, resultUserAttempt )
            }
            if ( data.errors != '404' ) {
                let objForMetaData = {
                    quizId: obj.payload.quizId,
                    questionId: obj.payload.questionId,
                    userId: obj.payload.userId,
                    isError: data && data.errors,
                    testCaseComparisonResult: obj.testCaseComparisonResult,
                    questionType,
                }
                if ( obj.payload.language && ( questionType == libs.constants.questionTypeNumeric.questionTypeCoding || libs.constants.questionTypeNumeric.questionTypeWeb) ) {
                    objForMetaData.langCode = obj.payload.language;
                }
                updateQuizQuestionMetaData(objForMetaData);
            }

            if (!(  obj.payload.quizId )) {
                updateQuestionStatus(obj, userAnswerId, questionType, data, obj.payload ? obj.payload.socketId || obj.socketId : "");
                return;
            }

        }
    });

};

var saveAttemptInDb =  function (obj, data, questionType, socket) {
    let { payload } = obj;
    let isCallback =  socket && typeof socket === 'function';
    let { isInvalidAttempt, isIdeRequest } = payload
    if ( !( isInvalidAttempt || isIdeRequest ) ) {

        if (obj.payload.submissionId) {

            let redisObj = {
                'userProgram': obj.txtCodeAt,
                'userLanguage': obj.payload.language,
                'userCompilationError': data && data.errors,
                'submissionId' : obj.payload.submissionId,
                questionType :  libs.constants.questionTypeNumeric.questionTypeCoding,
                _id : mongoose.Types.ObjectId(),
                codeId: obj.payload.codeId,
            }
            if ( obj.payload.language == constants.langStringToLangCode['React-Jest'] ) {
                redisObj.html = obj.payload.html || '';
                redisObj.css = obj.payload.css || '';
                redisObj.js = obj.payload.js || '';
            }

            let updateObj = {}, userOutputCoding = [] ;
            if( data.errors && data.errors.trim()){
                redisObj.userCompilationError = data.errors.trim();
                updateObj = { $set : { 'attemptData.$.userCompilationError' : data.errors }}
            } else {
                let outputArray =  obj.outputArray || data.outputArray || []
                let testCase = data.testCase ||
                         ( obj.question && obj.question.questionTypeCoding && 
                            ( obj.question.questionTypeCoding.tc || obj.question.questionTypeCoding.testCase ) ? 
                            obj.question.questionTypeCoding.tc || obj.question.questionTypeCoding.testCase : [] );
                let countTestCase = Math.max (testCase.length, outputArray.length );
                for (let i = 0; i < countTestCase ; i += 1) {
                    userOutputCoding.push( outputArray[i] ? 
                        outputArray[i].userOutput || ( typeof outputArray[i] == 'string' ? outputArray[i].replace(/[\r]/g, '') : "" ) : "");
                }

                redisObj.testCasesPassed =  obj.testCaseComparisonResult && obj.testCaseComparisonResult.testCasePassed;
                redisObj.totalTestCase = obj.testCaseComparisonResult && obj.testCaseComparisonResult.totalTestCases;
                redisObj.totalScore = obj.testCaseComparisonResult && obj.testCaseComparisonResult.totalScore;
                redisObj.userOutputCoding = userOutputCoding;
                
            }

            let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${obj.payload.quizId}:${obj.payload.userId}:${obj.payload.questionId}`;
 
            services.RedisServices.redis2('rpush', userAttemptListByQuesRedisKey, JSON.stringify(redisObj))

            let ongoingQuizScoreRedisKey = redisKeys.ongoingQuiz + ":" + obj.payload.userId + ":" + obj.payload.quizId  + ":" + redisKeys.submittedQuesScores;
            services.RedisServices.redis("hget", ongoingQuizScoreRedisKey, obj.payload.questionId, (err, redisScore) => {
                let score = ( obj.testCaseComparisonResult && parseInt(obj.testCaseComparisonResult.totalScore) ) ? parseInt(obj.testCaseComparisonResult.totalScore) : 0;
                if ( redisScore == null || isNaN(redisScore) || score > parseInt(redisScore) ) {
                    services.RedisServices.redis("hset", ongoingQuizScoreRedisKey, obj.payload.questionId, score );
                    //services.RedisServices.redis("expire", ongoingQuizScoreRedisKey, constants.expiryTimeOfQuizReportInSec );
                }
            });

            let ongoingQuizRedisKey = constants.redisKeys.ongoingQuiz + ':' + obj.payload.userId + ":" + obj.payload.quizId;
            let attemptedQuestionRedisKey = `${constants.redisKeys.questionIdString}:${obj.payload.questionId}`;
            
            services.RedisServices.redis('hget', ongoingQuizRedisKey, attemptedQuestionRedisKey, (err, quesObj) => {
                try {
                    let submitQuesObj = JSON.parse(quesObj);
                    submitQuesObj.score = ( obj.testCaseComparisonResult && obj.testCaseComparisonResult.totalScore ) || 0;
                    services.RedisServices.redis('hset', ongoingQuizRedisKey, attemptedQuestionRedisKey, JSON.stringify(submitQuesObj));
                } catch (error) {
                    console.error("Error while saving score in redis ", error, " obj ", obj, " data ", data);
                    if( isCallback ) socket( error );
                    return;
                }
            })
            
            {
               
            }
                if ( data.errors != '404' ) {
                    let objForMetaData = {
                        quizId: obj.payload.quizId,
                        questionId: obj.payload.questionId,
                        userId: obj.payload.userId,
                        isError: data && data.errors,
                        testCaseComparisonResult: obj.testCaseComparisonResult || (obj.payload && obj.payload.testCaseComparisonResult),
                        questionType,
                    }
                    if ( obj.payload.language && questionType == libs.constants.questionTypeNumeric.questionTypeCoding ) {
                        objForMetaData.langCode = obj.payload.language;
                    }     
                    updateQuizQuestionMetaData(objForMetaData);
                }
            // })
        } else {
            shouldSaveQuestionAttmpt(obj, data, questionType, (error, shouldSave) => {
                if ( !shouldSave) {
                    if( isCallback ){
                        socket(null);
                    }
                    return ;
                };
                // coding
                if( ( (obj.hasOwnProperty('txtCodeAt') || obj.payload.language == constants.langStringToLangCode['React-Jest'] ) && 
                    obj.payload.quizId && 
                    ! obj.payload.shouldSave && 
                    ! obj.payload.questionSubmission) ){
                    obj.payload.shouldSave = true;
                    if( data.output )
                        delete data.output
                    let payloadToPush = { obj, data, type :  questionType };
                    let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${obj.payload.quizId}:${obj.payload.userId}:${obj.payload.questionId}`;
                    try {
                        let outputArray = obj.outputArray;
                        if ( ! outputArray ) {
                            outputArray = ( (data && data.outputArray) || [] ).map(ot => ot.userOutput);
                        }
                        let redisObj = {
                            'userProgram': obj.txtCodeAt,
                            'userLanguage': obj.payload.language,
                            'testCasesPassed': obj.testCaseComparisonResult && obj.testCaseComparisonResult.testCasePassed,
                            'totalTestCase': obj.testCaseComparisonResult && obj.testCaseComparisonResult.totalTestCases,
                            'totalScore': obj.testCaseComparisonResult && obj.testCaseComparisonResult.totalScore,
                            'time': obj.payload.time,
                            'userOutputCoding': outputArray,
                            'userCompilationError': data && data.errors,
                            'submissionId' : obj.payload.submissionId,
                            questionType,
                            _id : mongoose.Types.ObjectId(),
                            codeId: obj.payload.codeId,

                        }
                        if ( obj.payload.language == constants.langStringToLangCode['React-Jest'] ) {
                            redisObj.html = obj.payload.html || '';
                            redisObj.css = obj.payload.css || '';
                            redisObj.js = obj.payload.js || '';
                        }
                        services.RedisServices.redis2('rpush', userAttemptListByQuesRedisKey, JSON.stringify(redisObj))
                    } catch (e) {
                        console.log("ERror in redisObj = ", e);
                    }
                    
                    // pushInQuizSubmissionQueue(obj.payload.quizId);
                    return ;
                }

                let { questionId, userId, userAnswerId, courseId, quizId } = payload;
                let queryObj = userAnswerId ? { _id: userAnswerId } :
                    { 'questionId': questionId, 'userId': userId, [courseId ? "courseId" : "quizId"]: courseId || quizId };
                
                userAnswerController.getOneUserAnswer(
                    queryObj, { '_id': 1, 'finalSubmission': 1 }, {}, function (errorUserAnswer, userAnswer) {
                        if (errorUserAnswer) {
                            logger.debug("saveAttemptInDb error getting user answer ", queryObj, " error ", errorUserAnswer);
                            logger.error("saveAttemptInDb error getting user answer");
                            if( isCallback ){
                                socket({ error : "Error while getting useranswer while submission"});
                            }
                        }
                        else {
                            if (!userAnswer) {
                                if (obj.payload.questionSubmission)
                                    queryObj.finalSubmission = true;
                                services.UserAnswerService.addUserAnswer(queryObj, function (errorCreate, resultCreate) {
                                    if (errorCreate) {
                                        logger.debug("saveAttemptInDb error in creating useranswer", errorCreate);
                                        logger.error("saveAttemptInDb error in creating useranswer");
                                        if( isCallback ){
                                            socket({ error : "Error while getting useranswer while submission"});
                                        }
                                    }
                                    createAttemptInDb(resultCreate._id, obj, data, questionType, socket);
                                });
                            } else {
                                if (obj.payload.questionSubmission && (!userAnswer.finalSubmission)) {
                                    services.UserAnswerService.updateUserAnswer({ _id: userAnswer._id }, { 'autoPopulate': false },
                                        { $set: { 'finalSubmission': true } }, (err, result) => {
                                            if (err) {
                                                logger.debug("saveAttemptInDb error updating useranswer finalSubmission field--", err);
                                                logger.error("saveAttemptInDb error updating useranswer finalSubmission field");
                                                if( socket && typeof socket === 'function'){
                                                    socket({ error : "Error while updating useranswer while submission"});
                                                }
                                            }
                                        })
                                } else {
                                    let testCaseComparisonResult = obj.testCaseComparisonResult || ( obj.payload && obj.payload.testCaseComparisonResult );
                                    let score = ( testCaseComparisonResult && testCaseComparisonResult.totalScore ) || 0;
                                    services.UserAnswerService.updateUserAnswer({ _id: userAnswer._id }, { 'autoPopulate': false },
                                        { $max: { score } }, (err, result) => {
                                            if (err) {
                                                logger.debug("saveAttemptInDb error updating useranswer finalSubmission field--", err);
                                                logger.error("saveAttemptInDb error updating useranswer finalSubmission field");
                                                if( socket && typeof socket === 'function'){
                                                    socket({ error : "Error while updating useranswer finalSubmission"});
                                                }
                                            }
                                        })
                                }
                                createAttemptInDb(userAnswer._id, obj, data, questionType, socket);
                            }
                        }
                    });
            })
        }
    }
};

var sendPostRequest = function (payload, urlRoute, headers, callback) {
    payload.filesMd5 = {};
    async.each( ( payload && payload.files) || [], (fileLoc, next) => {
        let fileName = process.platform === "win32" ? 
            fileLoc.substring( fileLoc.lastIndexOf('\\')+1) : 
            fileLoc.substring( fileLoc.lastIndexOf('/')+1);

        //console.log("File name = " + fileName);
        //console.log("File loc = " + fileLoc);

        services.RedisServices.redis("hget", libs.constants.redisKeys.filesMd5Hash, fileLoc, (err, md5Hash) => {
            if (err) {
                console.log("Error in getting md5 for file = " + fileLoc + " and error is " + err);
                next();
                return ;
            }
            try{
            if (! md5Hash) {
                md5Hash = md5File.sync(fileLoc);
                if (! md5Hash) {
                    next();
                    return ;
                }
                services.RedisServices.redis("hset", libs.constants.redisKeys.filesMd5Hash, fileLoc, md5Hash);                    
            }
            payload.filesMd5[fileLoc] = md5Hash;
            next();
        } catch( e ){
            next( e );
        }
        })
    }, (err) => {
        // console.log("Files md5 = ", payload.filesMd5);
        if( err ){
            console.error("Error while reading file ", err )
            return callback(err);
        }
        if ( payload.url ) {
            payload.url += urlRoute ? urlRoute : "/compile";
        }

        sendPostRequestToMasterCompileServer(payload, headers, (errorReq, response, body) => {
            if (errorReq) {
                console.log("errorReq = ", errorReq);
            }
            callback(errorReq, response, body);
        });
    })
};

var updateQuestionStatus = function (updateObj, userAnswerId, questionType, data, socketId) {
    if (questionType === libs.constants.questionTypeNumeric.questionTypeMCQ) {
        updateMCQStatus(updateObj.payload.isCompleted, userAnswerId, updateObj.payload.inputMCQ, data.result, socketId);
        return;
    }
    if (updateObj.payload.isCustomInput)
        return;
    if ( ( updateObj.payload.isCompleted === 'false' || ( !updateObj.payload.isCompleted ) ) && !data.errors &&
        ( ! updateObj.testCaseComparisonResult ||
            updateObj.testCaseComparisonResult.testCasePassed === updateObj.testCaseComparisonResult.totalTestCases ||
            questionType === libs.constants.questionTypeNumeric.questionTypeSubjective ) ) {

        userAnswerController.updateUserAnswer({'_id': userAnswerId}, {}, {$set: {'completed': true}}, (errUpdate, resultUpdate) => {
            if (errUpdate) {
                logger.error("error updating user answer completion status----", errUpdate);
                return;
            }
            if (socketId)
                io.to(socketId).emit('completion_status', {'isCompleted': true});
        });
    }
};

var updateMCQStatus = function (isCompleted, userAnswerId, input, correctAnswer, socketId) {
    if (isCompleted === 'false' || !isCompleted) {
        let valueMCQ = input.replace("chkOpt", "");
        let index = parseInt(valueMCQ, 10) - 1;
        if (correctAnswer.indexOf(index) !== -1) {
            userAnswerController.updateUserAnswer({'_id': userAnswerId}, {}, {$set: {'completed': true}}, (errUpdate, resultUpdate) => {
                if (errUpdate) {
                    logger.error("error updating user answer completion status----", errUpdate);
                    return;
                }
                if (socketId)
                    io.to(socketId).emit('completion_status', {'isCompleted': true});
                return true;
            });
        }
    }
    return false;
};

const handleCompileDefaultQuestionResponse = function (dataObj) {
    let key = libs.constants.redisKeys.codePayload + ":" + dataObj.codeId;
    services.RedisServices.redis("get", key , (err, obj) => {
        if ( err ) {
            console.log("Error in handleCompileDefaultQuestionResponse, err = ", err);
            return ;
        }
        if ( obj ) {
            try {
                obj = JSON.parse( obj );
            } catch (error) {
                console.log("Exception in JSON parse handleCompileDefaultQuestionResponse, Error = ", error, " obj ", obj);
                
            }
        }
        services.RedisServices.redis("del", key);
        if ( dataObj.error ) {
            // Error from master compile server
            console.log("Obj = ", obj);
            console.log("handleCompileDefaultQuestionResponse Err from compile Master = ", dataObj.error);
            sendDataToSocket(io, obj, 'ERROR', dataObj.error);
            libs.mailLib.compileServerMasterErrorMail(dataObj);
            return ;
        }
        let receivedData = {};
        let question = obj.question;
        if (!parseDataAfterCompilationNew(dataObj.payload, receivedData)) {
            logger.debug("compileQuestionForDefaultResult received data not valid while adding result in db", receivedData.error);
            logger.error("compileQuestionForDefaultResult received data not valid while adding result in db");
            sendDataToSocket(io, obj, 'ERROR', receivedData.error, question.type);
            return;
        }
        let outputObj = receivedData.data;
        let segmentId = question._id;
        let defaultObj = {'output': outputObj.output, 'errors': outputObj.errors};
        key = libs.constants.redisKeys.questionIdString + ':' + segmentId;
        services.RedisServices.redis("hgetall", key, (errField, resultField) => {
            if (errField) {
                logger.debug("compileQuestionForDefaultResult error in getting field of hash ", key, "error--", errField);
                logger.error("compileQuestionForDefaultResult error in getting field of hash");
                return;
            } else if (!resultField) {
                logger.error("entry not there for this segment id-", segmentId);
                return;
            }
            try {
                let codeProgLangObj = JSON.parse(resultField[libs.constants.redisKeys.codeproglangFieldString]);
                let compileCounter = parseInt(resultField[libs.constants.redisKeys.compileCounterFieldString], 10);
                codeProgLangObj[compileCounter].defaultCompileResult = defaultObj;
                if (( compileCounter + 1 ) ==
                    resultField[libs.constants.redisKeys.totalCompileCounterFieldString]) {
                    services.RedisServices.redis("del", key);
                    let updateObj = {$set: {'questionTypeCoding.codeproglang': codeProgLangObj}};
                    questionController.updateOneQuestion({'_id': obj.payload.questionId}, updateObj, {}, (err, result) => {
                        if (err) {
                            logger.debug("compileQuestionForDefaultResult error in updating default compilation result of question ", obj.payload);
                            logger.error("compileQuestionForDefaultResult error in updating default compilation result of question");
                            return;
                        }
                    });
                } else {
                    services.RedisServices.redis("hmset", key, libs.constants.redisKeys.compileCounterFieldString, compileCounter + 1,
                        libs.constants.redisKeys.codeproglangFieldString, JSON.stringify(codeProgLangObj));
                }
            } catch (err) {
                libs.util.sendEmail("<EMAIL>", "Compile Controller Error " + process.env.host,
                "mesaage-\n" + err?.message ?? err + '\nstackTrace-\n' + err?.stack ?? err + '\n' +
                "resultField: "+ JSON.stringify(resultField) 
                , process.env.DEFAULT_EMAIL,(error, info) => {
                    if (error) {
                        console.debug("error in sending mail while sending uncaught exception---", error);
                        console.error("error in sending mail while sending uncaught exception");
                    } else {
                        console.debug(`Message sent: ${info.response}`);
                    }
                });
            }
        })
    })
}

var addSegmentToCompilationQueue = function (segment, questionId, questionObj) {
    let codeComponentsCounter = 0;
    let tempObj = {'codeproglang': segment, 'compileCounter': 0};
    for (let i = 0; i < segment.length; i += 1) {
        let languageObj = segment[i];
        if( languageObj.language != libs.constants.languageCodes.SQL || languageObj.language != libs.constants.languageCodes.MySql || languageObj.language != libs.constants.languageCodes.Oracle ) {
            codeComponentsCounter += 1;
            var payload = {
                "language": languageObj.language,
                "questionId": questionId,
                'isAddingInDb': true,
                'role': libs.constants.roleNumberFromString.admin
            };
            payload.code = languageObj.codeComponents.body;
            payload.questionObj = questionObj;
            compileQuestion(payload);
        }
    }
    if( codeComponentsCounter ) {
        tempObj.totalCompileCounter = codeComponentsCounter;
        let key = libs.constants.redisKeys.questionIdString + ':' + questionId;
        services.RedisServices.redis("hmset", key,
            libs.constants.redisKeys.codeproglangFieldString, JSON.stringify(segment),
            libs.constants.redisKeys.compileCounterFieldString, 0,
            libs.constants.redisKeys.totalCompileCounterFieldString, codeComponentsCounter);
        return codeComponentsCounter;
    }
    return -1;
};

var parseDataAfterCompilation = function (error, response, body, outputObj) {
    if (error || response.statusCode != 200) {
        logger.debug("parseDataAfterCompilation error in request--parseDataAfterCompilation", error);
        logger.error("parseDataAfterCompilation error in request--parseDataAfterCompilation");
        outputObj.error = " error in request--" + error;
        return false;
    }
    try {
        outputObj.data = JSON.parse(body);
    }
    catch (e) {
        logger.debug("parseDataAfterCompilation JSON.parse was unsuccessful ", body, "error---", e);
        logger.error("parseDataAfterCompilation JSON.parse was unsuccessful");
        outputObj.error = "JSON.parse was unsuccessful , error---" + e
        return false;
    }

    if (!isValidResponseFromServer(outputObj.data)) {
        logger.debug("parseDataAfterCompilation response from compiler api was invalid", body);
        logger.error("parseDataAfterCompilation response from compiler api was invalid");
        outputObj.error = "response from compiler api was invalid--";
        return false;
    }
    return true;
};

var parseDataAfterCompilationNew = function (body, outputObj) {
    try {
        outputObj.data = JSON.parse(body);
    }
    catch (e) {
        logger.debug("parseDataAfterCompilation JSON.parse was unsuccessful ", body, "error---", e);
        logger.error("parseDataAfterCompilation JSON.parse was unsuccessful");
        outputObj.error = "JSON.parse was unsuccessful , error---" + e
        return false;
    }

    if (!isValidResponseFromServer(outputObj.data)) {
        logger.debug("parseDataAfterCompilation response from compiler api was invalid", body);
        logger.error("parseDataAfterCompilation response from compiler api was invalid");
        outputObj.error = "response from compiler api was invalid--";
        return false;
    }
    return true;
};

var saveAttemptOfCacheResult = function (obj, socket) {
    // questionController.getQuestionFromId(obj.payload.questionId, (errorQues, question) => {
    getCodingQuestionFromDb(obj, (errorQues, question) => {
        if (errorQues) {
            logger.debug("error in request saveAttemptOfCacheResult", errorQues);
            logger.error("error in request saveAttemptOfCacheResult");
            sendDataToSocket(io, obj, 'ERROR', "question id is not valid", question.type);
            return;
        }
        else if (!question) {
            logger.error("saveAttemptOfCacheResult question not present" + obj.payload.questionId);
            sendDataToSocket(io, obj, 'ERROR', "question not present", question.type);
            return;
        }
        obj.payload.isCachedResult = true;
        if (Object.prototype.hasOwnProperty.call(obj.result, 'output') && obj.result.output) {
            try {
                obj.result.output = libs.util.decrypt(obj.result.output);
            } catch (exception) {
                logger.debug("saveAttemptOfCacheResult Error in decrypting default result ", exception.message, "stack--", exception.stack, " output ", obj.result.output);
                logger.error("saveAttemptOfCacheResult Error in decrypting default result ");
                return;
            }
        }
        question = { questionTypeCoding : { tc : question.questionTypeCoding.testCase, 
            multipleTestCases : question.questionTypeCoding.multipleTestCases  }, type : question.type } ; //tc ---> testCase
        setOutputDataAfterCompilation(obj, obj.result, (err, question ) => {
            if (err) {
                sendDataToSocket(io, obj, 'ERROR', "Error in fething question data");
                return;
            };
            saveAttemptInDb(obj, obj.result, libs.constants.questionTypeNumeric.questionTypeCoding, socket);
        })
    });
};

var submitQuestion = function (payload, quizId, qId, sessionObj, callback, response = null) {
    try {
        let questionType;
        let { redisKeys } = libs.constants;

        if (Object.prototype.hasOwnProperty.call(payload, 'txtCodeAt')) {
            // payload.txtCodeAt = payload.txtCodeAt.trim();
            // payload.txtCodeAt = payload.txtCodeAt.replace(/^\s*$[\n\r]{1,}/gm, '');
            payload.code = payload.txtCodeAt;
            payload.language = payload.progLang;
            delete payload.progLang;
            delete payload.txtError;
        }
        if (Object.prototype.hasOwnProperty.call(payload, 'snapshots')) {
            payload.snapshots = [] || JSON.parse(payload.snapshots);
        }
        if (Object.prototype.hasOwnProperty.call(payload, 'isStepwise') &&
            (payload.isStepwise === 'true' || (typeof payload.isStepwise === 'boolean' && payload.isStepwise))) {
            let stepwiseInput = JSON.parse(payload.stepwiseUserInput);
            payload = {};
            payload.stepwiseUserInput = stepwiseInput;
            payload.isStepwise = true;
            questionType = libs.constants.questionTypeNumeric.questionTypeStepwise;
        } else {
            payload.isStepwise = false;
        }
        payload.quizId = quizId;
        payload.questionId = qId;
        payload.userId = sessionObj.userId.toString();
        payload.role = sessionObj.role;
        payload.questionSubmission = true;
        payload.time = Date.now();
        if (Object.prototype.hasOwnProperty.call(payload, 'inputMCQ')) {
            questionType = libs.constants.questionTypeNumeric.questionTypeMCQ;
        } else if (Object.prototype.hasOwnProperty.call(payload, 'txtSub')) {
            questionType = libs.constants.questionTypeNumeric.questionTypeSubjective;
        }  else if (Object.prototype.hasOwnProperty.call(payload, 'html')) {
            questionType = libs.constants.questionTypeNumeric.questionTypeWeb;
        } else if (!payload.isStepwise) {
            questionType = libs.constants.questionTypeNumeric.questionTypeCoding;
        }

        if ( questionType == libs.constants.questionTypeNumeric.questionTypeWeb ) {
            if ( payload.webTestCaseStatus && payload.scores && payload.webTestCaseStatus.length == payload.scores.length ) {
                payload.testCaseComparisonResult = {
                    'totalTestCases': payload.webTestCaseStatus.length, 
                    'testCasePassed': 0,
                    'totalScore': 0,
                    'webTestCaseStatus': payload.webTestCaseStatus, 
                };
                payload.webTestCaseStatus.map( ( isCorrect, index ) => {
                    if ( Boolean( isCorrect != 'undefined' && isCorrect != 'false' && isCorrect ) ) {
                        payload.testCaseComparisonResult.testCasePassed += 1;
                        payload.testCaseComparisonResult.totalScore += parseInt(payload.scores[index]) || 0;
                    }   
                })
                delete payload.webTestCaseStatus;
                delete payload.scores;
            }
        }

            setValuesInRedisAfterSubmission(payload, questionType, (errSet, redisSet) => {
                if (errSet) {
                    callback(errSet);
                    return;
                }
             //   console.log("Submission", payload)
                callback(null);
                if (Object.prototype.hasOwnProperty.call(payload, 'inputMCQ')) {
                    payload.type = libs.constants.questionTypeNumeric.questionTypeMCQ
                    pushInQuizSubmissionQueue(quizId);
                    // getMCQResult(payload, { 'id': true });
                } else if (Object.prototype.hasOwnProperty.call(payload, 'txtSub')) {
                    payload.type = libs.constants.questionTypeNumeric.questionTypeSubjective
                    pushInQuizSubmissionQueue(quizId);
                } else if (Object.prototype.hasOwnProperty.call(payload, 'html')) {
                    payload.type = libs.constants.questionTypeNumeric.questionTypeWeb;
                    if( payload.language == libs.constants.langStringToLangCode["React-Jest"] ){
                        compileQuestion(payload, { 'id': true });
                    } else {
                        pushInQuizSubmissionQueue(quizId);
                    }
                } else if(payload.isStepwise){
                    payload.type = libs.constants.questionTypeNumeric.questionTypeStepwise
                    pushInQuizSubmissionQueue(quizId);
                } else if (!payload.isStepwise) {
                    compileQuestion(payload, { 'id': true });
                }
            });
    } catch (error) {
        logger.debug("submitQuestion error in quiz question submission ", error, " Payload = ", payload);
        logger.error("submitQuestion error in quiz question submission");
        callback(error);
    }
};

var compileIdeRequest = function (obj) {
	if ( obj.payload.stdin )    obj.payload.testInputArr = [obj.payload.stdin || '\n'];
            obj.payload.stdin += libs.constants.endOfInputString;
    
    if( obj.payload.language ==  libs.constants.languageCodes.SQL || obj.payload.language ==  libs.constants.languageCodes.MySql || obj.payload.language ==  libs.constants.languageCodes.Oracle ){
        obj.payload.code += libs.constants.endOfInputString;
    }
    services.RedisServices.redis("set", libs.constants.redisKeys.codePayload + ":" + obj.payload.codeId, JSON.stringify( obj ), ( err ) => {
        sendPostRequest(obj.payload, options.url, options.headers, function (error, response, body) {
            if ( error ) {
                console.log("Error in sending code to master compile server, compileIdeRequest, Error = ", error);
                return ;
            }
            console.log("Code send to master compile server compileIdeRequest");
        });
    })
};

const handleCompileIdeResponse = function (dataObj) {
    let key = libs.constants.redisKeys.codePayload + ":" + dataObj.codeId;
    services.RedisServices.redis("get", key, (err, obj) => {
        if ( err ) {
            console.log("Err in handleCompileIdeResponse redis = ", err);
            return ;
        }
        services.RedisServices.redis("del", key );
        
        if ( obj ) {
            try {
                obj = JSON.parse( obj );
            } catch (error) {
                console.log("JSON parse exception in handleCompileIdeResponse, Error = ", error, "Obj = ", obj);
            }
        }

        if ( dataObj.error ) {
            console.log("Obj = ", obj);
            console.log("handleCompileIdeResponse Err from compile Master = ", dataObj.error);
            sendDataToSocket(io, obj, 'ERROR', dataObj.error);
            libs.mailLib.compileServerMasterErrorMail(dataObj);
            return ;
        }
        
        let receivedData = {};
        if (!parseDataAfterCompilationNew(dataObj.payload, receivedData)) {
            sendDataToSocket(io, obj, 'ERROR', receivedData.error);
            return;
        }
        let outputObj = receivedData.data;
        if (outputObj.errors && (  outputObj.errors.indexOf(libs.constants.coreDumpedString) !== -1 ||
            outputObj.errors.indexOf(libs.constants.segmentationFaultString) !== -1 )) {
            outputObj.errors = libs.constants.segmentationFaultString
        }
        delete outputObj.code;
        outputObj.langid = obj.payload.language;
        if (outputObj.langid == libs.constants.langStringToLangCode["React-Jest"]) {
            try {
                outputObj.output = JSON.parse(outputObj.output).output
            } catch (e) {
                console.log("Error while parsing jest response", e , outputObj.output );
            }
        }
        sendDataToSocket(io, obj, 'compile', outputObj);
    })
}

const handleCompileQuestionResponse = function(dataObj) {
    let key = libs.constants.redisKeys.codePayload + ":" + dataObj.codeId;
    services.RedisServices.redis("get", key, (err, obj) => {
        if ( err ) {
            console.log("Err in handleCompileQuestionResponse redis = ", err);
            return ;
        }
        if ( obj ) {
            try {
                obj = JSON.parse( obj )
            } catch (error) {
                console.log("JSON parse exception in handleCompileQuestionResponse, Error = ", error, "Obj = ", obj);
		return ;
            }
        } else {
            console.error("Obj null, please check redis", dataObj);
            return;
        }
        services.RedisServices.redis("del", key )
        if ( dataObj.error ) {
            console.log("Obj = ", obj);
            console.log("handleCompileQuestionResponse Err from compile Master = ", dataObj.error);
            sendDataToSocket(io, obj, 'ERROR', dataObj.error);
            libs.mailLib.compileServerMasterErrorMail(dataObj);
            return ;
        }
        let receivedData = {};
        if (! parseDataAfterCompilationNew(dataObj.payload, receivedData)) {
            logger.debug("compilePayloadObj received data not valid while adding result in db--", receivedData.error);
            logger.error("compilePayloadObj received data not valid while adding result in db");
            console.log("compilePayloadObj received data not valid while adding result in db--", receivedData.error);
            if ( ! obj.payload.reevaluate )
                sendDataToSocket(io, obj, 'ERROR', receivedData.error, ( obj.question && obj.question.type ) || '4' );
            return;
        } 
        let outputObj = receivedData.data;
        delete obj.payload.isDefaultCode;
        setOutputDataAfterCompilation(obj, outputObj, ( err, question ) => {
            if( err ){
                sendDataToSocket(io, obj, 'ERROR', "Error in fething question data");
                return;
            };
            if (obj.payload.reevaluate) {
                reevaluation.handleReevaluateAttempt(obj, outputObj);
            }
            else {
                sendDataToSocket(io, obj, 'compile', outputObj, question.type);
            }
    })
    })
}

var getQuestionForOutputManipulation = async function (payload = {}, cb) {
    const { questionId } = payload;
    if (!questionId) {
        cb({ error: "Question Id not present" });
        return;
    }
    
    let hash = libs.constants.redisKeys.questionString + ":" + questionId;
    let redisQuestionKeys = libs.constants.redisQuestionKeys;
    const { type, questionTypeWeb, questionTypeCoding, _id } = redisQuestionKeys;
    let questionMGETArray = [
        type.key,
        questionTypeCoding.key,
        questionTypeWeb.key,
    ]
    let { processMapName, controlKey } = libs.constants.cachingKeys.question
    if( !global[ controlKey] &&
         Object.prototype.hasOwnProperty.call( global[ processMapName ], questionId ) ){
        let questionObj = ( global[processMapName][ questionId ] );
        questionObj.lastUsed = Date.now()
        questionObj = _.cloneDeep( questionObj)
        cb( null, questionObj );
        return;
    }
    services.RedisServices.redis('hmget', hash, questionMGETArray,async (err, result) => {
        if (err) {
            console.log("Error is ", err);
            cb(err);
            return;
        }
        try {
            if (( result && result.length && result[0] && result[1])) {
                
                let questionObj = {
                    [type.key]: result[0],
                    [questionTypeCoding.key]: JSON.parse(result[1] || "{}"),
                    [questionTypeWeb.key]: JSON.parse(result[2] || "{}"),
                };
                cb(null, questionObj);
            } else {
                console.log("Question not present in redis", questionId, hash );
                userAnswerController.getQuestionFromDb(questionId, ( errQues, question )=>{
                    if( errQues ){
                        console.log(" Error in getQuestionFromDb", errQues);
                        cb({ 'error': "Error while getting question from db" });
                    }
                    return cb( null, question );
                });
                // return cb({ 'error': "Question data in present" })
            }
        } catch (e) {
            console.log(" Error in getQuestionForOutputManipulation", e);
            cb({ 'error': "Error while getting question" });
            return;
        }
    })
}

var arrangeFileNumber = function (obj, outputObj) {
    if (!outputObj.errors)
        return;
    if (!( obj.payload.codeObj  ))
        return;
    let linesArray = outputObj.errors.split('\n');
    let headLines = obj.payload.codeObj.h;
    let userCodeLength = outputObj.code.split("\n").length - obj.payload.codeObj.h - obj.payload.codeObj.t;
    let newErrorString = "";
    for (let i = 0; i < linesArray.length; i += 1) {
        let line = linesArray[i];
        let errorIndex = line.indexOf(" error:");
        if (errorIndex === -1)
            errorIndex = line.indexOf(" warning:");
        if (errorIndex === -1)
            errorIndex = line.indexOf(" note:");

        if (errorIndex != -1) {
            let errorStringParts = line.split(':');
            let lineNumber = parseInt(errorStringParts[1], 10);
            if (errorStringParts[1] && lineNumber > headLines) {
                let newLineNumber = lineNumber - headLines;
                if (newLineNumber < 0) {
                    newErrorString = "";
                    break;
                } else if (newLineNumber <= userCodeLength) {
                    errorStringParts[1] = newLineNumber;
                    line = errorStringParts.join(':');
                }
                else {
                    newErrorString = "";
                    break;
                }
            }
        }
        line += "\n";
        newErrorString += line;
    }
    if (newErrorString)
        outputObj.errors = newErrorString;
};

var saveSubmittedQuestion = async function (obj, questionType, options = {}, callback) {
    const { payload } = obj;
    const { scoreOnly } = options;
    let txtCodeAt = payload.txtCodeAt || obj.txtCodeAt;

    let key = libs.constants.redisKeys.ongoingQuiz + ':' + obj.payload.userId + ":" + obj.payload.quizId
    var questionKey = libs.constants.redisKeys.questionIdString + ":" + obj.payload.questionId;

    let quizSubmittedQuestionObj = await services.RedisServices.redis('hget', key, questionKey );
    if( !quizSubmittedQuestionObj ){
        console.log("submitted question object not present in redis", obj )
        throw new Error("submitted question object not present in redis")
    }

    quizSubmittedQuestionObj = JSON.parse( quizSubmittedQuestionObj );
    
    let testCaseComparisonResult = obj.testCaseComparisonResult || ( obj.payload && obj.payload.testCaseComparisonResult );
    let totalScore = ( testCaseComparisonResult && testCaseComparisonResult.totalScore ) || 0;
    if (!scoreOnly) {
        quizSubmittedQuestionObj.hasSubmitted = true;
        quizSubmittedQuestionObj.userProgram = txtCodeAt;
        quizSubmittedQuestionObj.userLanguage = obj.payload.language;
        await services.RedisServices.redis('hset', key, questionKey, JSON.stringify( quizSubmittedQuestionObj ) );
    } else {
        if (quizSubmittedQuestionObj.score && quizSubmittedQuestionObj.score < totalScore) {
            quizSubmittedQuestionObj.score = totalScore
            await services.RedisServices.redis('hset', key, questionKey, JSON.stringify( quizSubmittedQuestionObj ) );
        }

    }
   
};

const updateQuizQuestionMetaData = async function (obj) {
    try {
        const {questionType, quizId, userId, questionId, testCaseComparisonResult, langCode} = obj;
        if ( ! questionType )                                       throw new Error("QuestionType is null");
        if ( ! quizId )                                             throw new Error("QuizId is null");
        if ( ! userId )                                             throw new Error("UserId is null");
        if ( ! questionId )                                         throw new Error("QuestionId is null");
	   let {testCasePassed, totalScore, totalTestCases} = testCaseComparisonResult || {};
        if ( questionType == libs.constants.questionTypeNumeric.questionTypeCoding ||
            questionType == libs.constants.questionTypeNumeric.questionTypeStepwise || 
            questionType == libs.constants.questionTypeNumeric.questionTypeMCQ ) {
                if ( ! testCaseComparisonResult && ! obj.isError )          throw new Error("TestCaseComparisonResult is null");
//                let {testCasePassed, totalScore, totalTestCases} = testCaseComparisonResult || {};
        }

        let findObj = {quizId, questionId};
        let updateObj = {
            '$inc': {totalAttempts: 1}, 
            'type': questionType,
        };
        let arrayFilters = null;
        switch (questionType) {
            case libs.constants.questionTypeNumeric.questionTypeMCQ:
                updateObj['$inc'][totalScore ? 'successCount' : 'wrongCount'] = 1;
                if ( totalScore ) updateObj['$addToSet'] = { successAttemptByUserIds: typeof(userId) == "string" ? mongoose.Types.ObjectId(userId) : userId };
                break;

            case libs.constants.questionTypeNumeric.questionTypeCoding:
            case libs.constants.questionTypeNumeric.questionTypeStepwise:
                let keyToIncr = obj.isError ? 'compileError' : ( testCasePassed == totalTestCases ? 'successCount' : 'wrongCount' );
                updateObj['$inc'][keyToIncr] = 1;
                if ( testCasePassed == totalTestCases )    updateObj['$addToSet'] = { successAttemptByUserIds: typeof(userId) == "string" ? mongoose.Types.ObjectId(userId) : userId };
                // if ( langCode ) {
                //     updateObj['$inc']['langTotalAttempts.$[elem].totalAttempts'] = 1;
                //     arrayFilters = [{ 'elem.code': langCode}];
                // }
                break;
            case libs.constants.questionTypeNumeric.questionTypeSubjective:
            case libs.constants.questionTypeNumeric.questionTypeWeb:
                updateObj['$inc']['successCount'] = 1;
                updateObj['$addToSet'] = { successAttemptByUserIds: typeof(userId) == "string" ? mongoose.Types.ObjectId(userId) : userId };
                break;
            default:
                console.log("Question Type is invalid");
                return;
        }
        let optionsObj = {upsert: true};
        //if ( arrayFilters )  optionsObj['arrayFilters'] = arrayFilters
        let result = await services.QuizQuestionMetaDataService.updateOneQuizQuestionMetaData(findObj, updateObj, optionsObj);
        if ( ! result )     throw new Error("Quiz Meta data obj is null");

        if ( langCode ) {
            let curLangTotalAttemptObj = ( result.langTotalAttempts || [] ).filter(langTotalAttemptObj => langTotalAttemptObj.code == langCode);
            updateObj = {}
            
            if ( curLangTotalAttemptObj && curLangTotalAttemptObj.length ) {
                findObj['langTotalAttempts.code'] = langCode;
                updateObj['$inc'] = {'langTotalAttempts.$.totalAttempts': 1};
            }   
            else    updateObj['$addToSet'] = {langTotalAttempts: {code: langCode, totalAttempts: 1}};
    
            result = await services.QuizQuestionMetaDataService.updateOneQuizQuestionMetaData(findObj, updateObj, optionsObj);    
        }
    
    } 
    catch (e) {
        console.log("Error updateQuizQuestionMetaData = ", e, obj);
        return {error: e.message};
    }
}

var sendNotificationToAdmin = function (obj) {
    if (obj.payload.quizId) {
        services.RedisServices.redis("hget", libs.constants.redisKeys.ongoingQuiz + ':' + obj.payload.userId + ":" + obj.payload.quizId,
            libs.constants.redisKeys.sessionId, (errSess, sessionId) => {
                if (errSess) {
                    logger.debug("sendNotificationToAdmin Error while Getting session id ", errSess)
                    logger.error("sendNotificationToAdmin Error while Getting session id ")
                    return;
                }
                sessionController.getSessionBySessionID(sessionId, (error, session) => {
                    if (error) {
                        logger.debug("sendNotificationToAdmin Session is invalid for session id ", obj.payload.sid);
                        logger.error("sendNotificationToAdmin Session is invalid for session id");
                        return;
                    } else if (!session) {
                        session = {'email': obj.payload.userId}
                    }

                    let quizNameFromIdKey = libs.constants.redisKeys.quizNameFromId + ':' + obj.payload.quizId;
                    services.RedisServices.redis("get", quizNameFromIdKey, (err, quizName) => {
                        if (err || (!quizName)) {
                            logger.debug("sendNotificationToAdmin Quiz Name not found for key ", quizNameFromIdKey, " err ", err);
                            logger.error("sendNotificationToAdmin Quiz Name not found for key " + quizNameFromIdKey);
                            return;
                        }
                        let reportObj = {
                            'msg': `In Test ${quizName } with user ${ session.email } for question Id ${obj.payload.questionId}, Payload is not valid, may be browser issue `,
                            'level': libs.constants.badActivitiesTypeNumber.cheating,
                        };
                        libs.util.sendReportToAdmin(quizName, reportObj);
                    })
                })
            });
    }
};

const getSubmissionScore = function (payload, cb) {
    try {
        if ( ! payload )    throw new Error("Payload is null");
        switch (payload.questionType) {
            case constants.questionTypeNumeric.questionTypeMCQ:
                payload.onlyResultRequired = true;
                getMCQResult(payload, null, (err, obj) => {
                    if (err)    throw new Error(err.message);
                    let dataObj = {
                            score: ( obj && obj.testCaseComparisonResult && obj.testCaseComparisonResult.totalScore ) || 0,
                            isMCQCorrect: obj && obj.isMCQCorrect,
                        }
                    delete payload.onlyResultRequired;
                    if (cb)     cb(null, dataObj);
                });
                break;
            case constants.questionTypeNumeric.questionTypeStepwise:
                payload.onlyResultRequired = true;
                getMultipleAnswerResult({ payload }, (err, obj) => {
                    if (err)            throw new Error(err.message);
                    if ( ! obj )        throw new Error("getMultipleAnswerResult, Obj is null");
                    let {testCaseComparisonResult} = obj;
                    if ( ! testCaseComparisonResult )   throw new Error("testCaseComparisonResult is null in getMultipleAnswerResult");
                    
                    let dataObj = {
                            score: testCaseComparisonResult.totalScore || 0,
                            testCasesPassed: testCaseComparisonResult.testCasePassed || 0,
                            totalTestCase: testCaseComparisonResult.totalTestCases || 0,
                        }
                    delete payload.onlyResultRequired;
                    if (cb)     cb(null, dataObj);
                });
                break;
            case constants.questionTypeNumeric.questionTypeWeb:
                let {testCaseComparisonResult} = payload;
                let dataObj = {};
                if (testCaseComparisonResult) {
                    dataObj = {
                        score: testCaseComparisonResult.totalScore || 0,
                        testCasesPassed: testCaseComparisonResult.testCasePassed || 0,
                        totalTestCase: testCaseComparisonResult.totalTestCases || 0,
                    }
                }
                if (cb)     cb(null, dataObj);
                break;
            default:
                if (cb)     cb(null);
                break;
        }
        
    } catch (e) {
        console.log("getSubmssionScore Error = ", e, payload);
        if (cb)     cb(e);
        return null;
    }
}

const saveSubmissionInRedis = async function (payload, cb) {
    try {
        if ( ! payload )    throw new Error("Payload is null");
        getSubmissionScore(payload, async (err, dataObj) => {
            if (err)            throw new Error(err.message);
            //if ( ! dataObj )    throw new Error("dataObj is null");
            let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${payload.quizId}:${payload.userId}:${payload.questionId}`;
            let obj = {
                time: payload.time || Date.now(),
            }
            let canPushToRedis = true;
            switch (payload.questionType) {
                case libs.constants.questionTypeNumeric.questionTypeMCQ:
                    obj.isMCQCorrect = dataObj.isMCQCorrect;
                    obj.inputMCQ = payload.inputMCQ;
                    break;
                case constants.questionTypeNumeric.questionTypeStepwise:
                    obj.stepwiseUserInput = payload.stepwiseUserInput || [];
                    obj = {...obj, ...dataObj};
                    break;
                case constants.questionTypeNumeric.questionTypeSubjective:
                    obj.userProgram = payload.txtSub;
                    if ( payload.files && payload.files[0] ) {
                        obj.files = payload.files.map((value) => value.path);
                    }
                    break;
                case constants.questionTypeNumeric.questionTypeWeb:
                    if (payload.language == constants.langStringToLangCode['React-Jest']) {
                        canPushToRedis = false;
                        break;
                    }
                    obj.html = payload.html;
                    obj.css = payload.css;
                    obj.js = payload.js;
                    obj = {...obj, ...dataObj};
                    break;
                default:
                    canPushToRedis = false;
                    break;
            }
            obj.questionType = payload.questionType;
            dataObj = dataObj || {};
            if ( canPushToRedis ) {
                obj._id = mongoose.Types.ObjectId();
                services.RedisServices.redis2('rpush', userAttemptListByQuesRedisKey, JSON.stringify(obj) );
            }
            else    dataObj.notUpdateScore = true;
            if (cb)     cb(null, dataObj);
            return dataObj;
        })
        
    } catch (e) {
        console.log("saveSubmissionInRedis Error = ", e, payload);
        if (cb)     cb(e);
        return null;
    }
}

var setValuesInRedisAfterSubmission = async function (payload, questionType, callback) {
    if (!(payload && callback && typeof callback === 'function')) {
        logger.debug("invalid parameters setValuesInRedisAfterSubmission payload-", payload, questionType);
        logger.error("invalid parameters setValuesInRedisAfterSubmission ");
        callback("invalid parameters setValuesInRedisAfterSubmission");
        return;
    }
    try {
        payload.questionType = questionType;
        let key = redisKeys.ongoingQuiz + ":" + payload.userId + ":" + payload.quizId;
        let submittedQuestionKeys = key + ":" + redisKeys.submittedQuestions
        // let redisObj = [ libs.constants.redisKeys.questionIdString + ':' + payload.questionId];
        services.RedisServices.redis("hget", key, redisKeys.questionIdString + ':' + payload.questionId,
            (errMget, questionDetail) => {
                if (errMget) {
                    logger.debug(" Error in getting mget submitQuestion", errMget);
                    logger.error("Error in getting mget submitQuestion");
                    callback(errMget);
                    return;
                }
                questionDetail = questionDetail ? JSON.parse(questionDetail) : {}
                services.RedisServices.redis("sadd", submittedQuestionKeys, payload.questionId, (errQues, submittedQuestions) => {

                    if (errQues) {
                        logger.debug(" Error in getting smembers submitQuestions", errQues);
                        logger.error("Error in getting smembers submitQuestions");
                        callback(errQues);
                        return;
                    }
                    // let redisObj = [submittedQuestions, questionDetail]

                    let { redisKeys } = libs.constants;
                    let updateQuestion = true;
                    questionDetail.hasSubmitted = true;
                    if (questionType == libs.constants.questionTypeNumeric.questionTypeMCQ) {
                        let selectedOption = parseInt(payload.chk.replace("chkOpt", ""), 10) - 1;
                        if (questionDetail.userInputMCQ != selectedOption) {
                            questionDetail.userInputMCQ = selectedOption;
                        } else {
                            updateQuestion = false;
                        }
                    } else if (questionType == libs.constants.questionTypeNumeric.questionTypeCoding) {
                        questionDetail.userProgram = payload.txtCodeAt;
                        questionDetail.userLanguage = payload.language
                    } else if (questionType == libs.constants.questionTypeNumeric.questionTypeSubjective) {
                        questionDetail.userInputSubjective = payload.txtSub;
                    } else if (questionType == libs.constants.questionTypeNumeric.questionTypeStepwise) {
                        questionDetail.userOutputCoding = payload.stepwiseUserInput ? payload.stepwiseUserInput : []
                    }  else if (questionType == libs.constants.questionTypeNumeric.questionTypeWeb) {
                        questionDetail.html = payload.html || "";
                        questionDetail.css = payload.css || "";
                        questionDetail.js = payload.js || "";
                    }

                    

                    saveSubmissionInRedis(payload, (err, dataObj = {}) => {
                        if (err) {
                            logger.debug(" Error in getSubmissionScore ", err, " Payload ", payload);
                            logger.error(" Error in getSubmissionScore setValuesInRedisAfterSubmission");
                            callback(err);
                            return;
                        }

                        if (!updateQuestion) {
                            callback(null);
                            return;
                        }

                        if ( ! dataObj.notUpdateScore )   questionDetail.score = parseInt(dataObj.score) || 0;
                        let key = redisKeys.ongoingQuiz + ":" + payload.userId + ":" + payload.quizId;
                        services.RedisServices.redis("hset", key, redisKeys.questionIdString + ':' + payload.questionId, JSON.stringify(questionDetail), (errQues) => {
                            if (errQues) {
                                logger.debug(" Error in setting submitted question data ", errQues, " key ", key);
                                logger.error(" Error in setting submitted question data setValuesInRedisAfterSubmission");
                                callback(errQues);
                                return;
                            }
                            if ( ! dataObj.notUpdateScore ) {
                                key = redisKeys.ongoingQuiz + ":" + payload.userId + ":" + payload.quizId  + ":" + redisKeys.submittedQuesScores;
                                services.RedisServices.redis("hset", key, payload.questionId, questionDetail.score);
                                //services.RedisServices.redis("expire", key, libs.constants.expiryTimeOfQuizReportInSec );
                            }
                            
                            callback(errQues);
                        })
                    })
                })
            })
    } catch (exception) {
        logger.debug("setValuesInRedisAfterSubmission Error in parsing redisObj ", exception.message, "stack--", exception.stack, " redisObj ", redisObj)
        logger.error("setValuesInRedisAfterSubmission Error in parsing redisObj")
        callback(exception);
        return;
    }
};

var getMultipleAnswerResult = function( obj, cb ){
    // checking validity of obj
    let isCallback = cb && typeof cb === 'function';
    let onlyResultRequired = obj && obj.payload && obj.payload.onlyResultRequired;           // Only Result required, no db write and socket emission
    if (!isValidData(obj)) {
        logger.debug(" object payload was invalid getMultipleAnswerResult", obj.payload);
        logger.error(" object payload was invalid getMultipleAnswerResult");
        if( isCallback )    cb({error : "Invalid multiple answer payload"});
        if ( ! onlyResultRequired ) {
            sendNotificationToAdmin(obj);
            if (!obj.payload.questionSubmission)
                sendDataToSocket(io, obj, 'ERROR', "payload was invalid ", libs.constants.questionTypeNumeric.questionTypeStepwise);
        }
        return;
    }
    getCodingQuestionFromDb(obj, (errorQues, question) => {
        // questionController.getQuestionFromId(obj.payload.questionId, (errorQues, question) => {
        if (errorQues) {
            logger.debug("error in request compilePayloadObj ", errorQues);
            logger.error("error in request compilePayloadObj");
            if( isCallback )    cb({error : "Error in fetching multiple answer question from db"})
            if ( ! obj.payload.questionSubmission && ! onlyResultRequired )
                sendDataToSocket(io, obj, 'ERROR', "question id is not valid", question.type);
            services.RedisServices.redis("set", libs.constants.redisKeys.isCompilationResponsePending + global.workerIndex, libs.constants.redisKeys.emptyString);
            return;
        }
        else if (!question) {
            logger.error("question not present " + obj.payload.questionId);
            if( isCallback )    cb({error : "Question not present"})
            if ( ! obj.payload.questionSubmission && ! onlyResultRequired )
                sendDataToSocket(io, obj, 'ERROR', "question not present");
            return;
        }
// TODO - see if this can be done with question type
        if (obj.payload.isStepwise) {
            let outputObj = {};
            outputObj.testCase = question.questionTypeCoding.testCase;
            services.RedisServices.redis("setnx", obj.payload.questionId, JSON.stringify(outputObj));
            outputObj.outputArray = obj.payload.stepwiseUserInput;
            obj.testCaseComparisonResult = libs.util.compareTestCaseAndResult(question.questionTypeCoding.testCase, obj.payload.stepwiseUserInput, "1");
            obj.payload.testCaseComparisonResult = obj.testCaseComparisonResult;
            outputObj.txtCodeAt = obj.payload.code;
            outputObj.progLang = obj.payload.language;
            if ( ! onlyResultRequired ) {
                if (obj.payload.executionType === libs.constants.codingQuestionExecutionType.resultOnly) {
                    if (!obj.payload.questionSubmission)
                        sendDataToSocket(io, obj, 'compile', obj.testCaseComparisonResult, question.type);
                    else
                        saveAttemptInDb(obj, obj.testCaseComparisonResult, question.type, cb )
                }
                else {
                    if (!obj.payload.questionSubmission)
                        sendDataToSocket(io, obj, 'compile', outputObj, question.type);
                    else
                        saveAttemptInDb(obj, outputObj, libs.constants.questionTypeNumeric.questionTypeStepwise, cb)
                }
            }
            if ( isCallback )   cb(null, {outputObj, 'testCaseComparisonResult': obj.testCaseComparisonResult});
            return {outputObj, 'testCaseComparisonResult': obj.testCaseComparisonResult};
        }
        else {
            if ( isCallback )   cb(null, {});
            return {};
        }
    });
}

var shouldSaveQuestionAttmpt = function( obj, data, questionType , callback ){
    if( obj.payload.isInvalidAttempt){
        return callback(null, false )
    }
    
    if( obj.payload.shouldSave ||  obj.payload.questionSubmission 
        || questionType !== libs.constants.questionTypeNumeric.questionTypeCoding ){
        callback( null, true );
        return;
    }

    if( obj.payload.isCustomInput || obj.payload.runSampleTC || obj.payload.isCachedResult ){
        return callback( null, false)
        //if you want to save custom input then make sure you also add stdin in redis before saving payload
    }

    if (obj.payload.courseId) {
        callback( null, obj.payload.isCustomInput ? false : true);
        return ;
    }
   
            let key = libs.constants.redisKeys.ongoingQuiz + ":" + obj.payload.userId + ":" + obj.payload.quizId;
            let questionKey = libs.constants.redisKeys.maxScore + ":" + obj.payload.questionId;
            services.RedisServices.redis("hget",  key , questionKey, ( errQues, value )=> {
                if (errQues) {
                    logger.error("shouldSaveQuestionAttmpt error while getting max score of question ")
                    logger.debug("shouldSaveQuestionAttmpt error while getting max score of question ", obj, " error ", errQues );
                    callback(null, true);
                    return;
                } else if( !value ){
                    services.RedisServices.redis("hset",  key, questionKey, obj.testCaseComparisonResult && obj.testCaseComparisonResult.totalScore
                        ? obj.testCaseComparisonResult.totalScore : 0, ( errScore )=>{
                        if( errScore ){
                            logger.error("shouldSaveQuestionAttmpt error while saving max score ")
                            logger.debug("shouldSaveQuestionAttmpt error while saving max score ", obj, " error ", errScore );
                        }
                        callback(null, true);
                        return;
                    })
                } else {
                    if( obj.testCaseComparisonResult ) {
                        services.RedisServices.redis("hset",  key, questionKey, obj.testCaseComparisonResult && obj.testCaseComparisonResult.totalScore
                            ? obj.testCaseComparisonResult.totalScore : 0, ( errScore )=>{
                            if( errScore ){
                                logger.error("shouldSaveQuestionAttmpt error while saving max score ")
                                logger.debug("shouldSaveQuestionAttmpt error while saving max score ", obj, " error ", errScore );
                            }
                            callback(null, true);
                            return;
                        })
                    } else if ( data && data.errors && data.errors.trim() ) {
                        callback(null, true);
                        return;
                    } else{
                        callback(null, false );
                        return;
                    }
                }

            });
     
}

const handleCompileResponse = function(dataObj) {
    if (dataObj?.codeId) {
        console.log(`Compiler Response came for codeid: ${dataObj.codeId}`);
    }
    switch(parseInt(dataObj.codeType)) {
        case libs.constants.compileServerConstants.codeType.IDE_CODE:
            handleCompileIdeResponse(dataObj);
            break;
        case libs.constants.compileServerConstants.codeType.DEFAULT_CODE:
            handleCompileDefaultQuestionResponse(dataObj);
            break;
        default:
            handleCompileQuestionResponse(dataObj);
            break;
    }
}

let reevaluation = {
    reevaluateSingleCode: function( dataObj ) {
        const userAttemptDataId = dataObj.userAttemptDataId;
        const userAttemptId = dataObj.userAttemptId;
        const socketId = dataObj.socketId;
        
        if ( ! userAttemptDataId || ! userAttemptId ) {
            logger.debug("Data is invalid in re-evalute");
            dataObj.error = "Data is invalid in re-evalute";
            if ( socketId )     io.to( dataObj.socketId ).emit('reevaluate', dataObj);
            return ;
        }

        let findObj = { '_id': userAttemptId, "attemptData._id": userAttemptDataId };
        let projObj = { 'attemptData.$': 1, 'userAnswerId': 1 }
        userAttemptController.getOneUserAttempt( findObj, projObj, {}, (err, result) => {
            if ( err ) {
                logger.debug("Error in re-evaluate getOneUserAttempt, Error = ", err);
                dataObj.error = "Error in re-evaluate getOneUserAttempt";
                if ( socketId )     io.to( dataObj.socketId ).emit('reevaluate', dataObj);
                return ;
            }
    
            let payload = {
                code: result.attemptData[0].userProgram,
                executionType: "2",
                isCompleted: "false",
                isCustomInput: false,
                language: result.attemptData[0].userLanguage,
                questionId: result.userAnswerId.questionId._id.toString(),
                quizId: result.userAnswerId.quizId._id.toString(),
                role: "0",
                //sid: req.session.id,
                stdin: "",
                userId: result.userAnswerId.userId._id.toString(),
                questionSubmission: true,
                reevaluate: true,
                reevaluateObj: {
                    userAttemptId,
                    userAttemptDataId,
                    curOutput: result.attemptData[0].userOutputCoding,
                    curError: result.attemptData[0].userCompilationError,
                    changeInDb: dataObj.changeInDb
                }
            }
            compileQuestion(payload, {id: dataObj.socketId});
        })
    },
    reevaluateSingleCode2: function( dataObj ) {
        const userAttemptDataId = dataObj.userAttemptDataId;
        const userAttemptId = dataObj.userAttemptId;
        const socketId = dataObj.socketId;
        const userAttemptDataObj = dataObj.userAttemptDataObj;
        
        if ( ! userAttemptDataId || ! userAttemptId ) {
            logger.debug("Data is invalid in re-evalute");
            dataObj.error = "Data is invalid in re-evalute";
            if ( socketId )     io.to( dataObj.socketId ).emit('reevaluate', dataObj);
            return ;
        }
            
        //console.log("Result = ", result);
        let payload = {
            code: userAttemptDataObj.userProgram,
            executionType: "2",
            isCompleted: "false",
            isCustomInput: false,
            language: userAttemptDataObj.userLanguage,
            questionId: dataObj.quesId,
            quizId: dataObj.quizId,
            role: "0",
            //sid: req.session.id,
            stdin: "",
            userId: dataObj.userId, 
            questionSubmission: true,
            reevaluate: true,
            reevaluateObj: {
                userAttemptId,
                userAttemptDataId,
                curOutput: userAttemptDataObj.userOutputCoding,
                curError: userAttemptDataObj.userCompilationError,
                onlyResult: dataObj.onlyResult
            }
        }
        //console.log("Socket Id = ", dataObj.socketId);
        compileQuestion(payload, {id: dataObj.socketId});
        
    },
    handleReevaluateAttempt: function (obj, outputObj) {
        try {
            const redisSentKey = libs.constants.redisKeys.reEvaluateQuizTotalSent + ':' + obj.payload.quizId;
            const redisReceiveKey = libs.constants.redisKeys.reEvaluateQuizTotalReceive + ':' + obj.payload.quizId;
            const redisWrongAttemptsKey = libs.constants.redisKeys.reEvaluateQuizWrongAttempts + ':' + obj.payload.quizId;
            services.RedisServices.redis('incr', redisReceiveKey, (err, receive) => {
                services.RedisServices.redis('get', redisSentKey, (err, sent) => {
                    /*
                    services.RedisServices.redis('smembers', redisWrongAttemptsKey, (err, wrongAttempts) => {
                        let objToSend = {
                            'error': err,
                            'sent': sent,
                            'receive': receive,
                            'attempts': wrongAttempts
                        }
                        if (obj.socketId)   io.to(obj.socketId).emit('quizReEvaluation', objToSend);
                    })
                    */
                    console.log("ReEvaluate Sent = ", sent, " Receive = ", receive);
                    if ( redisSentKey == redisReceiveKey ) {
                        services.RedisServices.redis('expire', redisSentKey, 30);
                        services.RedisServices.redis('expire', redisReceiveKey, 30);
                        services.RedisServices.redis('expire', redisWrongAttemptsKey, 60*10);
                        services.RedisServices.redis('srem', libs.constants.redisKeys.reEvaluatingQuizzesIdSet, obj.payload.quizId);
                    }
                })
            })
            let dataObj = {
                'userAttemptId': obj.payload.reevaluateObj.userAttemptId,
                'userAttemptDataId': obj.payload.reevaluateObj.userAttemptDataId
            }
            let origOutputArray = obj.payload.reevaluateObj.curOutput;
            let userOutputArray = [];
            let newOutputArray = [];
            let isDiff = false;

            if (! origOutputArray) {
                console.log("Orignal ouput array is null");
                return;
            }

            if ( outputObj.errors ) {
                //console.log("outputObj.errors = ", outputObj.errors);
                //console.log("Cur error = ", obj.payload.reevaluateObj.curError);
                isDiff = outputObj.errors != obj.payload.reevaluateObj.curError;
            }
            else {
                userOutputArray = obj.testCaseComparisonResult.userOutputArray;
                if (! userOutputArray) {
                    console.log("testCaseComparisonResult.userOutputArray is null");
                    return;
                }
                isDiff = origOutputArray.length != userOutputArray.length;
                for (let index = 0; index < userOutputArray.length; index++) {
                    newOutputArray[index] = userOutputArray[index].userOutput;
                    if ( origOutputArray[index] != newOutputArray[index] )    isDiff = true;
                }
            }
            
            if ( isDiff ) {
                const redisWrongAttemptsKey = libs.constants.redisKeys.reEvaluateQuizWrongAttempts + ':' + obj.payload.quizId;
                let valueToAdd = obj.payload.reevaluateObj.userAttemptId + '-' + obj.payload.reevaluateObj.userAttemptDataId;
                services.RedisServices.redis('sadd', redisWrongAttemptsKey, valueToAdd, (err, isAdded) => {
                    if ( isAdded ) {
                        if ( obj.payload.reevaluateObj.onlyResult && obj.socketId ) {
                            io.to(obj.socketId).emit('quizReEvaluationAddWrongAttempt', {'attempt': valueToAdd});
                            return 
                        }
                    }
                });
                
                if ( obj.payload.reevaluateObj.onlyResult ) return ;

                // if ( ! obj.payload.reevaluateObj.changeInDb ) {
                //     console.log("Change in db false");
                //     return ;
                // }
                //services.RedisServices.redis("incr", "reevaluate-Diff");
                //services.RedisServices.redis("sadd", "reval-list", obj.payload.reevaluateObj.userAttemptId + '-' + obj.payload.reevaluateObj.userAttemptDataId);
                let findObj = {
                    "_id": mongoose.Types.ObjectId(obj.payload.reevaluateObj.userAttemptId),
                    "attemptData._id": mongoose.Types.ObjectId(obj.payload.reevaluateObj.userAttemptDataId)
                }
                let updateObj = {
                    $set: {
                        "attemptData.$.userOutputCoding": newOutputArray,
                        "attemptData.$.userCompilationError": outputObj.errors ? outputObj.errors : ""
                    }
                }
                userAttemptController.updateUserAttemptInDb(findObj, updateObj, {}, (err, result) => {
                    if (err) {
                        console.log("Err in handleReevaluateAttempt updateUserAttempt, Error = ", err);
                        return;
                    }
                    //console.log("User attempt reevaluated successfully, Result = ", result);

                    // TODO - Update user answer also
                    findObj = {
                        quizId: mongoose.Types.ObjectId(obj.payload.quizId),
                        userId: mongoose.Types.ObjectId(obj.payload.userId),
                        "quizSubmittedQuestion.questionId": mongoose.Types.ObjectId(obj.payload.questionId)
                    }

                    updateObj = {
                        $max: {
                            "quizSubmittedQuestion.$.score": outputObj.errors ? 0 : obj.testCaseComparisonResult.totalScore
                        }
                    }

                    userQuizSubmittedSegmentController.updateUserQuizSubmittedSegment(findObj, updateObj, {}, (err, result) => {
                        if (err) {
                            console.log("Err in handleReevaluateAttempt updateUserQuizSubmittedSegment, Error = ", err);
                            return;
                        }
                        //console.log("updateUserQuizSubmittedSegment max score changes, Result = ", result);
                        dataObj.msg = "Score changes";
                        if (obj.socketId) io.to(obj.socketId).emit('reevaluate', dataObj);
                    })
                })
            }
            else {
                //console.log("No difference in score");
                dataObj.msg = "No difference in score";
                if (obj.socketId) io.to(obj.socketId).emit('reevaluate', dataObj);
            }
        }
        catch (e) {
            console.log("handleReevaluateAttempt");
            console.log("Obj = ", obj);
            console.log("Output obj = ", outputObj);
            console.log("Error = ", e);
            
        }
    }
}

const sendPostRequestToMasterCompileServer = ( payload, headers, callback ) => {
    payload.url = libs.constants.compileServerConstants.masterCompileServerUrl;

    let requestObj = {};
    requestObj.form = {
        'language': payload.language,
        'code': payload.code,
        'files': payload.files,
        'projectObj': payload.projectObj,
        'responseUrl': libs.constants.compileServerConstants.responseUrl,
        'codeId': payload.codeId,
        'codeType': payload.codeType,
    };

    if ( payload.uniqueId )       requestObj.form.uniqueId = payload.uniqueId;
    if ( payload.filesContent )   requestObj.form.filesContent = payload.filesContent;
    if ( payload.filesMd5 )       requestObj.form.filesMd5 = payload.filesMd5;
    if ( payload.isMainServer )   requestObj.form.isMainServer = payload.isMainServer;
    if ( payload.testInputArr )   requestObj.form.testInputArr = payload.testInputArr;
    if ( payload.testCaseCloudId && payload.testCaseCloudId.length ) {
        requestObj.form.testCaseCloudId = payload.testCaseCloudId;
        requestObj.form.cloudTestCaseInputString = libs.constants.cloudTestCaseInputString;
        requestObj.form.questionId = payload.questionId;
        requestObj.form.updatedAt = payload.updatedAt ?? new Date();
        //TODO: NEED TO INVESTIGATE FURTER
        requestObj.form.codeType = libs.constants.compileServerConstants.codeType.CLOUD_TEST_CASE;
    }
    requestObj.url = payload.url;
    requestObj.headers = headers;
    requestObj.method = "POST";
    // console.log("reqObj url = ", requestObj);
    
    request(requestObj, callback);
}

const startIntervals = function(intervalValue = 1000 ) {

    try {
        interval = parseInt( intervalValue );
        if( isNaN( interval ) ) throw new Error("Interval is not valid--" + intervalValue)
        let { submissionCounterIntervalId, submissionInterval } = global;
        interval = Math.max(interval, libs.constants.submissionQueueData.minSubmissionInterval );
        console.log(`submissionCounterIntervalId = ${submissionCounterIntervalId} submission interval =${submissionInterval} interval= ${intervalValue} interval = ${interval} `)
        if (submissionCounterIntervalId) {
            if (interval == submissionInterval)
                throw new Error(`Interval values are same i.e. ${ intervalValue }`);
            console.log("Clearing old interval",submissionCounterIntervalId)
            clearInterval(submissionCounterIntervalId);
        }
        submissionCounterIntervalId = setInterval(saveSubmissionsFromSubmissionQueue, interval);    
        console.log(`new submissionCounterIntervalId = ${submissionCounterIntervalId}`)
                          // 1 sec
        global.submissionCounterIntervalId = submissionCounterIntervalId;
        global.submissionInterval = interval;
        return { interval, oldInterval: submissionInterval  }
    } catch (e) {
        console.error("Error while setting interval ", e)
        return { error : e.message, interval : global.submissionInterval};
    }
}
const moveExplicitQuizToActiveQuizSet = async function ( activityPayload = {}) {
    // try {
        const { allowedIP, quizSegments, quizContent, languagesAllowed, quizTime,
            copyPasteAllowed, revisitAllowed, title, tabSwitchAllowed, isWebCamAllowed,
            questionId, randomizeQuestion, poolQuestion } = libs.constants.quizParamsHMSET;

        let explicitQuizIdsData = await services.RedisServices.redis("hgetall", redisKeys.quizIdsForExplicitSubmissions);
        activityPayload.explicitQuizIdsData = {};
        // console.log(" explicitQuizIdsData ", explicitQuizIdsData)
        let quizIds = Object.keys(explicitQuizIdsData);
        for (let i = 0; i < quizIds.length; i += 1) {
            let removeFromExplicitSet = true;
            let explicitEntryTime = explicitQuizIdsData[quizIds[i]];
            let quizLastActivityTime = await services.RedisServices.redis("get", `${redisKeys.quizLastActivityTime}:${quizIds[i]}`);
            quizLastActivityTime = quizLastActivityTime ? parseInt(quizLastActivityTime) : 0;
            // console.log("quiz last activity time ", `${redisKeys.quizLastActivityTime}:${quizIds[i]}`, new Date(quizLastActivityTime)) //console.log("quizz time ", quizTime );
            
            activityPayload.explicitQuizIdsData = activityPayload.explicitQuizIdsData || {};
            activityPayload.explicitQuizIdsData[ quizIds[i]] = activityPayload.explicitQuizIdsData[ quizIds[i]] || {};
            
            let quiz = await quizController.getQuizFromDb({
                id: quizIds[i],
                projections: [quizSegments.key,
                quizContent.key, languagesAllowed.key, quizTime.key,
                copyPasteAllowed.key, revisitAllowed.key, title.key,
                tabSwitchAllowed.key, allowedIP.key, isWebCamAllowed.key,
                randomizeQuestion.key, poolQuestion.key]
            });
            if (!quiz) throw new Error(`Quiz not present ${ quizIds[i]}`)
            console.log("Quiz time =", quiz.quizTime);
            if (quizLastActivityTime + (quiz.quizTime * 60 * 1000) +( libs.constants.submissionQueueData.offsetTimeAfterQuizTime * 1000 ) < Date.now()) {
                let isMember = await services.RedisServices.redis("sismember", libs.constants.redisKeys.activeQuizSet, quizIds[i]);
                console.log("Is Quiz member of active quiz set", quizIds[i], isMember )
                if (isMember) {
                    await services.RedisServices.redis("hdel", redisKeys.quizIdsForExplicitSubmissions, quizIds[i]);
                    console.log("quiz removed from explicit set", quizIds[i])
                    continue;
                }
                let joinedUserIds = await services.RedisServices.redis("smembers", `${libs.constants.redisKeys.joinedUserIds}:${quizIds[i]}`);
                let update = false;
                console.log(" joined user ids ", joinedUserIds );
                activityPayload.explicitQuizIdsData[ quizIds[i]].joinedUserIds = joinedUserIds;
                for (let j = 0; j < joinedUserIds.length; j += 1) {
                    let userId = joinedUserIds[j], quizId = quizIds[i];
                    let key = libs.constants.redisKeys.ongoingQuiz + ":" + userId + ":" + quizId;
                    let userQuizSegment = await userQuizSubmittedSegmentController.getUserQuizSubmittedSegmentFromDb({ 'userId': userId, 'quizId': quizId }, {fromRedis : true});
                    if(!userQuizSegment){
                        console.log("user quiz segment not in redis", "user id = ", userId, " quiz id =", quizId);

                        await services.RedisServices.redis('srem', `${libs.constants.redisKeys.joinedUserIds}:${quizId}`, userId);
                        continue;
                    }
                    let values = await services.RedisServices.redis("hmget", key, [
                        libs.constants.redisKeys.endTime, 
                        libs.constants.redisKeys.startTime,
                    libs.constants.redisKeys.sessionTime,
                    libs.constants.redisKeys.userSessions,
                    libs.constants.redisKeys.extraTime,
                    libs.constants.redisKeys.extraTimeExpiry,]);
                    let endTime = values[0];
                    console.log(" values for userId ", userId, " quiz id ", quizId, values)
                    activityPayload.explicitQuizIdsData[ quizIds[i]][ userId] = { values };
                    if (values[1]) {
                        let startTime = parseInt(values[1]);
                        startTime = new Date(isNaN(startTime) ? values[1] : startTime);
                        if (!endTime) {
                            let extraTime = values[4] || 0;
                            extraTime = parseInt( extraTime );
                            let sessionTime = values[2];
                            if (!sessionTime) {
                                console.log(`Session time not present for user Id ${userId} and quizId ${quizId}`);
                                console.log("Values are ", values)
                                continue;
                            }
                            if (sessionTime) sessionTime = parseInt(sessionTime);
                            let endTime = new Date(startTime.getTime() + (sessionTime ? 
                                ( (sessionTime + extraTime ) * 1000) : quiz.quizTime * 60000));

                            let userSessions = values[3] || JSON.stringify([]);
                            userSessions = JSON.parse(userSessions);
                            if( !userSessions?.length ){
                                if( quiz.quizTime != sessionTime ){
                                    sessionTime = quiz.quizTime // because quiz has been updated and user left the test before that
                                }
                            }
                            userSessions.push({
                                startTime: values[1], endTime, sessionTime,
                                explicitSubmission: true,
                                extraTime
                            })
                            userSessions = JSON.stringify(userSessions);
                            let setObjForUser = {
                                endTime, explicitSubmission : true, userSessions, startTime : 0, sessionTime : null
                            }
                            activityPayload.explicitQuizIdsData[ quizIds[i]][ userId].setObj = setObjForUser;
                            await services.RedisServices.redis("hmset", key,
                                libs.constants.redisKeys.endTime, endTime,
                                libs.constants.redisKeys.explicitSubmission, true,
                                libs.constants.redisKeys.userSessions, userSessions,
                                libs.constants.redisKeys.startTime, 0,
                                libs.constants.redisKeys.sessionTime, null
                            );
                            console.log("User added to submission list automatically = ", userId, "quiz Id ", quizIds[i], " set obj ", setObjForUser );
                        }
                       
                        update = true
                    // }
                    // if (values[1]) {
                        await services.RedisServices.redis("sadd", `${libs.constants.redisKeys.submittedUserIds}:${quizId}`, userId);
                    } else if (values[2]) {
                        console.log("Session time is present but start time not, deleting session time ", values[2]);
                        let addInSubmissionList = true;
                        let extraTimeExpiry = values[5];
                        if (extraTimeExpiry) {
                            extraTimeExpiry = new Date(extraTimeExpiry);
                            let currentDate = new Date();
                            if (currentDate < extraTimeExpiry) {
                                // console.log("Still time left for extra time to be expired",
                                    // currentDate, extraTimeExpiry);
                                removeFromExplicitSet = false;
                                continue;
                            } else {
                                console.log("Extra time expiry reached ", currentDate, extraTimeExpiry);
                            }
                        }

                        let userSessions = values[3] || JSON.stringify([]);
                        userSessions = JSON.parse(userSessions);
                        if (userSessions.length) {
                            endTime = userSessions[userSessions.length - 1].endTime || new Date()
                        }
                        let setObjForUser = {
                            endTime, explicitSubmission: true, startTime: 0, sessionTime: null,
                            extraTime: null
                        }
                        activityPayload.explicitQuizIdsData[quizIds[i]][userId].setObj = setObjForUser;
                        await services.RedisServices.redis("hmset", key,
                            libs.constants.redisKeys.endTime, endTime,
                            libs.constants.redisKeys.explicitSubmission, true,
                            libs.constants.redisKeys.startTime, 0,
                            libs.constants.redisKeys.sessionTime, null,
                            libs.constants.redisKeys.extraTime, 0,
                        );
                        console.log("User added to submission list automatically after extratime expiry = ",
                            userId, "quiz Id ", quizIds[i], " set obj ", setObjForUser);

                        await services.RedisServices.redis('hdel', key, libs.constants.redisKeys.sessionTime)
                        await services.RedisServices.redis('hdel', key, libs.constants.redisKeys.extraTimeExpiry);
                        await services.RedisServices.redis("sadd", `${libs.constants.redisKeys.submittedUserIds}:${quizId}`, userId);
                        update = true;
                        // }

                    }else {
                        console.log("Start time and session time not present for user ", userId, quizId );
                        // await services.RedisServices.redis('del', key ); 
                    }
                }
                if (update) {
                    userController.updateQuizActiveInfo({ quizId: quizIds[i] })
                }  
            }
            if (removeFromExplicitSet) {
                console.log("Removing from explicit quiz set ", quizIds[i]);
                await services.RedisServices.redis("hdel", redisKeys.quizIdsForExplicitSubmissions, quizIds[i]);
            }
        }
        global.isProcessingSubmission = false;
        return;
}
const saveSubmissionsFromSubmissionQueue = async function () {
    let activityPayload = {};
    try {
        if (global.isProcessingSubmission)
            return;
        global.isProcessingSubmission = true;
        let stopSubmission = await services.RedisServices.redis("get", 'stop' );
       
        if( stopSubmission )  {
            // console.log("Submission stopped");
            global.isProcessingSubmission = false;
            return;
        }

        if( !global.lastSubmissionTime ){
            console.log("Setting submission time for first time");
            global.lastSubmissionTime = Date.now()
        }
        activityPayload.lastSubmissionTime = global.lastSubmissionTime;
        
        let currentlyProcessingQuizId = global.currentlyProcessingQuizId,
            currentlyProcessingUserId = global.currentlyProcessingUserId;

        if (!currentlyProcessingQuizId) {
            currentlyProcessingQuizId = await services.RedisServices.redis("get", redisKeys.currentlyProcessingQuizId);
            if (currentlyProcessingQuizId) global.currentlyProcessingQuizId = currentlyProcessingQuizId;
            activityPayload.currentlyProcessingQuizIdFromRedis = currentlyProcessingQuizId;

        }

        if (!currentlyProcessingQuizId) {
            currentlyProcessingQuizId = await services.RedisServices.redis("srandmember", redisKeys.activeQuizSet);
            activityPayload.currentlyProcessingQuizIdFromActiveQuizSet = currentlyProcessingQuizId;
            if( currentlyProcessingQuizId == 'undefined'){
                console.log("Undefined quiz encountered");
                await services.RedisServices.redis("srem", redisKeys.activeQuizSet, 'undefined');
                global.isProcessingSubmission = false;
                return;
            }
            await services.RedisServices.redis("set", redisKeys.currentlyProcessingQuizId, currentlyProcessingQuizId);
            global.currentlyProcessingQuizId = currentlyProcessingQuizId;
        }

        const { allowedIP, quizSegments, quizContent, languagesAllowed, quizTime,
            copyPasteAllowed, revisitAllowed, title, tabSwitchAllowed, isWebCamAllowed, 
            questionId, randomizeQuestion, poolQuestion } = libs.constants.quizParamsHMSET;

            // if( currentlyProcessingQuizId)// console.log(" currentlyProcessingQuizId ", currentlyProcessingQuizId)
        if (!currentlyProcessingQuizId) {
            // console.log("No active quiz present");
            //checking for those quizzes which are in the explicit list
            await moveExplicitQuizToActiveQuizSet(activityPayload);
        }

        if( !currentlyProcessingQuizId )    {
            global.isProcessingSubmission = false;
            return; 
        }
        let quiz = await quizController.getQuizFromDb({
            id: currentlyProcessingQuizId,
            projections: [quizSegments.key,
            quizContent.key, languagesAllowed.key, quizTime.key,
            copyPasteAllowed.key, revisitAllowed.key, title.key,
            tabSwitchAllowed.key, allowedIP.key, isWebCamAllowed.key, 
            questionId.key,randomizeQuestion.key, poolQuestion.key
        ]
        });
        if (!quiz) throw new Error("Quiz not present")
        activityPayload.quiz = quiz;
        if (!currentlyProcessingUserId) {
            global.currentlyProcessingUserId = await services.RedisServices.redis("srandmember", `${redisKeys.submittedUserIds}:${currentlyProcessingQuizId}`);
            currentlyProcessingUserId = global.currentlyProcessingUserId;
            activityPayload.currentlyProcessingUserIdFromSubmittedUsers = currentlyProcessingUserId;
        }
        
        if (!currentlyProcessingUserId) {
            // console.log("No submitted user present for quizId", currentlyProcessingQuizId);
            let quizLastActivityTime = await services.RedisServices.redis("get", `${redisKeys.quizLastActivityTime}:${currentlyProcessingQuizId}`);
            quizLastActivityTime = quizLastActivityTime ? parseInt( quizLastActivityTime) : 0;  
           

            global.currentlyProcessingUserId = null;
            global.currentlyProcessingQuizId = null;
            await services.RedisServices.redis("del", redisKeys.currentlyProcessingQuizId);
            let quizTime = parseInt( quiz.quizTime , 10 );
            // console.log("quiz last activity time ",  `${redisKeys.quizLastActivityTime}:${currentlyProcessingQuizId}`, new Date(quizLastActivityTime),"quiz time =", quizTime)
            // console.log("submissionTime ", new Date(( new Date(quizLastActivityTime)).getTime() + ( quizTime * 60000 ) + ( libs.constants.submissionQueueData.offsetTimeAfterQuizTime * 1000 ) )) //console.log("quizz time ", quizTime );
            //console.log("quizLastActivityTime + (( quizTime * 60 * 1000)) ", quizLastActivityTime + (( quizTime * 60 * 1000)), " Date.now() ", Date.now() );
            if (quizLastActivityTime + (( quizTime * 60 * 1000)) + ( libs.constants.submissionQueueData.offsetTimeAfterQuizTime * 1000 ) < Date.now()) {
                activityPayload.quizAcitvityTimePassed = {
                    quizLastActivityTime, offsetTime : libs.constants.submissionQueueData.offsetTimeAfterQuizTime, time : Date.now()
                }
                let joinedUserIds = await services.RedisServices.redis("smembers", `${libs.constants.redisKeys.joinedUserIds}:${currentlyProcessingQuizId}`);
                console.log( " joined users in quiz ", currentlyProcessingQuizId, joinedUserIds );
                activityPayload.joinedUserIds = joinedUserIds;
                if( joinedUserIds.length ){
                    console.log("adding in explicit submission set", currentlyProcessingQuizId)
                    await services.RedisServices.redis("hset", redisKeys.quizIdsForExplicitSubmissions, currentlyProcessingQuizId, Date.now());
                    activityPayload.quizAddedInExplicitSet = { currentlyProcessingQuizId, time : Date.now() }
                }
                await services.RedisServices.redis("srem", redisKeys.activeQuizSet, currentlyProcessingQuizId);
                activityPayload.removecFromActiveQuizSet = currentlyProcessingQuizId;
            } else if( global.lastSubmissionTime && (global.lastSubmissionTime + ( 5 * 60 * 1000) ) < Date.now()){
                console.log("Submission handler idle for more than 10 minutes", global.lastSubmissionTime );
                global.lastSubmissionTime = Date.now();
                await moveExplicitQuizToActiveQuizSet()
            }
            global.isProcessingSubmission = false;
            return;
        }
        console.log("Currently processing user id", currentlyProcessingUserId, currentlyProcessingQuizId )
        let userQuizSubmittedSegment;
        global.lastSubmissionTime = Date.now();
        let key = libs.constants.redisKeys.ongoingQuiz + ":" + currentlyProcessingUserId + ":" + currentlyProcessingQuizId;
        userQuizSubmittedSegment = await getUserQuizSubmittedSegmentForSubmission({ quizId: currentlyProcessingQuizId, userId: currentlyProcessingUserId });
        activityPayload.userQuizSubmittedSegment = userQuizSubmittedSegment;
        
        if (!userQuizSubmittedSegment) {
            console.log("Userquizsubmittedsegment not present for userId", currentlyProcessingUserId, " quizId", currentlyProcessingQuizId);
            await services.RedisServices.redis("srem", `${redisKeys.submittedUserIds}:${currentlyProcessingQuizId}`, currentlyProcessingUserId);
            await services.RedisServices.redis("srem", `${redisKeys.joinedUserIds}:${currentlyProcessingQuizId}`, currentlyProcessingUserId);
            await services.RedisServices.redis("del", key);
            global.currentlyProcessingUserId = null;
            global.isProcessingSubmission = false;
            // throw new Error("Userquizsubmittedsegment not present for userId" + currentlyProcessingUserId )
            return;
        }

        if (userQuizSubmittedSegment?.userSessions?.length) {
            let userSessionObj = userQuizSubmittedSegment.userSessions[userQuizSubmittedSegment.userSessions.length - 1];
            if (userSessionObj && userSessionObj.endTime && (Date.now() -
             libs.util.getDateObj({ value: userSessionObj.endTime }) < libs.constants.submissionQueueData.offsetTimeAfterQuizTime * 1000)) {
                // console.log("Exiting because not time yet ", ( Date.now() - libs.util.getDateObj({ value: userSessionObj.startTime }) )  / 1000 )
                global.currentlyProcessingUserId = null;
                global.isProcessingSubmission = false;
                return;
            }
            // console.log("Exiting because not time yet ", ( Date.now() - libs.util.getDateObj({ value: userSessionObj.startTime }) )  / 1000 )
            
        }

        if( userQuizSubmittedSegment.sessionTime && userQuizSubmittedSegment.sessionTime != '0' ){
            console.log("Session time not null adding user back to joined user ids from submitted users", currentlyProcessingUserId, userQuizSubmittedSegment );
            // throw new Error("Session time not present for userId " + currentlyProcessingUserId );
           
            await services.RedisServices.redis("sadd", `${redisKeys.joinedUserIds}:${currentlyProcessingQuizId}`, currentlyProcessingUserId);
            await services.RedisServices.redis("srem", `${redisKeys.submittedUserIds}:${currentlyProcessingQuizId}`, currentlyProcessingUserId);
            global.currentlyProcessingUserId = null;
            global.isProcessingSubmission = false;
            return;
        }

        if( !userQuizSubmittedSegment.endTime ){
            console.log("UserQuizSubmittedSegment end time not present", currentlyProcessingUserId)
            throw new Error("UserQuizSubmittedSegment end time not present" + currentlyProcessingUserId );
        }
        if( userQuizSubmittedSegment.endTime ){
            let userSessions = userQuizSubmittedSegment.userSessions || [];
            if( userSessions.length ){
                let lastSession = userSessions[ userSessions.length -1 ];
                if( !lastSession.explicitSubmission){
                    if( Date.now() - new Date(userQuizSubmittedSegment.endTime) < libs.constants.submissionQueueData.offsetTimeAfterQuizTime * 1000){
                        global.currentlyProcessingUserId = null;
                        global.isProcessingSubmission = false;
                        return;
                    }
                }
            }
        }

        // if( userQuizSubmittedSegment.userSessions && !userQuizSubmittedSegment.userSessions.length){
        //     throw new Error("Quiz user session cannot be empty")
        // }

        let attemptsSaved = await saveAttemptsFromRedis( { quiz, userQuizSubmittedSegment, activityPayload })
        activityPayload.attemptsSaved = attemptsSaved
        if (userQuizSubmittedSegment._id) delete userQuizSubmittedSegment._id;
        console.log("final userquiz submitted segment ", userQuizSubmittedSegment)
        activityPayload.userQuizSubmittedSegment = userQuizSubmittedSegment;
        if( userQuizSubmittedSegment.quizSubmittedQuestion && !userQuizSubmittedSegment.quizSubmittedQuestion.length ){
            // so that empty value don't overwrite previously saved values
            delete userQuizSubmittedSegment.quizSubmittedQuestion
        }
        
        if( userQuizSubmittedSegment.quizUserDetails && !userQuizSubmittedSegment.quizUserDetails.length){
            delete userQuizSubmittedSegment.quizUserDetails;
        }

        
        if( userQuizSubmittedSegment.userIp && !userQuizSubmittedSegment.userIp.length){
            delete userQuizSubmittedSegment.userIp;
        }
        
        if( userQuizSubmittedSegment.loginDetails && !userQuizSubmittedSegment.loginDetails.length){
            delete userQuizSubmittedSegment.loginDetails;
        }

        if( userQuizSubmittedSegment.tabSwitchReport && !userQuizSubmittedSegment.tabSwitchReport.length){
            delete userQuizSubmittedSegment.tabSwitchReport;
        }
        
        if( userQuizSubmittedSegment.fullScreenReport && !userQuizSubmittedSegment.fullScreenReport.length){
            delete userQuizSubmittedSegment.fullScreenReport;
        }

        if (userQuizSubmittedSegment.aiProctoringReport && !userQuizSubmittedSegment.aiProctoringReport.length) {
            delete userQuizSubmittedSegment.aiProctoringReport;
        }
        if (!userQuizSubmittedSegment.segmentSubmissionHistory?.length) {
            delete userQuizSubmittedSegment.segmentSubmissionHistory;
        }

        if (!userQuizSubmittedSegment.terminationDetails?.length) {
            delete userQuizSubmittedSegment.terminationDetails;
        }
        
        const userQuizSubmittedSegmentUpdatedObj = await userQuizSubmittedSegmentController.updateUserQuizSubmittedSegment({ userId: currentlyProcessingUserId, quizId: currentlyProcessingQuizId }, { $set: userQuizSubmittedSegment }, { upsert: true });
        
        let expireTime = 1;
        activityPayload.userQuizSubmittedSegmentUpdatedInDb = true;
       
       const quizSubmittedQuestions = userQuizSubmittedSegmentUpdatedObj?.quizSubmittedQuestion || [];
       for (let index = 0; index < quizSubmittedQuestions.length; index++) {
            const submittedQuesObj = quizSubmittedQuestions[index];
            if (submittedQuesObj?.scoreUpdationActivity?.length && submittedQuesObj.questionId) {
                const scoreUpdationRedisKey = redisKeys.getScoreUpdationActivityList(currentlyProcessingQuizId, currentlyProcessingUserId, submittedQuesObj.questionId);
                await services.RedisServices.redis("expire", scoreUpdationRedisKey, expireTime);
            }
       }

        await services.RedisServices.redis("expire", key, expireTime );
        activityPayload.userQuizSubmittedSegmentExpired = true;

        await services.RedisServices.redis("expire", `${key}:${redisKeys.submittedQuestions}`,expireTime );
        activityPayload.submittedQuestionsExpired = true;
        
        await services.RedisServices.redis("expire", `${key}:${redisKeys.submittedQuesScores}`,expireTime);
        activityPayload.submittedQuesScoresExpired = true;
       
        await services.RedisServices.redis("expire", redisKeys.getLoginTimeStampsList(currentlyProcessingQuizId, currentlyProcessingUserId), expireTime);
        activityPayload.loginStampListExpired = true;

        await services.RedisServices.redis("expire", redisKeys.getTabSwitchReportList(currentlyProcessingQuizId, currentlyProcessingUserId), expireTime);
        activityPayload.timeSwitchReportListExpired = true;

        await services.RedisServices.redis("expire", redisKeys.getfullScreenReportList(currentlyProcessingQuizId, currentlyProcessingUserId), expireTime);
        activityPayload.getfullScreenReportList = true;

        await services.RedisServices.redis('expire', redisKeys.getAiProctorReportList(currentlyProcessingQuizId, currentlyProcessingUserId), expireTime);
        activityPayload.aiProctorReportsListExpire = true

        await services.RedisServices.redis("srem", `${redisKeys.submittedUserIds}:${currentlyProcessingQuizId}`, currentlyProcessingUserId);
        activityPayload.userRemovedFromSubmittedUserIds = true;

        await services.RedisServices.redis("srem", `${redisKeys.joinedUserIds}:${currentlyProcessingQuizId}`, currentlyProcessingUserId);
        activityPayload.userRemovedFromJoinedUserIds = true;

        await services.RedisServices.redis('expire', libs.constants.redisKeys.getLiveStreamKey(currentlyProcessingUserId, currentlyProcessingQuizId), expireTime);
        
        await services.RedisServices.redis("del", redisKeys.getSegmentSubmissionHistory(currentlyProcessingQuizId, currentlyProcessingUserId));
        activityPayload.segmentSubmissionHistoryList = true;

        await services.RedisServices.redis('del', redisKeys.getTerminationList(currentlyProcessingQuizId, currentlyProcessingUserId));
        activityPayload.terminationDetailsList = true;

        global.currentlyProcessingUserId = null;
        global.isProcessingSubmission = false;
        // console.log( JSON.stringify(activityPayload))
        // libs.util.printValues(activityPayload);

    } catch (e) {
        global.isProcessingSubmission = false;
        console.log("Setting false ", global.isProcessingSubmission);
        console.log("payload = ", JSON.stringify(activityPayload) );
        console.log("error = ", e );
        // libs.util.printValues(activityPayload);
        let content = `Quiz Id = ${ global.currentlyProcessingQuizId } User Id = ${ global.currentlyProcessingUserId} \nError is ${ e.message } \n ${e.stack.toString()}`
        libs.util.sendEmail('<EMAIL>', 'Error while submission in quiz = ' + process.env.NODE_ENV, content, process.env.DEFAULT_EMAIL )
        libs.util.sendEmail('<EMAIL>', 'Error while submission in quiz =' + process.env.NODE_ENV , content, process.env.DEFAULT_EMAIL )
        global.currentlyProcessingQuizId = null;
        global.currentlyProcessingUserId = null;
        await services.RedisServices.redis("del", redisKeys.currentlyProcessingQuizId);
        await services.RedisServices.redis("set", 'stop', `${Date.now()}}_${ e.message}` );
        await services.RedisServices.redis("expire","stop", 24* 60 * 60 ); // one day expiry
        process.exit(1);
        console.log("Error while popping submission queue", e);
    }
    return;
}

const saveAttemptsFromRedis = async function (payload = {}) {

    let { quiz, userQuizSubmittedSegment, questId , activityPayload} = payload;
    activityPayload.attemptsInfo = {};
    if( !quiz ) throw new Error("Quiz not present");
    if( !userQuizSubmittedSegment ) throw new Error("userQuizSubmittedSegment not present");

    let currentlyProcessingQuizId = quiz._id.toString(), 
    currentlyProcessingUserId = userQuizSubmittedSegment.userId.toString();
    let questionIds = quiz.questionId || [], userAnswer, savedAttemptsData = {
        userId : currentlyProcessingUserId,
        quizId : currentlyProcessingQuizId
    };
    let ongoingQuizScoreRedisKey = redisKeys.ongoingQuiz + ":" + currentlyProcessingUserId + ":" + 
    currentlyProcessingQuizId + ":" + redisKeys.submittedQuesScores;
    if (quiz.randomizeQuestion || quiz.poolQuestion)
        questionIds = userQuizSubmittedSegment.questionId || [];
    let presentQuestions = { };
    for (let i = 0; i < questionIds.length; i += 1) {
        let questionId = questionIds[i].toString();
        presentQuestions[ questionId ] = 1 ;
        activityPayload.attemptsInfo[ questionId ] = {};
        let questionKey = libs.constants.redisKeys.questionIdString + ":" + questionId;
       
        if( questId && questionId.toString() != questId ) continue;
        
        if ( userQuizSubmittedSegment[questionKey]) {
            let questionData = JSON.parse(userQuizSubmittedSegment[questionKey]);
            activityPayload.attemptsInfo[ questionId ].questionData = questionData;

            //TODO remove unnecessary data from question data
            // delete questionData.userInputMCQ;
            // delete questionData.userInputSubjective;
            // delete questionData.userProgram;
            // delete questionData.userLanguage;
            // delete questionData.userOutputCoding;
            // delete questionData.css;
            // delete questionData.html;
            // delete questionData.js;
            let score = await services.RedisServices.redis('hget', ongoingQuizScoreRedisKey, questionId )
            activityPayload.attemptsInfo[ questionId ] .score = score;

            let scoreUpdationActivityList = await services.RedisServices.redis('lrange', redisKeys.getScoreUpdationActivityList(currentlyProcessingQuizId, currentlyProcessingUserId, questionId), 0, -1);
            if (scoreUpdationActivityList?.length) {
                let scoreUpdationActivityObjsArr = []
                scoreUpdationActivityList.map(scoreUpdationObj => {
                    const {originalScore, newScore, updatedBy, updatedAt} = JSON.parse(scoreUpdationObj) || {};
                    if (updatedBy) {
                        scoreUpdationActivityObjsArr.push({
                            originalScore: parseInt(originalScore) || 0,
                            newScore: parseInt(newScore) || 0,
                            updatedBy: mongoose.Types.ObjectId(updatedBy),
                            updatedAt: new Date(updatedAt),
                        })
                    }
                })
                if (scoreUpdationActivityObjsArr.length)  questionData.scoreUpdationActivity = scoreUpdationActivityObjsArr;
            }
            if( score ) {
                if( isNaN( parseInt( score ) ) ){
                    console.log("Invalid score ", currentlyProcessingQuizId, currentlyProcessingUserId, 
                    ongoingQuizScoreRedisKey, questionId, "score", score );
                    throw new Error("Invalid score present=" + score)
                } else {
                    questionData.score = parseInt( score );
                }
            }
            userQuizSubmittedSegment.quizSubmittedQuestion.push(questionData);
            delete userQuizSubmittedSegment[questionKey]
        }
        let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${currentlyProcessingQuizId}:${currentlyProcessingUserId}:${questionId}`;

        let questionAttempts = await services.RedisServices.redis2("lrange", userAttemptListByQuesRedisKey, 0, -1);
        activityPayload.attemptsInfo[ questionId ].questionAttempts = questionAttempts;
        savedAttemptsData[ questionId ] = { };
        if (!(questionAttempts && questionAttempts.length)) {
            questionAttempts = []
        } else {
            let queryObj = {
                'questionId': questionId, 'userId': currentlyProcessingUserId,
                "quizId": currentlyProcessingQuizId
            }
            userAnswer = await userAnswerController.getOneUserAnswer(
                queryObj, { _id: 1 }, {} );
            activityPayload.attemptsInfo[ questionId ].userAnswer = userAnswer;
            if (!userAnswer) {
                queryObj.finalSubmission = true;
                userAnswer = await userAnswerController.addUserAnswer(queryObj);
                userAnswer = userAnswer.toObject();
                activityPayload.attemptsInfo[ questionId ].userAnswerAdded = userAnswer
            }
        }
        let questionSaved = false;
        let attemptObjArray = [];
        console.log(`saving question ${questionId}, quizId ${currentlyProcessingQuizId}, userId ${currentlyProcessingUserId}`);
        savedAttemptsData[ questionId ] = { totalAttempts : questionAttempts.length };
        for (let k = 0; k < questionAttempts.length; k += 1) {
            let attemptObj = JSON.parse(questionAttempts[k]);
            if (attemptObj.submissionId) {
                if (attemptObj.userCompilationError != "404") {
                    for (let j = 0; j < attemptObjArray.length; j += 1) {
                        if (attemptObjArray[j].submissionId
                            && attemptObjArray[j].submissionId == attemptObj.submissionId
                            && attemptObjArray[j].userCompilationError == "404") {
                            attemptObj.timeOfCreation = attemptObjArray[j].timeOfCreation
                            attemptObjArray.splice(j, 1)
                            break;
                        }
                    }
                }
            }
            attemptObj = getAttemptObjForDB({ attempt: attemptObj });
            if (attemptObj) attemptObjArray.push(attemptObj);
        }
        activityPayload.attemptsInfo[ questionId ].attemptObjArray = attemptObjArray;
        if (attemptObjArray.length) {
            let resultAttempts = await userAttemptController.createAttemptInDbFromRedisData({ userAnswerId: userAnswer._id, attempts: attemptObjArray });
            await services.RedisServices.redis2('hincrby', redisKeys.pendingSubmissionsHash, currentlyProcessingQuizId, -attemptObjArray.length)
            await services.RedisServices.redis2("del", userAttemptListByQuesRedisKey);
            await sleep(500);
            questionSaved = true;
        } else {
            await services.RedisServices.redis2("del", userAttemptListByQuesRedisKey);
        }
    }

    for( let i = 0; i <( userQuizSubmittedSegment.submittedQuestions || [] ).length ; i += 1 ){
        let id = userQuizSubmittedSegment.submittedQuestions[i];
        if (!presentQuestions[id]) {
            let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${currentlyProcessingQuizId}:${currentlyProcessingUserId}:${id}`;
            await services.RedisServices.redis2("del", userAttemptListByQuesRedisKey);
        }
    }
    console.log(" savedAttemptsData", savedAttemptsData )
    return savedAttemptsData;
}

const getUserQuizSubmittedSegmentForSubmission = async function (payload = {}) {
    let { quizId, userId } = payload;

    if( !quizId ) throw new Error("Quiz id not present")
    if( !userId ) throw new Error("User id not present")

    let key = redisKeys.ongoingQuiz + ":" + userId + ":" + quizId;

    let userQuizSubmittedSegment = await services.RedisServices.redis('hgetall', key);
    delete userQuizSubmittedSegment.startTime;
    delete userQuizSubmittedSegment.extraTime
    if (userQuizSubmittedSegment && userQuizSubmittedSegment.userId ) {
        let submittedQuestionKeys = key + ":" + redisKeys.submittedQuestions;
        let submittedQuestions = await services.RedisServices.redis("smembers", submittedQuestionKeys)
        userQuizSubmittedSegment[redisKeys.submittedQuestions] = submittedQuestions || [];
        userQuizSubmittedSegment.quizSubmittedQuestion = [];
        userQuizSubmittedSegment[libs.constants.redisKeys.submittedSegments] = userQuizSubmittedSegment[libs.constants.redisKeys.submittedSegments] ? JSON.parse(userQuizSubmittedSegment[libs.constants.redisKeys.submittedSegments]) : [];

        // let startTime = parseInt(userQuizSubmittedSegment.startTime);
        userQuizSubmittedSegment.loginCount = userQuizSubmittedSegment.loginCount || 1;
        userQuizSubmittedSegment.currentIp = userQuizSubmittedSegment.currentIp || '';
        userQuizSubmittedSegment.tabSwitchCount = userQuizSubmittedSegment.tabSwitchCount || 0;
        userQuizSubmittedSegment.tabSwitchInCount = userQuizSubmittedSegment.tabSwitchInCount || 0;
        userQuizSubmittedSegment.forceLoginCount = userQuizSubmittedSegment.forceLoginCount || 0;
        userQuizSubmittedSegment.codePasteCount = userQuizSubmittedSegment.codePasteCount || 0;
        userQuizSubmittedSegment.camBlockCount = userQuizSubmittedSegment.camBlockCount || 0;
        userQuizSubmittedSegment.fullScreenInCount = userQuizSubmittedSegment.fullScreenInCount || 0;
        userQuizSubmittedSegment.fullScreenOutCount = userQuizSubmittedSegment.fullScreenOutCount || 0;
        userQuizSubmittedSegment.displayName = userQuizSubmittedSegment.displayName || "";
        userQuizSubmittedSegment.email = userQuizSubmittedSegment.email || "";
        userQuizSubmittedSegment.userSessions = JSON.parse(userQuizSubmittedSegment.userSessions || JSON.stringify([]) ) ;
     
        if( userQuizSubmittedSegment.quizUserDetails ){
            userQuizSubmittedSegment.quizUserDetails = userQuizSubmittedSegment.quizUserDetails ? JSON.parse(userQuizSubmittedSegment.quizUserDetails) : [];
        }
        if(userQuizSubmittedSegment.questionId){
          userQuizSubmittedSegment.questionId = JSON.parse(userQuizSubmittedSegment.questionId);
        }

        userQuizSubmittedSegment.loginDetails = [];
        const loginObjs = await services.RedisServices.redis('lrange', redisKeys.getLoginTimeStampsList(quizId, userId), 0, -1) || [];
        loginObjs. map(str => {
            const loginObj = libs.util.parseQuizLoginRedisString(str);
            let {ip, loginTime, platform} = loginObj;
            if (loginTime) {
                const obj = {loginTime: new Date(loginTime)};
                obj.platform = platform ?? '{}'
                if (1 || libs.util.isIpAddressValid(ip))     obj.ip = ip;
                if (platform) obj.platform = platform;
                userQuizSubmittedSegment.loginDetails.push(obj);
            }
            
        });

        userQuizSubmittedSegment.tabSwitchReport = [];
        const tabSwitchReport = await services.RedisServices.redis('lrange', redisKeys.getTabSwitchReportList(quizId, userId), 0, -1) || [];
        tabSwitchReport.map(str => {
            const tabSwitchReportObj = libs.util.parseTabSwitchReportRedisString(str);
            if (tabSwitchReportObj && tabSwitchReportObj.tabTime) {
                userQuizSubmittedSegment.tabSwitchReport.push(tabSwitchReportObj);
            }
        });

        userQuizSubmittedSegment.fullScreenReport = [];
        const fullScreenReport = await services.RedisServices.redis('lrange', redisKeys.getfullScreenReportList(quizId, userId), 0, -1) || [];
        fullScreenReport.map(str => {
            const fullScreenReportObj = libs.util.parseFullScreenReportRedisString(str);
            if (fullScreenReportObj && fullScreenReportObj.time) {
                userQuizSubmittedSegment.fullScreenReport.push(fullScreenReportObj);
            }
        });

        userQuizSubmittedSegment.aiProctoringReport = [];
        const aiProctorReports = await services.RedisServices.redis('lrange', redisKeys.getAiProctorReportList(quizId,userId), 0, -1) ?? [];
        aiProctorReports.forEach((obj) => {
            try {
                const parsedObj = JSON.parse(obj);
                if (parsedObj) {
                    userQuizSubmittedSegment.aiProctoringReport.push(parsedObj);
                }
            } catch (error) {
                console.log(error);
            }
        })

        userQuizSubmittedSegment.segmentSubmissionHistory = [];
        const segmentSubmissionHistory = await services.RedisServices.redis('lrange', redisKeys.getSegmentSubmissionHistory(quizId, userId), 0, -1) || [];
        segmentSubmissionHistory.forEach((str) => {
            const obj = libs.util.parseSegmentSubmissionHistoryRedisString(str);
            if (obj && obj.submissionType) {
                userQuizSubmittedSegment.segmentSubmissionHistory.push(obj);
            }
        });

        userQuizSubmittedSegment.terminationDetails = [];
        const terminationHistory  = await services.RedisServices.redis('lrange', redisKeys.getTerminationList(quizId, userId), 0, -1 ) || [];
        terminationHistory.forEach((str) => {
            const obj = libs.util.parseTerminationRedisString(str);
            if (obj && obj.terminationType) {
                userQuizSubmittedSegment.terminationDetails.push(obj);
            }
        });
    } else {
        return null;
    }
    return userQuizSubmittedSegment;
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
  
const getAttemptObjForDB = function( payload = {} ){
    let { attempt } = payload;
    let { questionType } = attempt;
    let obj = {
        _id : attempt._id || mongoose.Types.ObjectId(),
        finalSubmission : attempt.submissionId,
        timeOfCreation : attempt.time || attempt.timeOfCreation  || Date.now(),
    };
    if (attempt.codeId) {
        obj.codeId = attempt.codeId;
    }
    switch (questionType) {
        case libs.constants.questionTypeNumeric.questionTypeMCQ:
            obj.userInputMCQ = parseInt(attempt.inputMCQ.replace("chkOpt", ""), 10) - 1;
            return obj;
        case constants.questionTypeNumeric.questionTypeStepwise:
            obj.userOutputCoding = attempt.stepwiseUserInput || [];
            return obj;
        case constants.questionTypeNumeric.questionTypeSubjective:
            obj.userInputSubjective = attempt.userProgram || '';
            if ( attempt.files && attempt.files.length && attempt.files[0] ) {
                obj.files = attempt.files;
            }
            return obj;
        case constants.questionTypeNumeric.questionTypeWeb:
        case constants.questionTypeNumeric.questionTypeCoding:
            obj.userProgram = attempt.userProgram;
            obj.userLanguage = attempt.userLanguage;
            obj.testCasesPassed = attempt.testCasesPassed;
            obj.totalTestCase = attempt.totalTestCase;
            obj.totalScore = attempt.totalScore;
            obj.userOutputCoding = attempt.userOutputCoding;
            obj.userCompilationError = attempt.userCompilationError;
            obj.html = attempt.html;
            obj.css = attempt.css;
            obj.js = attempt.js;
            obj.submissionId = attempt.submissionId;
            return obj;
        default:
            break;
    }
    return null;
}

const getInterval = function( req, cb ){
    services.RedisServices.redis2('llen', redisKeys.submissionQueue, ( err ,len )=>{
        if( err ) {
            console.error("Error while getting length of submisssion queue", err )
            cb({error : "Error while getting length of submission queue"});
            return;
        }
        let { interval } = req.query;
        if (!interval) {
            cb({ interval: global.submissionInterval, length : len });
            return;
        }
        let result = startIntervals(interval);
        result.length = len;
        cb( result )
    })
}

const pushInQuizSubmissionQueue = async (quizId, callback) => {
    try {

        let isCB = callback && typeof callback === 'function';
        if (!(quizId )) {
            console.log("Error pushInQuizSubmissionQueue, quizId is not valid, ", quizId);
            if (callback) callback({ error: 'quizId  is not valid' });
            return;
        }
        global.dbSubmissionCounter += 1;
        let { timeSlices, maxTimeSlice, thresholdLoad, timeBasedCounters } = libs.constants.submissionQueueData
        if (global.dbSubmissionCounter >= thresholdLoad &&
            (!global.timeSinceLastCounterTimeUpdate ||
                Date.now() - global.timeSinceLastCounterTimeUpdate >= (timeSlices * maxTimeSlice))) {
            global.timeSinceLastCounterTimeUpdate = Date.now();
            console.log("Updating due to load", global.timeSinceLastCounterTimeUpdate)
            updateTimeBasedCounter(timeBasedCounters, global.dbSubmissionCounter)
        }
        services.RedisServices.redis2('hincrby', redisKeys.pendingSubmissionsHash, quizId, 1);
        return true;
    } catch (e) {
        console.log("Error while pushing in quiz queue", e)
    }
}

const popFromQuizSubmissionQueue = (callback) => {
    services.RedisServices.redis2('lpop', redisKeys.submissionQueue, (err, payload) => {
        if (err) {
            console.log("redis Error popFromQuizSubmissionQueue, ERror = ", err);
            return callback(err);
        }
        if ( ! payload )    return callback();
        try {
            payload = JSON.parse(payload);
            if ( ! payload.quizId ) {
                console.log("ERror popFromQuizSubmissionQueue, QuizId not present in payload, Payload = ", payload);
                return callback({error: "QuizId present in payload"});
            }
            services.RedisServices.redis2('hincrby', redisKeys.pendingSubmissionsHash, payload.quizId, -1, (err, val) => {
                if (err) {
                    console.log("redis Error popFromQuizSubmissionQueue, ERror = ", err);
                    return callback(err);
                }
                callback(err, payload);
                return ;
            })
        }
        catch(e) {
            console.log("JSON parse ERror popFromQuizSubmissionQueue, e = ",e);
            callback(e);
        }
    });
}
let updateTimeBasedCounter  = function( counters = libs.constants.submissionQueueData.timeBasedCounters, count = 1){
    if( count <= 0 ) return;
    global.dbSubmissionCounter = 0;
    counters.map( ( counter )=>{
        let now = Date.now();
        let precisionSlice = ( parseInt( now / counter ) * counter ) / 1000;
        let hash = `count:${counter}:hits`;
        console.log(" hash ", hash, " slice ", precisionSlice, " count ", count );
        services.RedisServices.redis2('hincrby', hash, precisionSlice, count ); 
    })
}

let modifySubmissionInterval = function( ){
    let now = Date.now(), counter = libs.constants.submissionQueueData.defaultSliceInterval;
    let maxTimeSlice = ( parseInt( now / counter ) * counter ) / 1000;
    let hmgetValues = [ maxTimeSlice];
    let hash = `count:${counter}:hits`;
    for( let i = 1; i < libs.constants.submissionQueueData.timeSlices ; i+=1 ){
        hmgetValues.push( maxTimeSlice - ( i * (counter/1000) ) )
    }
    services.RedisServices.redis2('hmget', hash, hmgetValues, ( err , values )=>{
        if( err ){
            console.error("Error while getting counter records ", err );
            startIntervals( libs.constants.submissionQueueData.maxSubmissionInterval );
            if ( global.submissionCounterIntervalId ) 
                clearInterval( global.submissionCounterIntervalId );
            return;
        }
        let  i = 0, str = "";
        console.log("================")
        for (i = 0; i < values.length; i += 1) {
           str +=`${maxTimeSlice - ( i * (counter/1000) ) } = ${values[i]}, `
        }
        console.log(`------>${str}<-----`)
        console.log("================")
        for( i = 0; i < values.length ; i += 1){
            let count = values[i];
	    count = count || 0;
            if (count >= libs.constants.submissionQueueData.thresholdLoad) {
                if (global.submissionInterval <= libs.constants.submissionQueueData.maxSubmissionInterval) {
                    console.log(" global.submission interval ", global.submissionInterval)
                    if (global.submissionInterval != libs.constants.submissionQueueData.maxSubmissionInterval) {
                        console.log(" decreasing submission saving speed to minimum", global.submissionInterval)
                        startIntervals(libs.constants.submissionQueueData.maxSubmissionInterval);
                    }
                }
                break;

            }
        }


        if( i == values.length ){
            //decrese interval gradually
            if ( global.submissionCounterIntervalId && 
                global.submissionInterval > libs.constants.submissionQueueData.minSubmissionInterval) {
                console.log("increasing submission speed, current interval ", global.submissionInterval );
                let newInterval = global.submissionInterval - 60000;
                newInterval = newInterval < libs.constants.submissionQueueData.minSubmissionInterval ? 1000 : newInterval
                console.log("new interval ", newInterval)
                startIntervals( newInterval );
            }
        }
    } ); 

}

let clearOldTimeSlices = function( ){
    let now = Date.now(), counter = libs.constants.submissionQueueData.defaultSliceInterval;
    let maxTimeSlice = ( parseInt( now / counter ) * counter ) / 1000;
    let minTimeSlice = ( maxTimeSlice - ( libs.constants.submissionQueueData.timeSlices  )* ( counter/1000 ) )
    let hmgetValues = [ maxTimeSlice];
    let hash = `count:${counter}:hits`;
    services.RedisServices.redis2('hkeys', hash, ( err , values )=>{
        if( err ){
            console.error("Error while getting keys representing time slices ", err );
            startIntervals( libs.constants.submissionQueueData.maxSubmissionInterval );
            if ( global.submissionCounterIntervalId ) 
                clearInterval( global.submissionCounterIntervalId );
            return;
        }
        console.log(" time slices ", values );
        let expiredTimeSlices = values.filter( ( timeSlice )=>{
            return timeSlice < minTimeSlice
        })
        if (expiredTimeSlices.length) {
            console.log(" removed time slices are ", expiredTimeSlices);
            services.RedisServices.redis2('hdel', hash, expiredTimeSlices, (errDel, values) => {
                if (errDel) {
                    console.error("Error while deleting expired time slices ", errDel);
                    startIntervals(libs.constants.submissionQueueData.maxSubmissionInterval);
                    if (global.submissionCounterIntervalId)
                        clearInterval(global.submissionCounterIntervalId);
                    return;
                }
            })
        }
    });
}

const startSalvationProcessForStrayAttempts = async function( req, res){
    try{
        let salvationSeekers = await (new Promise((resolve, reject) => {
             fs.readFile(path.join(  (path.dirname( __dirname ) ),'file.txt'), 'utf8' , ( err , data )=>{
                if (err) reject(err)
                resolve( data );
             });
        }) )
        console.log( salvationSeekers);
        salvationSeekers = salvationSeekers.split('\n');
        console.log( salvationSeekers);
        console.log("Total seekers ", salvationSeekers.length);
        let uniqueGroupWithSeekers = {};
        let quizObjs = {};
        for( let i = 0; i < salvationSeekers.length; i+=1){
            let key = salvationSeekers[i].trim().split(':');
            let [,userId, quizId, questionId ] = key;
            if (quizId && userId) {
                uniqueGroupWithSeekers[quizId] = uniqueGroupWithSeekers[quizId] || new Set();
                uniqueGroupWithSeekers[quizId].add(userId)
                if( questionId && questionId.trim() ){
                    uniqueGroupWithSeekers[`${quizId}:questions`] = uniqueGroupWithSeekers[`${quizId}:questions`] || {};
                    uniqueGroupWithSeekers[`${quizId}:questions`].questionIds = uniqueGroupWithSeekers[`${quizId}:questions`].questionIds || new Set();
                    uniqueGroupWithSeekers[`${quizId}:questions`].questionIds.add( questionId.trim());
                }
            } else if( !( quizId && userId ) ){
                console.log(" key ", key );
            }
        }

        let groups = Object.keys( uniqueGroupWithSeekers);
        console.log("Total salvation seeking group are", groups.length)
        for( let i = 0; i < groups.length; i +=1){
            let groupId = groups[i];
            if( groupId.indexOf(':') != -1 ) continue;
            if( groupId.indexOf('undefined') != -1 ) {
                let seekers = uniqueGroupWithSeekers[ groupId ];
                let salvationSeekers = Array.from( seekers );
                salvationSeekers.map( async (us) =>{
                    let isdeleted = await services.RedisServices.redis("del", `ongoingQuiz:${us}:${groupId}`);
                    let isdeletedScore = await services.RedisServices.redis("del", `ongoingQuiz:${us}:${groupId}:submittedQuestions`);
                    console.log(" deleted ", `ongoingQuiz:${us}:${groupId}`, isdeleted, isdeletedScore )
                })
                continue;
            }
            let quizObj = await services.QuizService.getOneQuiz({ _id : groupId }, { questionId : 1 , title : 1, quizTime : 1 }, {});
            if( !quizObj ) {
                console.log("Quiz not found for ", groupId );
                continue;
            }
            let { questionId } = quizObj;
            let attemptsQuesIds = ( uniqueGroupWithSeekers[`${groupId}:questions`] && uniqueGroupWithSeekers[`${groupId}:questions`].questionIds && Array.from( uniqueGroupWithSeekers[`${groupId}:questions`].questionIds ) ) || [];
            console.log(" attempts quesids ", attemptsQuesIds)
            if( !attemptsQuesIds.length ) continue;
            

            let seekers = uniqueGroupWithSeekers[ groupId ];
            let isAlreadyNearSalvation = await services.RedisServices.redis("sismember", redisKeys.activeQuizSet, groupId );
            
            if( 0 && isAlreadyNearSalvation )        continue;
           
            let salvationSeekers = Array.from( seekers );
            
            console.log("Total salvation seekers in group ", groupId, " are ", salvationSeekers.length )
            let addInSalvationList ;
            let notPresentQuestions = [];
            for( let w = 0; w < attemptsQuesIds.length; w+=1){
                let attemptId = attemptsQuesIds[w];
                let isFound = false;
                for( let t = 0; t < questionId.length; t += 1 ){
                    if( attemptId.toString() == questionId[t].toString() ){
                        isFound = true;
                        break;
                    }
                }
                if( !isFound ){
                    notPresentQuestions.push( attemptId );
                }
            }
            console.log(" attempts quesIds ", attemptsQuesIds );
            console.log(" not present ques ids ", notPresentQuestions );

            for (let j = 0; j < salvationSeekers.length; j += 1) {
                let seekerId = salvationSeekers[j];
                for( let w = 0 ; w < notPresentQuestions.length; w += 1){
                    let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${groupId}:${seekerId}:${notPresentQuestions[w]}`;
                    console.log(" removing attempts of \n ", userAttemptListByQuesRedisKey );
                    await services.RedisServices.redis2("del", userAttemptListByQuesRedisKey);
                }
                let isSeekerCompletedLifeTime = await services.RedisServices.redis("sismember", `${redisKeys.submittedUserIds}:${groupId}`,seekerId);

                if (isSeekerCompletedLifeTime) continue;

                let isSeekerCommitedSuicide = await services.RedisServices.redis("sismember", `${redisKeys.joinedUserIds}:${groupId}`, seekerId);

                if (0 && isSeekerCommitedSuicide) continue;

                await services.RedisServices.redis("sadd", `${redisKeys.joinedUserIds}:${groupId}`,seekerId );
                addInSalvationList = true;
            }

            if ( salvationSeekers.length) {
                console.log("Adding in salvation list", groupId)
                userController.updateQuizActiveInfo({ quizId : groupId, time : Date.now() - ( 300 * 60 * 1000 ) })
            }

        }
        res.json( { uniqueGroupWithSeekers})
    } catch(e){
        console.log("Error while achieving salvation -)",e );
        res.json({ error : e.message });
    }
}

const removeAttemptsFromRedis = async function( req, res ){
    try{
        let salvationSeekers = await (new Promise((resolve, reject) => {
            fs.readFile('./file.txt', 'utf8' , ( err , data )=>{
               if (err) reject(err)
               resolve( data );
            });
       }) )
       salvationSeekers = salvationSeekers.split('\n');
       console.log( salvationSeekers);
       console.log("Total seekers ", salvationSeekers.length);
       let uniqueGroupWithSeekers = {};
       for (let i = 0; i < salvationSeekers.length; i += 1) {
            let key = salvationSeekers[i].trim().split(':');
            let [, userId, quizId] = key;
            uniqueGroupWithSeekers[quizId] = uniqueGroupWithSeekers[quizId] || new Set();
            uniqueGroupWithSeekers[quizId].add(userId)
        }

        let groups = Object.keys( uniqueGroupWithSeekers);
        console.log("Total salvation seeking group are", groups.length)
        for (let i = 0; i < groups.length; i += 1) {
            let quizId = groups[i];
            let seekers = uniqueGroupWithSeekers[quizId];
            let projections = [quizSegments.key,
            quizContent.key, languagesAllowed.key, quizTime.key,
            copyPasteAllowed.key, revisitAllowed.key, title.key,
            tabSwitchAllowed.key, allowedIP.key, isWebCamAllowed.key,
            randomizeQuestion.key, poolQuestion.key, createdBy.key]

            let quizObj = await quizController.getQuizFromDb({ id: quizId, projections: {} }, {});
            if (!quizObj) throw new Error("Quiz Id is invalid" + quizId);
            let salvationSeekers = Array.from(seekers);

            console.log("Total salvation seekers in group ", quizId, " are ", salvationSeekers.length)
	     continue;
            let addInSalvationList;
            for (let j = 0; j < salvationSeekers.length; j += 1) {
                let userId = salvationSeekers[j]
                let key = redisKeys.ongoingQuiz + ":" + userId + ":" + quizId;
                let userQuizSubmittedSegment = await getUserQuizSubmittedSegmentForSubmission({ userId, quizId });

                if (!userQuizSubmittedSegment) {
                    console.log("User quiz submitted segment not present", quizId, userId);
                    throw new Error("User quiz submitted segment not present")
                };

                let quiz = quizObj;
                let currentlyProcessingUserId = userId, currentlyProcessingQuizId = quizId;
                let questionIds = quiz.questionId || [], userAnswer, savedAttemptsData = {
                    userId,
                    quizId
                };
                let ongoingQuizScoreRedisKey = redisKeys.ongoingQuiz + ":" + currentlyProcessingUserId + ":" +
                    currentlyProcessingQuizId + ":" + redisKeys.submittedQuesScores;

                for (let i = 0; i < questionIds.length; i += 1) {
                    let questionId = questionIds[i].toString();
                    let questionKey = libs.constants.redisKeys.questionIdString + ":" + questionId;


                    if (userQuizSubmittedSegment[questionKey]) {
                        let questionData = JSON.parse(userQuizSubmittedSegment[questionKey]);
                        let score = await services.RedisServices.redis('hget', ongoingQuizScoreRedisKey, questionId)
                        if (score) {
                            if (isNaN(parseInt(score))) {
                                console.log("Invalid score ", currentlyProcessingQuizId, currentlyProcessingUserId,
                                    ongoingQuizScoreRedisKey, questionId, "score", score)
                            } else {
                                questionData.score = parseInt(score);
                            }
                        }
                        userQuizSubmittedSegment.quizSubmittedQuestion.push(questionData);
                        delete userQuizSubmittedSegment[questionKey]
                    }
                    let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${currentlyProcessingQuizId}:${currentlyProcessingUserId}:${questionId}`;

                    let questionAttempts = await services.RedisServices.redis("lrange", userAttemptListByQuesRedisKey, 0, -1);

                    if (!(questionAttempts && questionAttempts.length)) {
                        continue;
                    }
                    let queryObj = { 
                        userId, quizId, questionId
                    }
                    let userAnswer = await userAnswerController.getOneUserAnswer(queryObj, { _id: 1 }, {});

                    let attempts = []
                    if (!userAnswer) {
                        queryObj.finalSubmission = true;
                        userAnswer = await userAnswerController.addUserAnswer(queryObj);
                        userAnswer = userAnswer.toObject();
                    } else {
                        let resultAttempts = await userAttemptController.getUserAttempt({ "userAnswerId": userAnswer._id }, { 'attemptData': 1 }, {});
                        resultAttempts.map(attemptObj => {
                            let att = attemptData.attemptData || []
                            att.map( obj => attempts.push(attempt) )
                        })
                    }
                    
                    savedAttemptsData[questionId] = {};

                    let questionSaved = false;
                    let attemptObjArray = [];
                    console.log(`saving question ${questionId}, quizId ${currentlyProcessingQuizId}, userId ${currentlyProcessingUserId}`);
                    savedAttemptsData[questionId] = { totalAttempts: questionAttempts.length };
                    for (let k = 0; k < questionAttempts.length; k += 1) {
                        attemptObj = getAttemptObjForDB({ attempt: attemptObj });
                        for( let r = 0 ; r < attempts.length; r += 1 ){
                            let pre = attempts[r];
                            if( +(new Date(pre.timeOfCreation )) == +(new Date( attemptObj.timeOfCreation ) ) ){
                                attemptObj = null
                                break;
                            }
                        }
                        if( !attemptObj ){
                            console.log("Attempt already present", quizId, userId, questionId);
                            continue;
                        }
                        let attemptObj = JSON.parse(questionAttempts[k]);
                        if (attemptObj.submissionId) {
                            if (attemptObj.userCompilationError != "404") {
                                for (let j = 0; j < attemptObjArray.length; j += 1) {
                                    if (attemptObjArray[j].submissionId
                                        && attemptObjArray[j].submissionId == attemptObj.submissionId
                                        && attemptObjArray[j].userCompilationError == "404") {
                                        attemptObj.timeOfCreation = attemptObjArray[j].timeOfCreation
                                        attemptObjArray.splice(j, 1)
                                        break;
                                    }
                                }
                            }
                        }
                        
                        if (attemptObj) attemptObjArray.push(attemptObj);
                    }
                    console.log(" saving atttempts in dn for ", quizId, userId, questionId );
                    console.log("no of atttempts are ", attemptObjArray.length );
                    if (attemptObjArray.length) {
                        if( attemptObjArray.length == attempts.length ){
                            console.log("Attempts already present");
                            continue;
                        }
                        let resultAttempts = await userAttemptController.createAttemptInDbFromRedisData({ userAnswerId: userAnswer._id, attempts: attemptObjArray });

                        await services.RedisServices.redis2('hincrby', redisKeys.pendingSubmissionsHash, currentlyProcessingQuizId, -attemptObjArray.length)
                        await services.RedisServices.redis("del", userAttemptListByQuesRedisKey);
                        await sleep(50);
                        questionSaved = true;
                    } else {
                        console.log("removing already added question attempts userAttemptListByQuesRedisKey ", userAttemptListByQuesRedisKey);
                        return;
                        await services.RedisServices.redis2('hincrby', redisKeys.pendingSubmissionsHash, currentlyProcessingQuizId, -attemptObjArray.length)
                        await services.RedisServices.redis("del", userAttemptListByQuesRedisKey);

                    }
                }

            }
        }
       res.json({ msg : "done"})
    } catch(e){
        console.log("Error while removing attempts from redis",e);
        res.json({ error : e.message })
    }
}

const getSubmissionCountsFromRedis = async function( req, res ){
    try{
        let { quizId, userId, questionId } = req.query;
        if( !quizId ) throw new Error("Quiz id not present");
        let key = `userQuesAttemptsList:${quizId}`;
        if( userId ){
            key += `:${userId}`
        }
        if( questionId ){
            key += `:${questionId}`
        }
        let result = await (new Promise( ( resolve, reject )=>{
            exec(`redis-cli -a hakunamaTATA922 keys \*`, ( err, stdout )=>{
                if( err )   {
                    console.log("Error while executing command", err );
                    reject(new Error("Error while executing command"));
                    return;
                }
                resolve(  stdout );
            })
        }) )
        result = result.split('\n');
        res.json({ result })
    } catch(e){
        console.log("Error while getting submission count from redis",e );
        res.json({ error : e.message})
    }
}
module.exports = {
    removeAttemptsFromRedis,
    startSalvationProcessForStrayAttempts,
    compileQuestion,
    startTimer,
    sendPostRequest,
    headers,
    options,
    getMCQResult,
    saveAttemptInDb,
    setOutputDataAfterCompilation,
    saveAttemptOfCacheResult,
    addSegmentToCompilationQueue,
    submitQuestion,
    compileIdeRequest,
    getMultipleAnswerResult,
    sendCompileRequestAfterTimeout,
    parseDataAfterCompilationNew,
    handleCompileResponse,
    startIntervals,
    getInterval,
    reevaluation,
    pushInQuizSubmissionQueue,
    saveSubmissionInRedis,
    getUserQuizSubmittedSegmentForSubmission,
    saveAttemptsFromRedis,
    getSubmissionCountsFromRedis,
};
