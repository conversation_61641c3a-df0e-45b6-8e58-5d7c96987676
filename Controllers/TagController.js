const services = require('../Services');
const libs = require('../Lib');
const logger = console
const mongoose = require('mongoose');

/**
 * 
 * @param {[{type: string, orgId: string, contentId: string, tags: [string]}]} payload 
 * @returns 
 */
var addTagForMultipleObj = async function (payload) {
    /** @type {Map<string, {questionId: [string], quizId: [string]}>} */
    const mapOfTagsToContentId = new Map();
    for (let index = 0; index < payload.length; ++index ) {
        const singleContent = payload[index];
        switch(singleContent.type) {
            case libs.constants.constantString.constantStringTagTypeQuestion: {
                if (singleContent.tags.length) {
                    singleContent.tags.forEach((singleTag) => {
                        singleTag = singleTag.trim()?.toLowerCase();
                        if (!singleTag) return;
                        const currentResult = mapOfTagsToContentId.get(singleTag) || {questionId: [], quizId: [], orgId: []};
                        currentResult.questionId.push(singleContent.contentId);
                        currentResult.orgId.push(singleContent.orgId);
                        mapOfTagsToContentId.set(singleTag, currentResult);
                    })
                }
                break;
            }
            case libs.constants.constantString.constantStringTagTypeQuiz: {
                if (singleContent.tags.length) {
                    singleContent.tags.forEach((singleTag) => {
                        singleTag = singleTag.trim()?.toLowerCase();
                        if (!singleTag) return;
                        const currentResult = mapOfTagsToContentId.get(singleTag) || {questionId: [], quizId: [], orgId: []};
                        currentResult.quizId.push(singleContent.contentId);
                        currentResult.orgId.push(singleContent.orgId);
                        mapOfTagsToContentId.set(singleTag, currentResult);
                    })
                }
                break;
            }
            default: {
                console.log("Something went wrong wrong type of tag is tried to be inserted silently ignoring", singleContent);
            }
        }
    }

    const bulkOperations = [];
    for ( let [tag, {quizId, questionId, orgId}] of mapOfTagsToContentId ) {
        bulkOperations.push({
            'updateOne': {
                filter: {
                    tag: tag,
                },
                update: {
                    $addToSet: {
                        adminId: {$each: orgId},
                        questionId: {$each: questionId},
                        quizId: {$each: quizId},
                    },
                    $setOnInsert: {
                        tag: tag,
                    }
                },
                upsert: true,
            }
        })
    }

    //TODO CHECK IF ordered: false is required or not, if not remove it.
    await services.TagService.bulkWrite(bulkOperations, {ordered: false});
    return;
}

var addTag = async function (tagType, obj, callback) {
    const isCB = callback && typeof callback === 'function';
    try{
        let tagTypeId = obj.tagTypeId;
        let tagTxt = obj.tags;
        let adminId = obj.adminId;
        if(!tagTxt){
            if(isCB)    return callback(null, 'There is no tag');
            return 'There is no tag';
        }
        var arrayTags = tagTxt.split(',');
        await addTagForMultipleObj(
            [
                {
                    type: tagType,
                    contentId: tagTypeId,
                    orgId: [adminId],
                    tags: arrayTags
                }
            ]
        );
        if(isCB)    callback(null);
        return;
    }
    catch(err){
        if(isCB)    return callback({error:err?.message ?? err});
        throw new Error(err?.message ?? err);
    }
};

var findTag = function (criteria, options, projections, callback) {
    return services.TagService.getTag(criteria, options, projections , callback);
}

var findUniqueTag = function (criteria, groupObj, callback) {
    var match = {$match: criteria};
    var groupField = {$group: groupObj}
    services.TagService.getDistinctTag(match, groupField, callback);
}

const getTagFindQueryByRole =  async (typeName, session) => {
    try{
        let adminIdArray = [];

        switch(session.role) {
            case libs.constants.roleNumberFromString.subAdmin:
                adminIdArray.push(session.userId);
            break;
            case libs.constants.roleNumberFromString.superOrg: {
                (session.orgIdsAssigned ?? []).forEach((element) => adminIdArray.push(element));
                adminIdArray.push(session.userId);
                break;
            }
            case libs.constants.roleNumberFromString.mentor:
            case libs.constants.roleNumberFromString.recruiter:
            case libs.constants.roleNumberFromString.contentCreator:
                adminIdArray.push(session.parentId);
            break;
            case libs.constants.roleNumberFromString.custom:
                adminIdArray.push(session.orgId);
        }

        adminIdArray = adminIdArray.map(id => mongoose.Types.ObjectId(id));
        let findQueryObj = {};
        if ( session.role != libs.constants.roleNumberFromString.admin ) findQueryObj = { adminId: { $in: adminIdArray } };
        if ( typeName )     findQueryObj[typeName] = {$exists: true, $ne: []};
            
        return findQueryObj;
    }
    catch(error){
        throw new Error(error?.message ?? error);
    }
}

const getTagsRoleWise = async (session, typeName, callback) => {
    try{
        let tagFindQueryObj = await getTagFindQueryByRole(typeName, session);
        let tags = await findTag(tagFindQueryObj, {'tag':1}, {} );
        if(callback) callback(null,tags);
        else return tags;
    }
    catch(error){
        if(callback) callback(error?.message ?? error, null);
        else throw new Error(error?.message ?? error);
    }
}

module.exports = {
    addTag: addTag,
    findTag: findTag,
    findUniqueTag: findUniqueTag,
    getTagFindQueryByRole: getTagFindQueryByRole,
    getTagsRoleWise: getTagsRoleWise,
    addTagForMultipleObj,
}
