const services = require('../Services');
const libs = require('../Lib');
const mongoose = require('mongoose');

const addExtraTime = async (userIds, quizId, {extraTime, expireTime, createdBy}) => {
    const bulkOperationArray = [];
    userIds.forEach((userId) => {
        bulkOperationArray.push({
            updateOne: {
                filter: {userId: userId, quizId: quizId},
                update: {
                    $push: {
                        extraTime: {
                            extraTime,
                            expireAt: expireTime,
                            createdBy: createdBy,
                            createdAt: new Date(),
                        },
                    },
                    $setOnInsert: {
                        userId: userId,
                        quizId: quizId,
                    }
                },
                upsert: true,
            }
        });
    })
    
    const result = await services.UserQuizSubmittedSegmentMetaDataService.bulkWrite(bulkOperationArray, {ordered: false});
    return result;
}

const getExpireTimeLogs = async (userId, quizId) => {
    const result = await services.UserQuizSubmittedSegmentMetaDataService.getOne(
        {
            userId: userId,
            quizId: quizId,
        }, {
            extraTime: 1,
        }
    )
    if (!result) {
        return {extraTime: []};
    }
    const setOfUserIdsToGet = new Set();
    (result?.extraTime ?? []).forEach((element) => {
        setOfUserIdsToGet.add(element.createdBy);
    })
    if (setOfUserIdsToGet.size) {
        const mapOfUsers = new Map();
        const users = await services.UserService.getUser({_id: Array.from(setOfUserIdsToGet)}, {displayname: 1}, {});
        users.forEach((user) => {
            mapOfUsers.set(user._id.toString(), user);
        })
        result.extraTime.forEach(element => {
            element.createdBy = mapOfUsers.get(element.createdBy.toString()) ?? {displayname: 'DELETED_USER'};
        })
    }
    return result;
}


module.exports = {
    addExtraTime,
    getExpireTimeLogs,
}