/* eslint-disable linebreak-style */

const axios = require('axios').default;

const services = require("../Services");
const mongoose = require('mongoose');
const util = require("../Lib/Util");
const detector = new(require('node-device-detector'));
const _ = require('lodash');
const logger = console
const mailLib = require("../Lib/MailLib");
const uploadFormatOfFile = require("../Lib/constants");
const questionController = require("../Controllers/QuestionController");
const userAnswerController = require("../Controllers/UserAnswerController");
const archivedUserAnswerController = require("../Controllers/Archived_Controller/UserAnswerController");
const userAttemptController = require("../Controllers/UserAttemptController");
const archivedUserAttemptController = require("../Controllers/Archived_Controller/UserAttemptController");
const archivedUserAttemptSnapshotController = require("../Controllers/Archived_Controller/UserAttemptSnapshotController");
const quizController = require('../Controllers/QuizController');
const userQuizSubmittedSegmentController = require("../Controllers/UserQuizSubmittedSegmentController");
const archivedUserQuizSubmittedSegmentController = require("../Controllers/Archived_Controller/UserQuizSubmittedSegmentController");
const badActivityController = require("../Controllers/BadAcitvityController");
const archivedErrorController = require("../Controllers/Archived_Controller/ErrorController");
const SessionController = require('../Controllers/SessionController');
const UserQuizSubmittedSegmentMetaDataController = require('../Controllers/UserQuizSubmittedSegmentMetaDataController');
const LiveStreamController = require('../Controllers/LiveStreamController');

const libs = require("../Lib");
const { constants, messages } = libs 
const { contentTypeString, redisKeys, questionTypeNumeric } = constants;

const getOneUser = async function (criteria, projections, options, callback) {
    return services.UserService.getOneUser(criteria, projections, options, callback);
};

const getUser = function (criteria, projections, options, callback) {
    return services.UserService.getUser(criteria, projections, options, callback);
};

const getUsersCount = function (criteria, callback) {
    return userService.getUsersCount(criteria, callback);
}

const updateOneUser = function (criteria, updateData, options, callback) {
    return services.UserService.updateUser(criteria, updateData, options, callback);
};

const updateUsers = function (criteria, updateData, options, callback) {
    return services.UserService.updateUsers(criteria, updateData, options, callback);
};

//! not implemented yet.
const aggregateUser = function (aggregationArray, callback) {
    return services.UserService.aggregate(aggregationArray, callback);
};


//TODO This function is required.
var addNewUser = async function (payload, session, {guestUser, hostname}) {
    if (guestUser) return services.UserService.createGuestUser(payload, session, hostname);
    return services.UserService.addNewUser(payload, session, hostname);
}

//! required by getDashBoardDataOfContainer(current stand remove)
var calculatedPercentage = function (completedQuestion, totalQuestion) {
    if (completedQuestion <= 0)
        return ``;
    let percentageCompletion = ( completedQuestion ) / ( totalQuestion );
    percentageCompletion *= 100;
    if (percentageCompletion < 1)
        percentageCompletion = Math.floor((( percentageCompletion) * 100 )) / 100;
    else
        percentageCompletion = Math.floor((( percentageCompletion )));
    return `${percentageCompletion}%`;
}

//TODO This function is required.
const getUserQuesAttemptsFromRedis = async function (payload, cb) {
    try {
        let { userId, quizId, questionId, session, userObj, container } = payload;
        if (!userId) throw new Error("UserId is null");
        if (!quizId) throw new Error("QuizId is null");
        if (!questionId) throw new Error("QuestionId is null");
        if (!session) throw new Error("Session is null");

        let { email } = userObj || {};
        if (!email) throw new Error("Email is null");

        if (session.role === libs.constants.roleNumberFromString.user && session.userId != userId) {
            console.log("getUserQuesAttemptsFromRedis trying to get attempt detail of some other user-", "email-", session.email);
            throw new Error("Not authorized to access attempt detail");
        }

        let quesObj = await questionController.getOneQuestion({ _id: questionId }, { title: 1, type: 1, questionTypeWeb: 1 }, {});
        if (!quesObj) throw new Error("QuesId is wrong");

        let quizObj = container || await quizController.getOneQuiz({ _id: quizId }, { title: 1 }, {});
        if (!quizObj) throw new Error("QuizId is wrong");

        let userAttemptsRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${quizId}:${userId}:${questionId}`;
        let attemptsListFromRedis = await services.RedisServices.redis2("lrange", userAttemptsRedisKey, 0, -1);

        payload.containerId = quizId;
        payload.isQuiz = true;
        payload.email = email;
        payload.container = quizObj;
        let obj = await getAttemptsOfQuestion(payload);

        // if( !obj )  throw new Error("Attempts not present")
        //console.log("Obj = ", obj);
        let redisSubmissionStartTimeStamp = -1;

        let redisAttemptData = (attemptsListFromRedis || []).map((attemptStr, index) => {
            let attemptObj = JSON.parse(attemptStr);
            if (redisSubmissionStartTimeStamp == -1) redisSubmissionStartTimeStamp = attemptObj.time;
            attemptObj.attemptBucketId = '0';
            attemptObj._id = attemptObj._id || index;
            attemptObj.timeOfCreation = new Date(attemptObj.time);
            attemptObj.fromRedis = true;
            if (quesObj.type != libs.constants.questionTypeNumeric.questionTypeCoding &&
                !(quesObj.type == constants.questionTypeNumeric.questionTypeWeb && quesObj.questionTypeWeb && quesObj.questionTypeWeb.isReactQuestion))
                attemptObj.finalSubmission = true;

            return attemptObj;
        })

        const isReactQuestion = Boolean(quesObj.type == questionTypeNumeric.questionTypeWeb && quesObj.questionTypeWeb && quesObj.questionTypeWeb.isReactQuestion);
        let dbAttemptData = (obj && obj.userAttempts) || [];
        if (redisSubmissionStartTimeStamp != -1) dbAttemptData = dbAttemptData.filter(attemptObj => {
            if ((quesObj.type == libs.constants.questionTypeNumeric.questionTypeCoding || isReactQuestion) && attemptObj.finalSubmission) return true;
            return attemptObj.timeOfCreation < redisSubmissionStartTimeStamp;
        });

        dbAttemptData.map(attemptObj => {
            if (Boolean(quesObj.type != questionTypeNumeric.questionTypeCoding && ! isReactQuestion))   attemptObj.finalSubmission = true;
        })

        let data = {
            attemptData: dbAttemptData.concat(redisAttemptData),
            questionId,
            title: quesObj.title,
            type: quesObj.type,
            containerTitle: quizObj.title,
        }
        const map404Attempts = new Map();
        const filteredAttempts = data.attemptData.filter(attempt => {
            if(attempt.userCompilationError === '404') {
                map404Attempts.set(attempt.submissionId, attempt);
                return false;
            }
            return true;
        });

        const actualAttempts = filteredAttempts.map(attempt => {
            if (map404Attempts.has(attempt.submissionId)) {
                const tempAttempt = map404Attempts.get(attempt.submissionId);
                map404Attempts.delete(attempt.submissionId);
                return {
                    ...attempt,
                    timeOfCreation: tempAttempt.timeOfCreation,
                };
            }
            return attempt;
        }).concat([...map404Attempts.values()]);
        data.attemptData = actualAttempts;
        if (cb) cb(null, { userAttempts: [data] });
        return { userAttempts: [data] };
    }
    catch (e) {
        console.log("getUserQuesAttemptsFromRedis, Error = ", e, payload);
        if (cb) cb(e);
        return { error: e.message };
    }
}

//TODO This function is required.
var getAttemptsOfQuestion = async function (payloadObj= {}, callback) {
    let isCB = callback && typeof callback === 'function';
    try {
        let { email, userId, container, questionId} = payloadObj;
        let sessionObj = payloadObj.session || payloadObj.req.session;
        if (!sessionObj) throw new Error("Session not present");
        if (!container) throw new Error("Container not present");
        if (!questionId) throw new Error("Question id not present");
        if (!email) throw new Error("Email not present");
        if (sessionObj.role === '1') {
            if (sessionObj.email !== email) {
                console.log("getAttemptsOfQuestion trying to get attempt detail of some other user-", "email-", email, "yourId-", sessionObj.email);
                throw new Error("Not authorized to access attempt detail");
            }
        }
        // let userId = sessionObj.userId;
        let findObj = {
            "userId": userId,
            "questionId": questionId
        };
        findObj.quizId = container._id;
        let isArchived = (payloadObj.isArchived === "true") || (payloadObj.req && payloadObj.req.query && payloadObj.req.query.isArchived === "true");
        let userAnswer = await userAnswerController.getOneUserAnswer(findObj, { updatedAt :1, questionId : 1 }, {});
        if( !userAnswer ) {
            if( isCB)   return callback( null );
            return null;
        }
        let userAttempts = await getAttemptsFromUserAnswerId({ userAnswer, container });
        if( isCB) return callback( null, attempts );
        let responseObj = {
            userAttempts,
            container,
            userAnswer
        }
        return responseObj;
    } catch (e) {
        console.log("Error while getting attempts of question", e, payloadObj);
        if (isCB) return callback(e.message);
        return null;
    }
}

//TODO This function is required.
var getAttemptsFromUserAnswerId = async function (payloadObj = {}, callback) {
    let isCB = callback && typeof callback === 'function'
    try {
        let { container, userAnswer } = payloadObj;
        let { questionId } = userAnswer;
        if (!container) throw new Error("Container not present");
        if (!questionId) throw new Error("Question not present");
        let questionObj = questionId;
        let containerContentObj = getContainerContentObject(container, questionObj._id);
        let resultAttempts = await userAttemptController.getUserAttempt({ "userAnswerId": userAnswer._id }, { 'attemptData': 1 }, {});
        let attemptsArray = [];
        if (!(resultAttempts && resultAttempts.length)) {
            //TODO if check for length of bucket is also required or not
            if (isCB) return callback(null, []);
            return [];
        }
        for (let i = 0; i < resultAttempts.length; i += 1) {
            let attempsCollection = resultAttempts[i];
            let attemptData = attempsCollection.attemptData || []
            for (let j = 0; j < attemptData.length; j += 1) {
                let attemptObj = attemptData[j];
		    attemptObj.attemptBucketId = attempsCollection && attempsCollection._id;
                attemptObj.testCasesPassed = 0;

                if (questionObj.type === '3' || questionObj.type === '4' || questionObj.type === '5') {
                    let counterTestCase = 0;
                    let testCasePassed = 0;
                    questionObj.questionTypeCoding.executionType = containerContentObj.executionType;

                    attemptObj.totalTestCase = questionObj.questionTypeCoding.testCase.length;
                    
                    if (attemptObj.userOutputCoding && questionObj.questionTypeCoding.testCase.length) {
                        questionObj.questionTypeCoding.testCase.forEach((testCaseObj) => {
                            if (!attemptObj.userCompilationError &&
                                //testCaseObj.codeprogexpectedoutput &&
                                attemptObj.userOutputCoding[counterTestCase] &&
                                attemptObj.userOutputCoding[counterTestCase].trim() === testCaseObj.codeprogexpectedoutput.trim())
                                testCasePassed += 1;
                            counterTestCase += 1;
                        });
                        attemptObj.testCasesPassed = testCasePassed;
                    } else if (attemptObj.userCompilationError && attemptObj.userCompilationError.length) {
                        attemptObj.compilationError = attemptObj.userCompilationError;
                    }
                    if (questionObj.questionTypeCoding.executionType === libs.constants.codingQuestionExecutionType.onlyCompile) {
                        attemptObj.compileOnly = true;
                    }
                }
                else if (questionObj.type === '1') {
                    if (attemptObj.userInputMCQ && questionObj.questionTypeMCQ.correctAnswers.indexOf(parseInt(attemptObj.userInputMCQ, 10)) != -1) {
                        questionObj.isMCQCorrect = true;
                    } else {
                        questionObj.isMCQCorrect = false;
                    }
                }
                attemptsArray.push( attemptObj);
            }
        }
        return attemptsArray
    } catch (e) {
        console.log("Error while getting attempts from useranswer id", payloadObj, e);
        if (isCB) return callback(e.message);
        return null;
    }
}

//TODO this function is required
var getContainerContentObject = function (container, contentId) {
    let contentObj = null;
    let containerContent = container.quizContent;
    if (!( container && containerContent && contentId )) {
        logger.debug("invalid arguments getcontainerContentObject container-", container, " contentid-", contentId);
        logger.error("invalid arguments getcontainerContentObject");
        return null;
    }
    for (let i = 0; i < containerContent.length; i += 1) {
        contentObj = containerContent[i];
        if (contentObj.id.toString() == (contentId.toString())) {
            return contentObj;
        }
    }
}


var getQuestionForQuiz = async (quiz) => {
    // let quizId = quiz._id;
    let findObj = {'_id': {$in: quiz.questionId|| [] } };
    const questions = await questionController.getQuestion(findObj, {
        '_id': 1,
        'title': 1,
        'type': 1,
        'score': 1,
        'createdBy': 1,
        'orgId': 1,
    }, {});
    let questionsArray = [];
    if (!( questions instanceof Array ))
        questionsArray.push(questions);
    else
        questionsArray = questions;
    return  {quizContentArray: ReOrderContainerContentFromContentArrays( { containerObj: quiz, tutorialsArray: [], questionsArray })};
   
}

//! Depricated will be changed to getQuestionForQuiz later
var getQuestionsAndTutorialsFromCourseOrQuiz = function (containerObj, callback) {
    let containerId = containerObj._id;
    let findObj = {'_id': {$in: containerObj.questionId|| [] } };
    let findObjQuiz = {'_id': {$in: containerObj.quizId || [] } };
    questionController.getQuestion(findObj, {
        '_id': 1,
        'title': 1,
        'type': 1,
        'score': 1
    }, {}, (errorQues, questions) => {
        if (errorQues) {
            logger.debug(`getQuestionsAndTutorialsFromCourseOrQuiz Error getting Question  for  ${containerId}`, errorQues);
            logger.error(`getQuestionsAndTutorialsFromCourseOrQuiz Error getting Question`);
            callback({'error': `Error getting Question for ${containerId}`});
            return;
        } else {
            let questionsArray = [];
            if (!( questions instanceof Array ))
                questionsArray.push(questions);
            else
                questionsArray = questions;
            let tutorialsArray = [];
            if( containerObj.isQuiz ){
                let containerContentArray =  ReOrderContainerContentFromContentArrays( { containerObj, tutorialsArray, questionsArray });
                callback(null, {'quizContentArray': containerContentArray} );
                return;
            } else {
                quizController.getQuiz(findObjQuiz,  {_id: 1, title: 1}, {},  (errorQuiz, quiz) => {
                    if (errorQuiz) {
                        logger.error(`getQuestionsAndTutorialsFromCourseOrQuiz Error getting quiz for  ${containerId}`, errorQuiz);
                        logger.error(`getQuestionsAndTutorialsFromCourseOrQuiz Error getting quiz`);
                        callback({'error': `Error getting quiz for ${containerId}`});
                        return;
                    } else {
                        let quizArray = [];
                        if (!( quiz instanceof Array ))
                            quizArray.push( quiz );
                        else
                            quizArray = quiz;
                            callback(null, { 'courseContentArray': containerContentArray });
                        
                    }
                });
            }
        }
    });
};

//! need to  refactor it after getQuestionsAndTutorialsFromCourseOrQuiz is deleted
var ReOrderContainerContentFromContentArrays = function( payload ){
    let { containerObj, tutorialsArray, questionsArray, quizArray, projectArray } = payload
    let containerContentArray = [];
    let contentArray = containerObj.isQuiz ? containerObj.quizContent : containerObj.courseContent;
    contentArray = contentArray ? contentArray : [];
    contentArray.forEach((tempObj, j ) => {
        if (tempObj.contentType === libs.constants.contentTypeString.contentTypeTutorial) {
            for (let i = 0; i < tutorialsArray.length; i += 1) {
                let tutorialobj = tutorialsArray[i];
                if (( tutorialobj._id).toString() == tempObj.id.toString()) {
                    question = 1;
                    tutorialobj.type = 'tutorial';
                    tutorialobj.score = contentArray[j].score || tutorialobj.score;
                    containerContentArray.push(tutorialobj);
                    tutorialsArray.splice(i, 1);
                    break;
                }
            }
        } else if (tempObj.contentType === contentTypeString.contentTypeQuiz ) {
            for (let i = 0; i < quizArray.length; i += 1) {
                let quizobj = quizArray[i];
                if (( quizobj._id).toString() == tempObj.id.toString()) {
                    quizobj.type = contentTypeString.contentTypeQuiz;
                    quizobj.threshold = contentArray[j].threshold 
                    containerContentArray.push(quizobj);
                    quizArray.splice(i, 1);
                    break;
                }
            }
        } else if (tempObj.contentType === contentTypeString.contentTypeProject ) {
            for (let i = 0; i < projectArray.length; i += 1) {
                let projectobj = projectArray[i];
                if (( projectobj._id).toString() == tempObj.id.toString()) {
                    projectobj.type = contentTypeString.contentTypeProject;
                    projectobj.link = projectobj.name ? '/project/getProject/?project=' + (projectobj.name ) + "&userId=" + (projectobj.userId) : ""
                    containerContentArray.push(projectobj);
                    projectArray.splice(i, 1);
                    break;
                }
            }
        }
        else {
            for (let i = 0; i < questionsArray.length; i += 1) {
                let questionobj = questionsArray[i];
                if (( questionobj._id).toString() == tempObj.id.toString()) {
                    questionobj.qtype = questionobj.type;
                    questionobj.type = 'question';
                    if (tempObj.executionType) {
                        questionobj.executionType = tempObj.executionType;
                        questionobj.showHead = tempObj.showHead;
                        questionobj.showTail = tempObj.showTail;
                        questionobj.showCustomInput = tempObj.showCustomInput;
                    }
                    if (tempObj.files) {
                        questionobj.files = tempObj.files
                    }
                    questionobj.score = contentArray[j].score || questionobj.score;
                    containerContentArray.push(questionobj);
                    questionsArray.splice(i, 1);
                    break;
                }
            }
        }
    });
    return containerContentArray;
}

//! refactor or remove after refactoring getContainerSegmentDataForDashboard
var getCourseContentForSegment = function (courseId, segmentCount, callback) {
    let projectionObj = {
        'courseSegments': 1,
        'courseContent': 1,
        'attemptInSequence': 1,
        'startTime': 1,
        'endTime': 1,
        'courseTime': 1,
        'archaic': 1,
    }
    courseController.getOneCourse({'_id': courseId}, projectionObj, {}, (errCourse, resultCourse) => {
        if (errCourse) {
            logger.debug(`getCourseContentForSegment Error getting Course id  ${courseId}`, errCourse);
            logger.error(`getCourseContentForSegment Error getting Course id`);
            callback({'error': "error in getting Course"});
            return;
        } else if (!resultCourse) {
            logger.debug("getCourseContentForSegment Course not found--", courseId);
            logger.error("getCourseContentForSegment Course not found");
            callback({'error': "Course not found"});
            return;
        } else {
            getContainerSegmentData(resultCourse, segmentCount, false, callback)
        }
    });
};

//! refactor or remove after refactoring getContainerSegmentDataForDashboard
var getQuizContentForSegment = async (quizId, segmentCount, callback) => {
    const isCB = callback && typeof callback === 'function';
    try{
        let projectionObj = {
            'quizSegments': 1, 'quizContent': 1, 'attemptInSequence': 1, 'revisitAllowed': 1, 'title': 1,
            'quizContent': 1, 'startTime': 1, 'endTime': 1, 'displaystatus': 1, 'quizSegments': 1, 'revisitAllowed': 1,
            'attemptInSequence': 1, 'languagesAllowed': 1, 'quizTime': 1
        };
        const resultQuiz = await quizController.getOneQuiz({'_id': quizId}, projectionObj, {});
        if (!resultQuiz) {
            throw new Error(`quizid is invalid ${quizId}`);
        }
        return getContainerSegmentData(resultQuiz, segmentCount, true, callback);
    } catch (error) {
        console.trace(error);
        if(isCB) return callback(error?.message);
        throw new Error(error?.message);
    }
};

//TODO this function is required
var getContainerSegmentData = async function (container, segmentCount, isQuiz, callback) {
    let isCB = callback && typeof callback === 'function';
    let containerSegments = isQuiz ? container.quizSegments : container.courseSegments;
    let containerContent = isQuiz ? container.quizContent : container.courseContent;

    let segmentObj = {};
    let segmentStart = 0, segmentEnd = 0;

    if (!( containerSegments && containerSegments.length) || ( segmentCount < 0 )) {
        segmentObj.startIndex = 1;
        segmentObj.endIndex = containerContent.length;
        segmentObj.count = containerContent.length;
        segmentEnd = containerContent.length;
    } else {
        if (( containerSegments.length != 0 && containerSegments.length <= segmentCount)) {
            console.log("getContainerSegmentData invalid segmentcount-", segmentCount, " contentLength--", containerSegments.length)
            throw new Error( "invalid segment count");
        }
        for(let i = 0; i <= segmentCount ; i ++ ){
            segmentObj = containerSegments[i];
            segmentStart = segmentEnd;
            segmentEnd += ( segmentObj &&  !isNaN( segmentObj.count ) ? segmentObj.count : 0 );

        }
        segmentObj = containerSegments[segmentCount];
        if (segmentCount == ( containerSegments.length - 1 )){
            segmentObj.endIndex = containerContent.length;
            segmentObj.count = ( containerContent.length - segmentStart) ;
            segmentEnd = Math.min( segmentStart + segmentObj.count, segmentEnd);
        }
    }
    let containerContentArray = [], questionIdArray = [], tutorialIdArray = [], quizIdArray = [], projectIdArray = [];
    for (let i = segmentStart; (i < segmentEnd && i < containerContent.length ); i += 1) {
        if( containerContent[i].contentType <= contentTypeString.contentTypeStepwise || 
            containerContent[i].contentType == contentTypeString.contentTypeWeb ){
            questionIdArray.push( containerContent[i].id )
        } else if( containerContent[i].contentType == contentTypeString.contentTypeTutorial ){
            tutorialIdArray.push( containerContent[i].id )
        } else if( containerContent[i].contentType == contentTypeString.contentTypeQuiz ){
            quizIdArray.push( containerContent[i].id )
        } else if( containerContent[i].contentType == contentTypeString.contentTypeProject ){
            projectIdArray.push( containerContent[i].id )
        }
        containerContentArray.push(containerContent[i].id);
    }

    try {
        let questions, tutorials, quiz, project;
        if (questionIdArray.length) {
            questions = await questionController.getQuestion( {'_id': {$in: questionIdArray }}, 
            { 'title': 1, 'type': 1,'score': 1 }, {} )
        }
        if (tutorialIdArray.length) {
            tutorials = []//await tutorialController.getTutorial( {'_id': {$in: tutorialIdArray}}, {'title': 1}, {} )
        }
        if (quizIdArray.length) {
            quiz = await quizController.getQuiz( {'_id': {$in: quizIdArray }}, { 'title': 1 }, {} )
        }
        if (projectIdArray.length) {
            project = await projectController.getProject( {'_id': {$in: projectIdArray }}, { 'name': 1 }, {} )
        }
        let payload = { questions, tutorials, quiz, project  }
        let reOrderedContentArray = getQuizContentInSequence(container, segmentStart, segmentStart + segmentObj.count, payload );
        let key = isQuiz ? libs.constants.redisKeys.quizIdString : libs.constants.redisKeys.courseIdString
        key += ':' + container._id.toString();
        let property = libs.constants.redisKeys.segmentCountString + ':' + segmentCount;
        let cachedObj = {
            'reOrderedContentArray': reOrderedContentArray,
             ...segmentObj ,
            'attemptInSequence': container.attemptInSequence,
            'isArchived': container.archaic
        };
        services.RedisServices.redis("hset", key, property, JSON.stringify(cachedObj));

        if (isQuiz) {
            let endTime = new Date(container.endTime);
            let currentTime = Date.now();
            let quizTimeInSec = container.quizTime * 60;
        } else {
            services.RedisServices.redis("expire", key, 60 * 6 * 1000);
        }

        let callbackObj = {
            'tutorials': tutorials || [],
            'questions': questions || [],
            'quiz' : quiz || [],
            'reOrderedContentArray': reOrderedContentArray,
            'attemptInSequence': container.attemptInSequence,
            'segmentCount': segmentCount,
            'isArchived': container.archaic,
            'container' : container
        };
        callbackObj[isQuiz ? libs.constants.redisKeys.quizIdString : libs.constants.redisKeys.courseIdString] = container._id;
        if( isCB) callback(null, callbackObj);
        return callbackObj

    } catch (e) {
        console.log(" Error while getting course content ", e );
        if(isCB) return callback(e?.message);
        throw e;
    }
}

//! refactor after getContainerSegmentDataForDashboard
var getContainerSegmentDataFromContentArray = function (sessionObj, isOnlyTitleRequired, isQuiz, callback) {
    return function (error, result) {
        try {
            if (error) {
                callback(error);
                return;
            }
            let reOrderedContentArray = result.reOrderedContentArray;
            let segmentCount = result.segmentCount;
            let containerId = isQuiz ? result.quizId : result.courseId;
            let skipFirst = false;
            if (result.attemptInSequence && segmentCount != 0){
                skipFirst = true;  
                // we are using to skip first content in reorderedContent Object because we save the last id of previous segment in that orderedContent,
                // so that we can take decision wheter to open first item of the the requested segment.
            }

            result.attemptInSequence = sessionObj.role === libs.constants.roleNumberFromString.user ?
                result.attemptInSequence : false

            if (isOnlyTitleRequired) {
                callback(error, {'titles': reOrderedContentArray, 'skipFirst': skipFirst});
                return;
            }
            let containerContentIdsArray = [], segmentData = result, attemptInSequence = result.attemptInSequence;
            
            for (let i = 0; i < reOrderedContentArray.length; i += 1) {
                //containerContentIdsArray.push(mongoose.Types.ObjectId((reOrderedContentArray[i]._id )));
                containerContentIdsArray.push( (reOrderedContentArray[i]._id.toString() ));
            }
            if (attemptInSequence) {
                if (segmentData.triggerId) {
                    // if we have a trigger id then the checkpoint must be changed to that id.
                    let _id =  mongoose.Types.ObjectId((segmentData.triggerId)) 
                    containerContentIdsArray[0] = _id;
                    reOrderedContentArray[0] = { _id };
                } else if ( segmentCount != 0 ){
                    //if we dont have a trigger id it means it does not require any checkpoint to check before we open first item of that segment
                    // so we are removing the first element of the reOrderedContent which is the last element of previous segment.
                    skipFirst = false;
                    containerContentIdsArray.splice( 0 , 1 );
                    reOrderedContentArray.splice(0, 1 )
                }
            }
            let containerObj = isQuiz ? {"quizId": mongoose.Types.ObjectId(containerId)} : {"courseId": mongoose.Types.ObjectId(containerId)}
            let queryObj = {
                $and: [
                    {"userId": mongoose.Types.ObjectId(sessionObj.userId)}, containerObj
                ]
            };
            let statusProperty = 'completed';
            if (isQuiz) {
                statusProperty = 'finalSubmission'
                queryObj.$and.push({[statusProperty]: true});
            }

            queryObj.$and.push(
                {
                    $or:
                        [
                            {
                                $and: [
                                    {"tutorialId": {$exists: true}},
                                    {"tutorialId": {$in: containerContentIdsArray}}
                                ]
                            },
                            {
                                $and: [
                                    {"questionId": {$exists: true}},
                                    {"questionId": {$in: containerContentIdsArray}}
                                ]
                            },
                            {
                                $and: [
                                    {"courseQuizId": {$exists: true}},
                                    {"courseQuizId": {$in: containerContentIdsArray}}
                                ]
                            },
                            {
                                $and: [
                                    {"projectId": {$exists: true}},
                                    {"projectId": {$in: containerContentIdsArray}}
                                ]
                            }
                        ]
                }
            );
            if (result.isArchived) var aggregationFuction = archivedUserAnswerController.performAggregation;
            else var aggregationFuction = userAnswerController.performAggregation;

            aggregationFuction([{$match: queryObj}], (errorUserAnswer, userAnswer) => {
                let deleteId = false;
                if (errorUserAnswer) {
                    logger.debug("getContainerSegmentDataFromContentArray error in finding userAnswer  with id ", errorUserAnswer);
                    logger.error("getContainerSegmentDataFromContentArray error in finding userAnswer");
                    callback(errorUserAnswer);
                }
                else {
                  if (!( userAnswer && userAnswer.length )) {
                    userAnswer = [];
                    if (result.attemptInSequence) {
                      //this is for such segments which dont have any triggerid means they are the first segments either the skip allowed or first
                      // attemptInSequence segment follwed by one or many skipAllowed segments in begining.
                      if (!segmentData.triggerId) {
                        if( segmentData.skipAllowed )
                          result.attemptInSequence = false;
                      }
                      else if (segmentCount != 0) {
                        deleteId = true;
                      }
                    }
                  } else if( attemptInSequence && segmentData.skipAllowed ){
                    //for such a segment which has skipAllowed = true, so in  case its trigger question has been completed successfully,
                    // then whole segment can be attempted.
                    if (!segmentData.triggerId) {
                      result.attemptInSequence = false;
                    } else {
                      let reorderedObj = reOrderedContentArray[0];
                      for (let k = 0; k < userAnswer.length; k += 1) {
                        let userAnswerObj = userAnswer[k];
                        let objectId = userAnswerObj.tutorialId || userAnswerObj.questionId || userAnswerObj.courseQuizId || userAnswerObj.projectId;
                        if ( reorderedObj._id.toString() === (objectId).toString() && userAnswerObj.completed ){
                          result.attemptInSequence = false;
                          break;
                        }
                      }
                    }
                  }
                    let tempResultArray = [];
                    reOrderedContentArray.forEach((reorderedObj) => {
                        let tempObj = {};
                        let k;
                        let spliceIndex = -1;
                        for (k = 0; k < userAnswer.length; k += 1) {
                            let userAnswerObj = userAnswer[k];
                            let objectId =  userAnswerObj.tutorialId || userAnswerObj.questionId || userAnswerObj.courseQuizId || userAnswerObj.projectId;
                            if (reorderedObj._id.toString() === (objectId).toString()) {
                                tempObj.userAnswer = userAnswerObj;
                                spliceIndex = k;
                                break;
                            }
                        }
                        tempObj.type = reorderedObj.type
                        if ( deleteId || ( result.attemptInSequence &&
                                ( spliceIndex === -1 || ( !userAnswer[spliceIndex][statusProperty] ) ) )) {
                            if (deleteId) {
                                // delete reorderedObj._id;
                            }
                            else {
                                tempObj._id = reorderedObj._id;
                                deleteId = true;
                            }
                        } else {
                            tempObj._id = reorderedObj._id;
                        }
                        if (spliceIndex !== -1) {
                            userAnswer.splice(k, 1);
                        }

                        if (skipFirst)
                            skipFirst = false;
                        else
                            tempResultArray.push(tempObj);
                    });
                    callback(errorUserAnswer, {
                        'result': tempResultArray,
                        'userId': libs.util.encrypt(sessionObj.email),
                        'isArchived': result.isArchived
                    });
                }
            });
        }
        catch
            (exception) {
            logger.debug("getContainerSegmentDataFromContentArray exception caught", exception);
            logger.error("getContainerSegmentDataFromContentArray exception caught");
            callback(exception);
            return;
        }
    }
};


//TODO this function is required
var getQuizDashboard = async function (id, callback) {
    const isCB = callback && typeof callback === 'function';
    try {
        const quizObj = await quizController.getOneQuiz({'_id': id, displaystatus: 1}, {}, {});
        if (!quizObj) {
            throw new Error(`Error gettig quiz id ${id}`);
        }
        quizObj.isQuiz = true;
        const dataContent = await getQuestionForQuiz(quizObj);
        dataContent.quizObj = quizObj;
        if (isCB) return callback(null, dataContent);
        return dataContent;
    } catch (error) {
        if (isCB) return callback(error?.message ?? error);
        throw new Error(error?.message ?? error);
    }
};


// TODO function is required
//get quiz list of user
var quizOfUser = async function (req, res) {
    const resultQuiz = [];
    if (true || req.query.fromRedis == '1') {
        try {
            const activeQuizzes = await services.RedisServices.redis('smembers', libs.constants.redisKeys.activeQuizSet);
            const redisPipeline = services.RedisServices.getRedisInstance('redis').pipeline();
            let quizzesFromRedis = [];
            if (activeQuizzes.length) {
                for (let index = 0; index < activeQuizzes.length; index++) {
                    redisPipeline.sismember(`${libs.constants.redisKeys.submittedUserIds}:${activeQuizzes[index]}`, req.session.userId)
                }
                const redisResult = await redisPipeline.exec();
                redisResult.forEach((result, index) => {
                    if (result?.[1]) {
                        quizzesFromRedis.push(activeQuizzes[index]);
                    }
                });
            }
            if (quizzesFromRedis.length) {
                const quizzesWhereShowResultIsActive = await services.QuizService.getQuiz({ _id: quizzesFromRedis, showResults: true, displaystatus: libs.constants.displayStatus.active }, {
                    description: 1,
                    title: 1,
                    endTime: 1,
                    userId: 1,
                    _id: 1,
                }, {});
                const quizSubmissionObj = {};
                const redisPipelineForOnGoingQuiz = services.RedisServices.getRedisInstance('redis').pipeline();
                quizzesWhereShowResultIsActive.forEach((quiz) => {
                    let redisHashKeyForOngoingQuiz = libs.constants.redisKeys.ongoingQuiz + ':' + req.session.userId + ':' + quiz._id.toString();
                    let redisHashKeyForSubmittedQuesScores = redisHashKeyForOngoingQuiz + ':' + libs.constants.redisKeys.submittedQuesScores;
                    redisPipelineForOnGoingQuiz.hgetall(redisHashKeyForOngoingQuiz);
                    redisPipelineForOnGoingQuiz.hgetall(redisHashKeyForSubmittedQuesScores);
                });
                const onGoingSegmentResult = await redisPipelineForOnGoingQuiz.exec();
                for (let index = 0; index < (onGoingSegmentResult.length - 1); index+=2) {
                    const onGoingQuiz = onGoingSegmentResult[index][1];
                    const submittedQuestion = onGoingSegmentResult[index + 1]?.[1];
                    const quizId =  quizzesWhereShowResultIsActive[index / 2]._id.toString()
                    quizSubmissionObj[quizId] = {
                        userSessions: [],
                        quizSubmittedQuestion: Object.keys(submittedQuestion ?? []).map(key => ({ questionId: key, score: submittedQuestion[key] })),
                    }
                    if (onGoingQuiz.userSessions) {
                        try {
                            const userSession = JSON.parse(onGoingQuiz.userSessions);
                            onGoingQuiz.startTime = userSession[0].startTime ?? onGoingQuiz.startTime;
                            quizSubmissionObj[quizId].userSessions = userSession;
                        } catch (error) { }

                    }
                    if (onGoingQuiz.startTime) {
                        if (typeof onGoingQuiz.startTime === 'string' || typeof onGoingQuiz.startTime === 'number') {
                            const intoNumber = parseInt(onGoingQuiz.startTime);
                            quizSubmissionObj[quizId].startTime = new Date(isNaN(intoNumber)?onGoingQuiz.startTime:intoNumber);
                        }
                    }
                    if (onGoingQuiz.endTime) {
                        if (typeof onGoingQuiz.endTime === 'string' || typeof onGoingQuiz.endTime === 'number') {
                            const intoNumber = parseInt(onGoingQuiz.endTime);
                            quizSubmissionObj[quizId].endTime = new Date(isNaN(intoNumber)?onGoingQuiz.endTime:intoNumber);
                        }
                    }
                }
                quizzesWhereShowResultIsActive.forEach((quiz) => {
                    resultQuiz.push({ quizId: quiz, ...(quizSubmissionObj[quiz._id.toString()] ?? {}), userId: req.session.userId})
                });
            }
        } catch (error) {
            console.error(`Error while getting quizzes from redis for user dashboard`);
        }
    }

    let queryQuiz = {'userId' : req.session.userId }
    let aggregationArray = [  { '$match' : {'userId' : mongoose.Types.ObjectId( req.session.userId) } },
        { '$lookup' : { 'from' : 'quizzes' ,
            localField: "quizId",
            foreignField: "_id",
            as: "quizId"
        }},
        { '$unwind' : '$quizId'},
        { '$match' : { 'quizId.showResults' : true, 'quizId._id': { $nin: resultQuiz.map((ele) => mongoose.Types.ObjectId(ele.quizId._id)) } } },
        {'$project' : {'quizId.description' : 1,'quizId.title' : 1, 'quizId.endTime':1 ,'userId' : 1, 'quizId._id' : 1,'quizSubmittedQuestion':1,'startTime':1,'endTime':1,'idleTime':1, 'quizId.createdBy': 1,'userSessions' : 1 }}]
    const resultQuizFromDB = await userQuizSubmittedSegmentController.performAggregation( aggregationArray);
    resultQuiz.push( ...(resultQuizFromDB ?? []));
    let userIdsSet = new Set();
    resultQuiz.map(obj => {
        if (obj && obj.quizId && obj.quizId && obj.quizId.createdBy)    userIdsSet.add(obj.quizId.createdBy.toString());
    })
    let usersObjArr = await services.UserService.getUser({_id: {$in: [...userIdsSet]}}, {displayname: 1}, {});
    let userIdsObj = {};
    (usersObjArr || []).map(obj => userIdsObj[obj._id] = userIdsObj[obj.displayname]);
    for (let index = 0; index < resultQuiz.length; index++) {
        let userSubmittedQuizObj = resultQuiz[index];
        if ( ! userSubmittedQuizObj )   continue ;
        
        let quizId = userSubmittedQuizObj.quizId && userSubmittedQuizObj.quizId._id && userSubmittedQuizObj.quizId._id.toString();
        if ( ! quizId )     continue ;

        userSubmittedQuizObj.quizId.creatorName = userIdsObj[userSubmittedQuizObj.quizId.createdBy];

        let ongoingQuizScoreRedisKey = redisKeys.ongoingQuiz + ":" + req.session.userId + ":" + quizId  + ":" + redisKeys.submittedQuesScores;
        let userQuesScoreObj = await services.RedisServices.redis('hgetall', ongoingQuizScoreRedisKey);
        if ( userQuesScoreObj && Object.keys(userQuesScoreObj).length ) {
            (userSubmittedQuizObj.quizSubmittedQuestion || []).map( obj => {
                if ( ! obj )    return ;
                let qId = obj.questionId && obj.questionId.toString();
                if (userQuesScoreObj[qId]) {
                    obj.score = parseInt(userQuesScoreObj[qId]);
                }
            });
        }
    }
    return resultQuiz;
};

var checkLogIn = async (req, res, next) => {
    try{
        const {email, password} = req.body;
        if(!email || !password) {
            throw new Error('Payload is not valid.');
        }
        const result = await services.UserService.loginUser(req,res);
        if (result) {
            SessionController.createSession(req, result);
        }
        if(next) {
            return next(result);
        };
        return result;
    } catch (error) {
        if(next) return next(error?.message ?? error);
        throw new Error(error?.message);
    }
};

//TODO function is required.
var getQuizContentData = function (containerObj, isQuiz, userAnswer, contentInSequence, containerString, callback) {

    let containerContent = isQuiz ? containerObj.quizContent : containerObj.courseContent;
    let containerSegments = isQuiz ? containerObj.quizSegments : containerObj.courseSegments;

    let totalQuestions = isQuiz
        ? ( containerObj.quizContent.length ? containerObj.quizContent.length : 1 )
        : ( containerObj.courseContent.length ? containerObj.courseContent.length : 1 );

    if (!( containerSegments && containerSegments.length && containerSegments[0].endIndex )) {
        containerSegments = [{'startIndex': 0, 'endIndex': totalQuestions}]
    }
    containerSegments[containerSegments.length - 1].endIndex = totalQuestions;
    let containerSegmentCounter = 0;
    let segmentObj = containerSegments[containerSegmentCounter];

    for (let j = 0; j < containerContent.length; j += 1) {

        if (segmentObj.endIndex === j) {
            containerSegmentCounter += 1;
            segmentObj = containerSegments[containerSegmentCounter];
        }

        let containerContentObj = containerContent[j];
        containerContentObj.question = contentInSequence.reOrderedContentArray[j];
    }
};

//TODO function is rquired.
var getQuizDataForQuestionAttempt = async function (quizObj, sessionObj, callback) {
    try {
        let isCB = callback && typeof callback === 'function'

        let userAnswer = [];
        let containerObj = quizObj;
        containerObj.submittedSegments = [];
        // trying to fetch current segment
        if ((containerObj.userQuizSubmittedSegment && containerObj.userQuizSubmittedSegment.quizSubmittedSegments &&
            containerObj.userQuizSubmittedSegment.quizSubmittedSegments.length)) {
            containerObj.submittedSegments = containerObj.userQuizSubmittedSegment.quizSubmittedSegments.slice(0);
        }
        
        // so that 1 segment always open in dashboard
        containerObj.submittedSegments.push(-1);
        containerObj.currentSegment = containerObj.submittedSegments[0];

        for (let k = 1; k < containerObj.submittedSegments.length; k += 1) {
            if (containerObj.submittedSegments[k] > containerObj.currentSegment)
                containerObj.currentSegment = containerObj.submittedSegments[k];
        }

        // get content in sequence
        //TODO- move it in a function like getReorderedDataFromRedis
        let containerString = 'quizId';
        let key = libs.constants.redisKeys.quizIdString
        key += ':' + containerObj._id.toString();
        let field = libs.constants.redisKeys.segmentCountString + ':-1';
        let result = await services.RedisServices.redis("hget", key, field)
        if (result) {
            getQuizContentData(containerObj, true, userAnswer, JSON.parse(result), containerString, callback);
        } else {
            let containerInSequence = await getContainerSegmentData(containerObj, -1, true )
            getQuizContentData(containerObj, true, userAnswer, containerInSequence, containerString, callback);
        }
        if( isCB )  callback( null, containerObj );
        return containerObj
    } catch (e) {
        console.log("Error while getting quiz data for question attempt", e);
        if( isCB) callback({ error : e.message });
        return false;
    }
};

//TODO function is required.
var getSubmittedQuestionData = function (userQuizSubmittedSegment, quizContent, questionId) {
    let totalSubmittedQuestions = 0;
    let isQuestionIdAttemptPresent = false;
    let submittedQuestions = [...(userQuizSubmittedSegment.submittedQuestions || [])];
    if (submittedQuestions && submittedQuestions.length) {
        for (let j = 0; j < quizContent.length; j += 1) {
            let contentObj = quizContent[j];
            let index = -1;
            if (submittedQuestions.length)
                index = submittedQuestions.indexOf(contentObj.id.toString());

            if (index !== -1) {
                submittedQuestions.splice(index, 1);
                totalSubmittedQuestions += 1;
                contentObj.quizSubmittedQuestion = true;
            }
        }
    }
    if (questionId && ( userQuizSubmittedSegment.isPresentInDb ))
        isQuestionIdAttemptPresent = true;
	return {
        'totalSubmittedQuestions': totalSubmittedQuestions,
        'isQuestionIdAttemptPresent': isQuestionIdAttemptPresent
    };
};


//TODO function is required.
var getLastAttemptOfQuestion = async function (sessionObj, email, userId, containerId, questionId, isQuiz, callback) {
    try {
        let isCB = callback && typeof callback === 'function';
        if ( !(sessionObj && containerId && questionId && email ) ) {
            console.log(" parameters are invalid getLastAttemptsOfQuestion sessionObj-", sessionObj, "containerId-", containerId,
                "questionId-", questionId, "email-", email);
            throw new Error( "parameters are invalid getLastAttemptsOfQuestion")
        }
        if (sessionObj.role === libs.constants.roleNumberFromString.user) {
            if (sessionObj.email !== email) {
                console.log("getLastAttemptOfQuestion trying to get attempt detail of some other user-", "email-", email, "yourId-", sessionObj.email);
                throw new Error("not authorized to access attempt detail");
                return;
            }
        }
        // let userId = sessionObj.userId;
        let findObj = {
            "userId": userId,
            "questionId": questionId
        };
        let key = libs.constants.redisKeys.ongoingQuiz + ":" + userId + ":" + sessionObj.quizId;
        let result = await services.RedisServices.redis("hget", key, libs.constants.redisKeys.questionIdString + ":" + questionId );
        if( !result )   throw new Error("Last attempt not present")
        result = JSON.parse(result);
        
        if (!result.userProgram) {
            let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${sessionObj.quizId}:${userId}:${questionId}`;
            let programs = await services.RedisServices.redis("lrange", userAttemptListByQuesRedisKey, -1, -1);
            if (programs.length) {
                programs = JSON.parse(programs)
                result.userProgram = programs.userProgram
                result.userLanguage = programs.userLanguage
            }
        }
        
        if( isCB)   callback(null, JSON.parse(result));
        return result;
    } catch (e) {
        console.log("Error while getting last attempt", e);
        if( isCB)   callback({ error : e.message });
        return false;
    }
};

//TODO function is required.
var logoutUser = async function (req,res, callback) {
    let isCB = callback && typeof callback === 'function';
    try {
        if (!req) throw new Error("Invalid parameters");

        let isBadLogin = req.isBadLogin
        // req.logout();
        let sessionId = req.session.id;
        let userId = req.session.userId;
        let quizId = req.session.quizId;
        let quizName = req.session.quizName ? req.session.quizName : '';
        // await req.session.destroy();
        await SessionController.logoutUser(req,res);
        await libs.util.destroyInfoAfterSessionTermination(sessionId, false, isBadLogin ? null : {
            'userId': userId,
            'quizId': quizId,
            'quizName': quizName
        });
        if (isCB) return callback(null);
        return true;
    } catch (e) {
        console.log("Error while logging out user", e);
        if (isCB) return callback(e.message);
        return false
    }
};

//TODO function is required.
var checkBadLoginAttemptForQuiz = function (req, res, next) {
    let quizId = req.params.quizId || req.query.quizId
    if (!req.session.quizId) {
        console.trace(" Invalid Request For Quiz");
        let obj = {
            'msg': `${ req.session.email } trying to attempt through some other link `,
            'level': libs.constants.badActivitiesTypeNumber.permission,
            'ip': req.headers['x-forwarded-for']
        };
        logoutUser(req,res, () => {
            res.redirect('/login?msg=Bad Request');
        });
        return true;
    } else if( req.session.quizId != quizId ){
        console.log("Quiz Ids are different ", quizId, req.session );
        logoutUser(req,res, () => {
            res.redirect('/login?msg=Multiple Quiz Login');
        });
        return true;
    }
    return false;
};

const validateInviteToken = async (payload) => {
    const { quizId, email, userIp } = payload;
    const userObj = await services.UserService.getOneUser({email}, {_id: 1 }, {});
    const userId = userObj?._id;
    const quiz = await quizController.getQuizFromDb({id: quizId});
    if (!quiz) {
        throw new Error('Quiz is not valid');
    }
    const isEmailAllowedExist = await quizController.isEmailAllowedExistInQuiz(quizId);
    if (isEmailAllowedExist) {
        const isEmailAllowed = await quizController.isEmailAllowedInQuiz(quizId, email);
        if ( ! isEmailAllowed ) {
            console.log(`${req.body.email} not present in allowed emails. QuizId = ${quizId}`);
            throw new Error(messages.quizAuthentication.emailNotAllowed);
        }
    }
    if (quiz.allowedIP && quiz.allowedIP.split(',').indexOf(userIp)  === -1) {
        throw new Error(messages.loginForQuiz.ipNotAllowed);   
    }
    let queryObj = { userId, quizId };
    let quizSegment = await userQuizSubmittedSegmentController.getUserQuizSubmittedSegmentFromDb( queryObj, {});
    if (Date.now() <= new Date(quiz.startTime)) {
        throw new Error(messages.startQuiz.earlyAttempt);
    } else if (quizSegment && quizSegment.endTime) {
        throw new Error(messages.startQuiz.alreadyAttempted);
    } else if (!quizSegment) {
        if (  new Date() >= new Date(quiz.endTime)) {
            throw new Error(messages.startQuiz.linkExpired);
        }
    }
    if(quizSegment && quizSegment.extraTimeExpiry ){
        let extraTimeExpiry = new Date( quizSegment.extraTimeExpiry );
        let currentDate = new Date()
        if( currentDate > extraTimeExpiry ){
            throw new Error(messages.startQuiz.extraTimeExpiryReached);
        }
    }
}

//TODO function is required. async implementation will be slower.
var loginForQuiz = async function (req, res, cb ) {
    if (req.session.role === libs.constants.roleNumberFromString.admin) {
        // req.flash('info', );
        if (req.isTokenLogin)   res.redirect('/');
        else    res.status(401).json({ error : messages.quizAuthentication.adminNotAllowed });
        return;
    }
    if(req.body.tryTest == 1) req.session.tryTest = true ;
    if (!req?.session?.tryTest
        && (
            req?.session?.role === libs.constants.roleNumberFromString.subAdmin
            ||
            req?.session?.role === libs.constants.roleNumberFromString.superOrg
        )) {
        if (req.isTokenLogin)   res.redirect('/');
        else    res.status(401).json({ error : messages.quizAuthentication.adminNotAllowed });
        return;
    }
		/* had to disable this for react client
		
		if (!(req.headers.referer && req.headers.referer.indexOf('test/') !== -1 )) {
        logger.debug(`invalid path login ${req.headers.referer}`);
        res.redirect('/?msg=Please Login using Test Link');
        return;
    }
		*/

    let quizName = req.body.quizName || req.headers.referer.split("test/")[1];
    req.currentTime = Date.now();
    let { _id , time, startTime, endTime, questionId,
         randomizeQuestion, poolQuestion, quizSegments, quizContent } =  libs.constants.quizParamsHMSET;
    services.RedisServices.redis("hmget", quizName, 
    [ _id.key ], async (errQuizName, quizParams) => {
        if (errQuizName) {
            console.log("loginForQuiz- Error in getting quiz Params for quiz ", quizName, " err is ", errQuizName);
            if (req.isTokenLogin)   res.redirect('/');
            else    res.status(401).json({ error : messages.quizAuthentication.wrongQuizName } );
            return
        } else if (!( quizParams && quizParams[0] ) ) {
            console.log(`loginForQuiz quiz Params not saved in redis for quizname ${quizName}`);
            if (req.isTokenLogin)   res.redirect('/');
            else    res.status(401).json({ error : messages.quizAuthentication.wrongQuizName } );
            return
        }
        quizParams = { [_id.key] : quizParams[0] }
        let quiz = await quizController.getQuizFromDb({ id: quizParams[_id.key], projections: [questionId.key, randomizeQuestion.key, time.key, startTime.key, endTime.key, poolQuestion.key, quizSegments.key, quizContent.key ] });
        if( !quiz){
            console.log("Error in getting quiz in loginforquiz", quizParams[_id.key]);
            logoutUser(req,res, () => {
                if (req.isTokenLogin)   res.redirect('/');
                else    res.status(401).json({ error : messages.loginForQuiz.quizNotPresent});
            });
            return ;   
        }
        quizParams = quiz;
        req.session.quizId = quizParams[ _id.key ];
        req.session.quizName = quizName;
        let ongoingQuizUserIdSessionIdStr = libs.constants.redisKeys.ongoingQuiz + ':' + req.session.userId + ':' + quizParams[ _id.key ];
        services.RedisServices.redis("hsetnx", ongoingQuizUserIdSessionIdStr, libs.constants.redisKeys.sessionId, req.session.id, (errNx, val, code) => {
            if (!val) {
                services.RedisServices.redis("hget", ongoingQuizUserIdSessionIdStr, libs.constants.redisKeys.sessionId, (errSessionId, sessionId) => {
                    if (sessionId != req.session.id) {
                        // req.isBadLogin = true;
                        let obj = {
                            'msg': `${ req.session.email } trying to login from other place `,
                            'level': libs.constants.badActivitiesTypeNumber.cheating,
                            'ip': req.headers['x-forwarded-for'],
                            'userId': req.session.userId,
                            'quizName': quizName,
                            'time': Date.now()
                        };
                        libs.util.sendReportToAdmin(quizName, obj);
                        badActivityController.addBadActivity(obj, (errBad, resultBad) => {
                            if (errBad) {
                                logger.debug("Error in saving bad activity loginForQuiz", errBad);
                                logger.error("Error in saving bad activity loginForQuiz");
                            }
                        });
                        logger.debug(`User trying to login from other place email ${ req.session.email } userId ${ req.session.userId}`);
			            console.log(`User trying to login from other place email ${ req.session.email } userId ${ req.session.userId}`), 
                        forceLoginHandler(quizName, req.session, (err) => {
                            if (err) {
                                console.log("Error in loginForQuiz forceLoginHandler, ERror = ", err);
                                logoutUser(req,res, () => {
                                    if (req.isTokenLogin)   res.redirect('/');
                                    else                    res.status(401).json({error : "Error in logging out User from last login"});
                                });
                                return ;
                            }
			                console.log("Force Login handler completed");
                            req.isForceLogIn = true;
                            req.currentTime = Date.now();
                            setQuizParametersInSessionForQuiz(req, res, quizParams, quizName);
                            return;
                        })
                    } else {
                        setQuizParametersInSessionForQuiz(req, res, quizParams, quizName);
                    }
                });
            } else {
                setQuizParametersInSessionForQuiz(req, res, quizParams, quizName);
            }
        });
    });
}

//TODO function is required. dependent on loginForQuiz
// set quizId, quizName, lastSubmittedSegment
var setQuizParametersInSessionForQuiz = async function (req, res, quizParams, quizName) {
    try {
	    const isTryTest = req.session && req.session.tryTest;
        if (!quizParams) {
            console.log(`setQuizParametersInSessionForQuiz payload not valid`);
            if (req.isTokenLogin)   res.redirect('/');
	        else if ( isTryTest )	res.redirect('/test/list');
            else    res.status(401).json({ error : messages.quizAuthentication.loginDetailError});
            return
        }
        const { _id, quizTime, startTime, endTime, randomizeQuestion, poolQuestion } = libs.constants.quizParamsHMSET;
        req.session.quizId = quizParams[_id.key].toString();
        req.session.quizName = quizName;
        req.session.lastSubmittedSegment = -1;
        let quizTimeInSec = parseInt(quizParams[quizTime.key], 10) * 60;
        let queryObj = { 'userId': req.session.userId, 'quizId': req.session.quizId };
        let resultUserQuiz = await userQuizSubmittedSegmentController.getUserQuizSubmittedSegmentFromDb( queryObj, {});

        let logout = false;
        let msg = "", level;
        let timeElapsedInMilliSec = 0;
        if ((resultUserQuiz?.fromDb || !resultUserQuiz) && global.maintenanceMode) {
            logout = true;
            msg = "Server is under maintenance";
        }
        if (!isTryTest && Date.now() <= new Date(quizParams[startTime.key])) {
            logout = true;
            msg = messages.startQuiz.earlyAttempt;
            level = libs.constants.badActivitiesTypeNumber.permission;
        } else if (resultUserQuiz && resultUserQuiz.endTime) {
            logout = true;
            console.log(`Again trying to reattempt email ${req.session.email}`, queryObj  );
            msg = messages.startQuiz.alreadyAttempted;
            level = libs.constants.badActivitiesTypeNumber.permission;
        } else if (!resultUserQuiz) {
            if ( !isTryTest && req.currentTime >= new Date(quizParams[endTime.key])) {
                logout = true;
                msg = messages.startQuiz.linkExpired;
                level = libs.constants.badActivitiesTypeNumber.permission;
            }
            let isInvitesLeft = await quizController.checkAndUpdateInviteCount(req.session.quizId, req.session.tryTest);
            if ( ! isInvitesLeft ) {
                console.log("invites are finished for quizId = ", req.session.quizId);
                logout = true;
                msg = messages.startQuiz.invitesFinished;
                level = libs.constants.badActivitiesTypeNumber.info;
            } 
        }

        if (resultUserQuiz && !logout ) {
            // let userSessions =  resultUserQuiz.userSessions || [];
            if (!resultUserQuiz.hasOwnProperty('sessionTime')) {
                console.log("Session time not present when user has already submitted once", req.session.quizId);
                logout = true;
                msg = messages.startQuiz.sessionTimeNotPresent;
                level = libs.constants.badActivitiesTypeNumber.permission;
            }
            if (!logout) {
                let sessionTime = resultUserQuiz.sessionTime;
                sessionTime = parseInt(sessionTime, 10)
                if (!resultUserQuiz.hasOwnProperty('startTime')) {
                    // flow will enter here after giving extra time
                    if(resultUserQuiz && resultUserQuiz.extraTimeExpiry ){
                        let extraTimeExpiry = new Date( resultUserQuiz.extraTimeExpiry );
                        let currentDate = new Date()
                        if( currentDate > extraTimeExpiry ){
                            logout = true;
                            console.log(`Extra time expiry has passed current Data = ${currentDate} extratimeexpiry = ${ extraTimeExpiry } userId = ${req.session.userId} quizId = ${req.session.quizId}`);
                            msg = messages.startQuiz.extraTimeExpiryReached;
                            level = libs.constants.badActivitiesTypeNumber.info;
                        }
                    }
                    if (!logout) {
                        let hashName = libs.constants.redisKeys.ongoingQuiz + ':' + req.session.userId + ':' + req.session.quizId;
                        let startTime = Date.now()
                        await services.RedisServices.redis("hmset", hashName,
                         libs.constants.redisKeys.startTime, startTime,
                         libs.constants.redisKeys.extraTimeExpiry, 0);
                        console.log(`Setting start time again user id = ${req.session.userId} quizId = ${req.session.quizId} startTime = ${startTime} and session time is ${sessionTime}`)
                        resultUserQuiz.startTime = startTime;
                    }
                }
                quizTimeInSec = resultUserQuiz.sessionTime;
            }
        }
        let userIp = libs.util.getIpOfRequest(req)
        if ((!logout && resultUserQuiz && resultUserQuiz.startTime)) {
            req.session.lastSubmittedSegment = (resultUserQuiz.quizSubmittedSegments &&
                resultUserQuiz.quizSubmittedSegments.length) ?
                resultUserQuiz.quizSubmittedSegments[resultUserQuiz.quizSubmittedSegments.length - 1] :
                -1;

            let nowww = req.currentTime;
            timeElapsedInMilliSec = (nowww) - new Date(resultUserQuiz.startTime);
            let extraTime = resultUserQuiz.extraTime || 0;
            if (timeElapsedInMilliSec >= ( (quizTimeInSec + extraTime ) * 1000)) {
                logout = true;
                msg = "Trying to open test after test time has finished.";
                level = libs.constants.badActivitiesTypeNumber.permission;
                console.log("Submitting quiz ", req.currentTime, resultUserQuiz.startTime);
                let endTime = new Date( (new Date(resultUserQuiz.startTime) ).getTime() + ( (quizTimeInSec + extraTime ) * 1000) ); 
                quizController.submitQuiz({ quizId: req.session.quizId, userId: req.session.userId }, endTime , (errSubmit, resultSubmit) => {
                });

            }
        }
        if (logout) {
            let obj = { 'msg': req.session.email + ' ' + msg, 'level': level };
            console.log("obj in logout block = ", obj, queryObj );
            if( resultUserQuiz.fromDb){
                let key = libs.constants.redisKeys.ongoingQuiz + ":" + req.session.userId + ":" +
                 req.session.quizId;
                console.log("Deleting userquizsubmitted segment in logout", key );
                await services.RedisServices.redis("del", key );
                await services.RedisServices.redis("del", `${key}:${redisKeys.submittedQuestions}`);
                await services.RedisServices.redis("del", `${key}:${redisKeys.submittedQuesScores}`);
                await services.RedisServices.redis("del", redisKeys.getLoginTimeStampsList( req.session.quizId, req.session.userId));
                await services.RedisServices.redis("del", redisKeys.getTabSwitchReportList( req.session.quizId, req.session.userId));
                await services.RedisServices.redis("del", redisKeys.getfullScreenReportList( req.session.quizId, req.session.userId));
                await services.RedisServices.redis("del", redisKeys.getSegmentSubmissionHistory(req.session.quizId, req.session.userId));
            } else {
                updateQuizActiveInfo( { quizId : req.session.quizId , userId : req.session.userId });

            }
            if (msg != messages.startQuiz.invitesFinished)  libs.util.sendReportToAdmin(quizName, obj);
            logoutUser(req,res, (err, result) => {
                if (req.isTokenLogin && !req.noRedirection)       res.redirect(`/?msg=${msg}`);
		        else if (isTryTest)         res.redirect('/test/list');
                else                        res.status(400).json({ error : msg});
            })
        } else {

            // Live Stream Token
            let token = null;
            try {
                token = await LiveStreamController.getLiveStreamKey({
                    userId: req.session.userId,
                    quizId: req.session.quizId,
                    email: req.session.email,
                });
            } catch (error) {
                console.log(error);
            }
            req.session.authTokenForStream = token;
            // updateData
            let hashName = libs.constants.redisKeys.ongoingQuiz + ':' + req.session.userId + ':' + req.session.quizId;
            if (req.isForceLogIn) {
                msg = "Force Logged In into quiz";
                let obj = {
                    'msg': req.session.email + ' ' + msg,
                    'level': libs.constants.badActivitiesTypeNumber.cheating
                };
                let keyToUpdate = libs.constants.redisKeys.forceLoginCount;
                let updatedValue = await services.RedisServices.redis("hincrby", hashName, keyToUpdate, 1) 
                let output = await services.RedisServices.redis("hgetall", hashName)
                console.log("Force Login count incremented to ", updatedValue, queryObj );
                libs.util.sendReportToAdmin(quizName, output);
            }
            if (!isTryTest) {
                let keyToUpdate = redisKeys.loginCount;
                services.RedisServices.redis("hincrby", hashName, keyToUpdate, 1, (err) => {
                    if (err) {
                        console.log("Error in updating loginCount ", err);
                        return ;
                    }
                    const platform = libs.util.parsePlatform(req);
                    const loginString = libs.util.createQuizLoginRedisString(Date.now(), userIp, JSON.stringify(platform), req.session.id);
                    services.RedisServices.redis('rpush', redisKeys.getLoginTimeStampsList(req.session.quizId, req.session.userId), loginString);
                    if( resultUserQuiz ){
                        services.RedisServices.redis('hset', hashName, redisKeys.currentIp, userIp);
                    }
                });
            }
            // services.RedisServices.redis("expire", libs.constants.redisKeys.ongoingQuiz + ':' + req.session.userId + ':' + req.session.quizId,
            //     parseInt(parseInt(quizTimeInSec * 1000 - timeElapsedInMilliSec, 10) / 1000) + 300);
            // services.RedisServices.redis("expire", hashName, libs.constants.expiryTimeOfQuizReportInSec);
            if (!resultUserQuiz) {
                await quizController.checkActiveUserLimit(quizParams.orgId);
                // New User Here        
                let setObj = {
                    'startTime': Date.now(),
                    'dashboardUpdateTime' : Date.now(),
                    'userId' : req.session.userId,
                    'displayName': req.session.displayname,
                    'email': req.session.email,
                    'tabSwitchCount': 0,
                    'tabSwitchInCount': 0,
                    'userExitCount': 0,
                    'userEnterCount': 0,
                    'multipleUserDetected': 0,
                    'illegalObjectDetected': 0,
                    'fullScreenOutCount': 0,
                    'fullScreenInCount': 0,
                    'forceLoginCount': 0,
                    'codePasteCount': 0,
                    'loginCount': 1,
                    'currentIp' : userIp,
                    'camBlockCount': 0,
                    'tryTest': req.session.tryTest,
                    [libs.constants.redisKeys.submittedSegments]: [],
                    [libs.constants.redisKeys.submittedQuestions]: [],
                    'quizSubmittedQuestion': [],
                    'userSessions' : [],
                    'sessionTime' : quizTimeInSec
                };

                let questionIdsArray = [];
                let index = 0;
                console.log(" quizParams[randomizeQuestion.key] ", quizParams[randomizeQuestion.key])
                console.log(" quizParams[poolQuestion.key] ", quizParams[poolQuestion.key])
                console.log("userId", req.session.userId, "quizId", req.session.quizId )
                if (quizParams[randomizeQuestion.key]) {
                    (quizParams.quizSegments || []).map(segmentObj => {
                        let freezedQuestions = []
                        for (let i = index; i < index + ( segmentObj.count || 0 ); i += 1) {
                            let contentObj = quizParams.quizContent[i];
                            if (contentObj) {
                                freezedQuestions.push(contentObj.id.toString())
                            }
                        }
                        if (freezedQuestions.length) {
                            freezedQuestions = libs.util.shuffle(freezedQuestions);
                            for (let j = 0; (j < segmentObj.count) && (j < freezedQuestions.length); j += 1) {
                                questionIdsArray.push(freezedQuestions[j])
                            }
                        }

                        index += segmentObj.count;
                    })
                    setObj.questionId = questionIdsArray
                } else if (quizParams[poolQuestion.key]) {
                    (quizParams.quizSegments || []).map(segmentObj => {
                        let freezedQuestions = [];
			    segmentObj.count = segmentObj.count || 0;
			    segmentObj.pollNumber = segmentObj.pollNumber || 0;
                        for (let i = index; i < index + (segmentObj.count || 0); i += 1) {
                            let contentObj = quizParams.quizContent[i];
                            if (contentObj) {
                                if (contentObj.isFreeze || 1 ) {
                                    freezedQuestions.push(contentObj.id.toString())
                                }
                            }
                        }
                        if (freezedQuestions.length) {
                            freezedQuestions = libs.util.shuffle(freezedQuestions);
                            for (let j = 0; (j < segmentObj.pollNumber) && (j < freezedQuestions.length); j += 1) {
                                questionIdsArray.push(freezedQuestions[j])
                            }
                        }

                        index += segmentObj.count;
                    })
                    setObj.questionId = questionIdsArray
                }
                console.log(" setobj ", setObj)
                await userQuizSubmittedSegmentController.setQuizSubmittedSegmentsParamsInRedis(setObj, queryObj)
            }
            let activityObj =  { quizId : req.session.quizId , userId : req.session.userId, start : true }
            console.log("updating quiz active info- ", activityObj  )
            updateQuizActiveInfo( activityObj );
            req.session.startTime = resultUserQuiz && resultUserQuiz.startTime ? resultUserQuiz.startTime : Date.now();
            console.log('Saving session');
            req.session.save();
            if(!req.session.tryTest && req.accepts('json') && ! (req.isTokenLogin && !req.noRedirection) ) {
                res.json({
                    success: true,
                });
            } else {
                req.session.token = req.params.encryptedToken;
                res.redirect(`${req.appHost}/test/${quizName}`);
            }
        }
    } catch (e) {
        console.log("Error while setting quiz params in session for quiz", e);
        await logoutUser(req,res);
        if (req.isTokenLogin)   res.redirect('/');
        else    res.status(401).json({ error : e.message })
    }
};

//TODO this function is required.
var logoutUserFromQuiz = async function (payload, callback) {
    try {
        if ( ! payload ) {
            logger.debug(` parameters are invalid logoutUserFromQuiz`, payload);
            logger.error(` parameters are invalid logoutUserFromQuiz`);
            throw new Error("parameters are invalid");
        }
        const {userId, quizId, logoutMessage} = payload;
        if ( ! userId )     throw new Error("userId is null");
        if ( ! quizId )     throw new Error("quizId is null");
        
        const ongoingQuizRedisKey = redisKeys.ongoingQuiz + ':' + userId + ':' + quizId;

        const sessionId = await services.RedisServices.redis("hget", ongoingQuizRedisKey, redisKeys.sessionId);
        if ( ! sessionId )      throw new Error("User not attempting this test.");

        const connectedSocketIds = await services.RedisServices.redis("smembers", sessionId );
        (connectedSocketIds || []).map(socketId => {
            io.to(socketId).emit('session_expired', {msg: logoutMessage || "Force logged out"});
        })

        services.RedisServices.redis("del", sessionId );
        await SessionController.forceLogout(sessionId);
        await services.RedisServices.redis("hdel", ongoingQuizRedisKey, redisKeys.sessionId);

        if (callback)   callback(null, { 'msg': 'User logged Out Successfully' });
        return { 'msg': 'User logged Out Successfully' };
    }
    catch (error) {
        console.log("Error logoutUserFromQuiz, e = ", error);
        if ( callback )     callback({'error': error.message})
        return ;
    }
};

//TODO this function is required.
var forceLoginHandler = async function (quizName, sessionObj, callback) {
    const isCB = callback && typeof callback === 'function';
    try {
        if (! quizName || !sessionObj) {
            throw new Error(`Invalida payload quizName :${!!quizName}or sessionObj ${!!sessionObj}`);
        }
        const { quizParamsHMSET } = libs.constants;
        const { _id, time, endTime, startTime } = quizParamsHMSET;
        const redisGetArr = [ _id.key, time.key, endTime.key, startTime.key ];
        let quizParams = await services.RedisServices.redis('hmget', quizName, redisGetArr);
        if ( ! ( quizParams && quizParams[0] ) ) {
            throw new Error(`quiz Params not saved in redis for quizname ${quizName}`);
        }
        quizParams = {
            [ _id.key ] : quizParams[0],
            [ time.key ] : quizParams[1],
            [ endTime.key ] : quizParams[2],
            [ startTime.key ] : quizParams[3],
        }
        await logoutUserFromQuiz({'userId': sessionObj.userId, 'quizId': quizParams[ _id.key ] });
        let ongoingQuizUserIdSessionIdStr = libs.constants.redisKeys.ongoingQuiz + ':' + sessionObj.userId + ':' + quizParams[ _id.key ];
        const  resultNew = await services.RedisServices.redis("hset", ongoingQuizUserIdSessionIdStr, libs.constants.redisKeys.sessionId, sessionObj.id);
        if(isCB) return callback();
        return resultNew;
    } catch (error) {
        const errorMessage = `Error in forceloginHandler ${error?.message}`;
        if(isCB) return callback(errorMessage);
        throw new Error(errorMessage);
    }
};

//! Dependent on /quiz/report/:quizId need to check if its used or not
var getSubmittedQuestionsAsQuizContent = function (userQuizSubmittedSegment, quizContent, questionId) {
    let totalSubmittedQuestions = 0, totalScore = 0, quizTotalScore = 0;
    let isQuestionIdAttemptPresent = false;
    let submittedQuestions = userQuizSubmittedSegment.quizSubmittedQuestion;

    quizContent.forEach( ( contentObj )=>{
        quizTotalScore += contentObj.question && contentObj.question.score ? contentObj.question.score : 0;
    })

    if (submittedQuestions && submittedQuestions.length) {
        for (let j = 0; j < quizContent.length; j += 1) {
            let contentObj = quizContent[j];
            let index = -1;
            if (submittedQuestions.length) {
                let totalScoreOfQues = 0;
                for (let submittedQuesIndex = submittedQuestions.length - 1; submittedQuesIndex >= 0; submittedQuesIndex -= 1) {
                    if (submittedQuestions[submittedQuesIndex].questionId.toString() === contentObj.id.toString() &&
                        submittedQuestions[submittedQuesIndex].hasSubmitted) {
                        index = submittedQuesIndex;
                        contentObj.quizSubmittedQuestion = submittedQuestions[submittedQuesIndex];
                        totalScoreOfQues = Math.max( totalScoreOfQues, submittedQuestions[submittedQuesIndex].score );
                        totalScore += totalScoreOfQues;
                   //     quizTotalScore += 
                        submittedQuestions.splice(index, 1);
                        //break;
                    }
                }
                if ( contentObj.quizSubmittedQuestion ) {
                    contentObj.quizSubmittedQuestion.score = totalScoreOfQues;
                }
            }
        }
    }
    if (questionId && ( userQuizSubmittedSegment.isPresentInDb ))
        isQuestionIdAttemptPresent = true;
    return {
        'totalSubmittedQuestions': totalSubmittedQuestions, totalScore, quizTotalScore : quizTotalScore < totalScore ? 0 : quizTotalScore, 
        'isQuestionIdAttemptPresent': isQuestionIdAttemptPresent
    };
};


//TODO this function is required.
var addExtraTime = async function (req, callback) {
    try {
        if (!(req && req.body.userId && req.body.quizId && req.body.time)) {
            console.log("Invalid payload addExtraTime");
            throw new Error("Invalid payload");
        }
        req.body.userId = req.body.userId instanceof Array ? req.body.userId : [req.body.userId]

        req.body.quizId = mongoose.Types.ObjectId(req.body.quizId);
        let quizId = req.body.quizId
        let quizObj = await quizController.getOneQuiz({ '_id': quizId }, { 'quizTime': 1, }, {})
        if ((!quizObj)) {
            console.log("Quiz not present", req.body);
            throw new Error("Quiz not present");
        }
        
        let extraTimeExpiry = req.body.extraTimeExpire;
        console.log("Extra time expiry", extraTimeExpiry );
        // extraTimeExpiry = new Date( Date.now() + 60 * 1000 );
        if( extraTimeExpiry ){
            extraTimeExpiry = parseInt( extraTimeExpiry );
            if( extraTimeExpiry > quizObj.quizTime ){
                throw new Error("Extra time expire duration cannot be greater than test duration")
            }
            extraTimeExpiry = new Date ( Date.now() + (extraTimeExpiry * 60 * 1000 ) );
            if( !libs.util.isValidDate( extraTimeExpiry ) ){
                console.log("Invalid Extra time expiry", extraTimeExpiry );
                extraTimeExpiry = null;
            }
        } 
        if (req.body.time > quizObj.quizTime) {
            logger.debug("Extra Time more then quizTotal time");
            callback({ 'error': "Extra time greater than test duration" });
            return;
        }
        let extraTimeUserIds = req.body.userId;
        let updatedUsers = [], notUpdatedUsers = [];
        let redisPipeline = redisClient.pipeline() 
        console.log("Adding extra time ", quizId, extraTimeUserIds)
        for (let i = 0; i < extraTimeUserIds.length; i += 1) {
            let userId = extraTimeUserIds[i]
            let result = await userQuizSubmittedSegmentController.getUserQuizSubmittedSegmentFromDb({ 'userId': userId, 'quizId': quizId }, {})
            
            if (!result) {
                notUpdatedUsers.push( userId )
                continue;
            }

            let sessionTime = result.sessionTime ? parseInt(result.sessionTime) : 0;
            let extraTime = result.extraTime ? parseInt(result.extraTime) : 0;
            extraTime += (parseInt(req.body.time,10) ) * 60;
            let startTime = result.startTime;

            let key =  libs.constants.redisKeys.ongoingQuiz + ':' + userId + ':' + quizId;
            if( startTime && startTime != 0 && result.endTime ){
                redisPipeline.hset( key, libs.constants.redisKeys.sessionTime, (parseInt(req.body.time,10) ) * 60 )
            } else if( startTime && startTime != 0 ){
                redisPipeline.hset( key, libs.constants.redisKeys.extraTime, extraTime )
            } else {
                redisPipeline.hset( key, libs.constants.redisKeys.sessionTime, (parseInt(req.body.time,10) ) * 60 )
            }
            
            redisPipeline
            .hdel( key , libs.constants.redisKeys.endTime)
            .hdel( key, libs.constants.redisKeys.explicitSubmission)
            .hdel( key, libs.constants.redisKeys.isAutoSubmit)
            
            .srem( `${libs.constants.redisKeys.submittedUserIds}:${quizId}`, userId )
            .persist( key )
            if( extraTimeExpiry ){
                redisPipeline.hset( key, libs.constants.redisKeys.extraTimeExpiry, extraTimeExpiry )
            }
            if( result.endTime && result.startTime ){
                //for old users there will be start time for old quizzess
                let userSessions = (result.userSessions || ([]) );
                let sessionTime = ( parseInt(quizObj.quizTime) + ( result.extraTime ? parseInt( result.extraTime ) : 0 ) ) * 60;
                sessionTime = sessionTime > parseInt(quizObj.quizTime) ? parseInt(quizObj.quizTime) : sessionTime;
                let timeTaken = ( new Date( result.endTime ) - new Date( result.startTime ) ) / 60000 
               
                userSessions = [( {
                    startTime :  result.startTime,
                    endTime : result.endTime,
                    sessionTime,
                    isAutoSubmit : result.isAutoSubmit,
                    explicitSubmission : result.explicitSubmission
                })]
                userSessions = JSON.stringify( userSessions );
                redisPipeline
                .hset( key, libs.constants.redisKeys.startTime, null)
                .hset( key, libs.constants.redisKeys.userSessions, userSessions)
            }
            // let res = await services.RedisServices.redis("hmset", key,
            //     )
            // await services.RedisServices.redis("persist", key)

        }

        if( redisPipeline.length ){
            let responsePipeline = await redisPipeline.exec();
        }

        if( updatedUsers.length ){
            updateQuizActiveInfo({ quizId, userIds : extraTimeUserIds })
        }
        callback(null, {
            'msg': "Extra Time added successfully",
            'updatedUsers': updatedUsers,
            'totalUsers': extraTimeUserIds.length,
            notUpdatedUsers,
        });
        try {
            const result = await UserQuizSubmittedSegmentMetaDataController.addExtraTime([...extraTimeUserIds, ...updatedUsers], quizId,{
                createdBy: req.session.userId,
                extraTime:  (parseInt(req.body.time,10) ) * 60,
                expireTime: extraTimeExpiry,

            });
            console.log(result);
        } catch (error) {
            console.log(`Error while saveing ExtraTimeLog`, error);
        }
    } catch (e) {
        console.log("Error while adding extra time", e);
        callback({ error : e.message})
        throw e;
    }
};

var removeContentFromCache = async function (contentId) {
    try {
        if (!(contentId )) {
            logger.debug("Invalid Payload removeContentFromCache ", contentId);
            logger.error("Invalid Payload removeContentFromCache");
            return ''
        }
        const quiz = await quizController.getQuiz({'questionId': contentId}, {'title': 1}, {});
        if(quiz) {
            quiz.forEach((quiz) => {
                services.RedisServices.redis("hdel", `${libs.constants.redisKeys.questionIdString}:${quiz._id.toString()}`, contentId);
            })
        }
    } catch (error) {
        console.log(`Error occured while removeContentfromCache userController\n`,contentId);
    }
};


//! do not touch these may be important for migrations.
function insertDocs(bulkInsert, documents, callback) {
    var insertedIds = [];
    var id;
    documents.forEach(function (doc) {
        id = doc._id;
        // Insert without raising an error for duplicates
        bulkInsert.find({_id: id}).upsert().replaceOne(doc);
        insertedIds.push(id);
    });
    libs.util.executeQuery(bulkInsert, function (errBulkQuery, resultBulkQuery) {
        if (errBulkQuery) {
            logger.debug("error in bulk operation of insertDocs", errBulkQuery);
            logger.error("error in bulk operation of insertDocs");
            callback(errBulkQuery, null);
            return;
        }
        resultBulkQuery.insertedIds = insertedIds;
        callback(errBulkQuery, resultBulkQuery)
    });
}

//! do not touch these may be important for migrations.
function deleteBatch(bulkRemove, documents, callback) {
    documents.forEach(function (doc) {
        bulkRemove.find({_id: doc._id}).removeOne();
    });
    libs.util.executeQuery(bulkRemove, function (errBulkQuery, resultBulkQuery) {
        if (errBulkQuery) {
            logger.debug("error in bulk operation of removeDocs", errBulkQuery);
            logger.error("error in bulk operation of removeDocs");
            callback(errBulkQuery, null);
            return;
        }
        callback(errBulkQuery, resultBulkQuery)
    });
}

//! do not touch these may be important for migrations.
function copyDocsFromOneCollectionToOther(payload, callback) {
    payload['getSourceDocCount'](payload.filter, (errCount, count) => {
        if (errCount) {
            logger.debug("copyDocsFromOneCollectionToOther Error in counting docs " + payload.collectionName, errCount);
            logger.error("copyDocsFromOneCollectionToOther Error in counting docs");
            callback(errCount);
            return;
        }
        if (!count) {
            logger.debug("No document present for archived course in " + payload.collectionName);
            callback(errCount, {
                'totalArchivedDocuments': 0,
                'totalDocCount': 0,
                'totalRemovedDocuments': 0,
                'archivedIds': []
            });
            return;
        }
        console.log("Moving " + count + " documents ");
        var totalArchivedUserAnswers = 0, totalRemovedDocuments = 0, archivedIds = [];
        let batchSize = payload.batchSize ? payload.batchSize : 100;
        (function archiveDocs() {
            if (totalArchivedUserAnswers < count) {
                console.log((count - totalArchivedUserAnswers ) + " documents remaining");
                payload['getSourceDocs'](payload.filter, {}, {
                    'limit': batchSize,
                    'autoPopulate': false
                }, (errUserAnswers, sourceDocs) => {
                    if (errUserAnswers) {
                        logger.error("Error while fetching " + payload.collectionName, errUserAnswers);
                        callback({
                            'error': "Error while fetching " + payload.collectionName,
                            'totalArchivedDocuments': totalArchivedUserAnswers,
                            'totalRemovedDocuments': totalRemovedDocuments,
                            'totalDocCount': count,
                            'archivedIds': archivedIds
                        });
                        return;
                    }
                    // let sourceDocs = sourceCollection.find(filter).limit(batchSize);
                    insertDocs(payload.targetBulkObj(), sourceDocs, (errBulk, resultBulk) => {
                        if (errBulk && errBulk.code != 11000) {
                            callback({
                                'error': "Error while archiving in target " + payload.collectionName,
                                'totalArchivedDocuments': totalArchivedUserAnswers,
                                'totalRemovedDocuments': totalRemovedDocuments,
                                'totalDocCount': count,
                                'archivedIds': archivedIds
                            });
                            return;
                        }

                        payload['getTargetDocs']({_id: {$in: resultBulk.insertedIds}}, {
                            _id: 1,
                            'attemptData': 1
                        }, {'autoPopulate': false}, (errArchived, targetDocs) => {
                            if (errArchived) {
                                logger.debug("copyDocsFromOneCollectionToOther Error while fetching inserted docs " + payload.collectionName, errArchived);
                                logger.error("copyDocsFromOneCollectionToOther Error while fetching inserted docs");
                                callback({
                                    'error': "Error while fetching inserted docs " + payload.collectionName,
                                    'totalArchivedDocuments': totalArchivedUserAnswers,
                                    'totalRemovedDocuments': totalRemovedDocuments,
                                    'totalDocCount': count,
                                    'archivedIds': archivedIds
                                });
                                return;
                            }
                            totalArchivedUserAnswers += sourceDocs.length;
                            deleteBatch(payload.sourceBulkObj(), targetDocs, (errBulkRemove, resultBulkRemove) => {
                                if (errBulkRemove) {
                                    callback({
                                        'error': "Error while removing from source " + payload.collectionName,
                                        'totalArchivedDocuments': totalArchivedUserAnswers,
                                        'totalRemovedDocuments': totalRemovedDocuments,
                                        'totalDocCount': count,
                                        'archivedIds': archivedIds
                                    });
                                    return;
                                }
                                if (payload.collectionName === libs.constants.modelString.UserAttemptModelString ||
                                    payload.collectionName === libs.constants.modelString.archivedUserAttemptModelString) {
                                    let archivedIdsNew = [];
                                    targetDocs.forEach((attemptObj) => {
                                        if (attemptObj.attemptData && attemptObj.attemptData.length) {
                                            attemptObj.attemptData.forEach((userAttempt) => {
                                                archivedIdsNew.push(userAttempt._id);
                                            })
                                        }
                                    });
                                    archivedIds = archivedIds.concat(archivedIdsNew);
                                } else {
                                    archivedIds = archivedIds.concat(resultBulk.insertedIds);
                                }
                                totalRemovedDocuments += targetDocs.length;
                                setTimeout(archiveDocs, 0);
                            });
                        })
                    });
                })
            } else {
                callback(errCount, {
                    'totalArchivedDocuments': totalArchivedUserAnswers,
                    'totalRemovedDocuments': totalRemovedDocuments,
                    'totalDocCount': count,
                    'archivedIds': archivedIds
                });
                console.log("Done!");
                return;
            }
        })();
    });
}

//! this function will not work getUnorderedBulkOpObj is not implemented.
var archiveCourseOrQuiz = function (payload, callback) {
    if (!( payload && ( payload.courseId || payload.quizId ) && callback && typeof callback === 'function')) {
        logger.error("Invalid payload archiveCourseOrQuiz", payload);
        logger.error("Invalid payload archiveCourseOrQuiz");
        callback({'error': "Invalid Payload"});
        return;
    }

    libs.util.getActiveUsersInQuiz({[ payload.courseId ? 'courseId' : 'quizId'] : payload.courseId || payload.quizId }, (err, activeUsers) => {
        if (err) {
            callback({'error': "Error while getting active users in quiz"});
            return;
        } else if (activeUsers > 0) {
            callback({'error': "Quiz is active, plz try after some time"});
            return;
        }
        services.RedisServices.redis("get", libs.constants.redisKeys.isArchiving, (errRedis, value) => {
            if (errRedis) {
                logger.debug("archiveCourseOrQuiz Error while getting redis key", errRedis);
                logger.error("archiveCourseOrQuiz Error while getting redis key");
                callback({'error': "Unable to process request "});
                return;
            } else if (value === libs.constants.redisKeys.compilationInProgress) {
                logger.debug("Another Archive already in progress");
                callback({'error': "Another archive already in progress, please try after some time"});
                return;
            }
            services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.compilationInProgress, (errRedis) => {
                if (errRedis) {
                    logger.debug("archiveCourseOrQuiz Error while setting up redis key", errRedis);
                    logger.error("archiveCourseOrQuiz Error while setting up redis key");
                    callback({'error': "Unable to process request "});
                    services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString)
                    return;
                }
                let startTime = Date.now();
                payload.containerId = mongoose.Types.ObjectId(payload.courseId || payload.quizId);
                let userAnswerPayload = {
                    'batchSize': 1000,
                };
                userAnswerPayload.filter = payload.courseId ? {'courseId': payload.containerId} : {'quizId': payload.containerId}
                let userAttemptPayload = {
                    'batchSize': 1000,
                };
                let userAttemptSnapshotPayload = {
                    'batchSize': 1000,
                };
                let userQuizSubmittedSegmentPayload = {
                    'filter': {'quizId': payload.containerId},
                    'batchSize': 1000,
                }
                if (payload.archaic == "true") {
                    console.log("Archiving course ", payload.courseId);
                    userAnswerPayload.sourceBulkObj = services.UserAnswerService.getUnorderedBulkOpObj;
                    userAnswerPayload.targetBulkObj = services.ArchivedServices.UserAnswerService.getUnorderedBulkOpObj;
                    userAnswerPayload.getSourceDocCount = userAnswerController.getDocCount;
                    userAnswerPayload.getSourceDocs = userAnswerController.getUserAnswer;
                    userAnswerPayload.getTargetDocs = archivedUserAnswerController.getUserAnswer;
                    userAnswerPayload.collectionName = libs.constants.modelString.UserAnswerModelString

                    userAttemptPayload.sourceBulkObj = services.UserAttemptService.getUnorderedBulkOpObj;
                    userAttemptPayload.targetBulkObj = services.ArchivedServices.UserAttemptService.getUnorderedBulkOpObj;
                    userAttemptPayload.getSourceDocCount = userAttemptController.getDocCount;
                    userAttemptPayload.getSourceDocs = userAttemptController.getUserAttempt;
                    userAttemptPayload.getTargetDocs = archivedUserAttemptController.getUserAttempt;
                    userAttemptPayload.collectionName = libs.constants.modelString.UserAttemptModelString;

                    userAttemptSnapshotPayload.sourceBulkObj = services.UserAttemptSnapshotService.getUnorderedBulkOpObj;
                    userAttemptSnapshotPayload.targetBulkObj = services.ArchivedServices.UserAttemptSnapshotService.getUnorderedBulkOpObj;
                    userAttemptSnapshotPayload.getSourceDocCount = userAttemptSnapshotController.getDocCount;
                    userAttemptSnapshotPayload.getSourceDocs = userAttemptSnapshotController.getUserAttemptSnapshot;
                    userAttemptSnapshotPayload.getTargetDocs = archivedUserAttemptSnapshotController.getUserAttemptSnapshot;
                    userAttemptSnapshotPayload.collectionName = libs.constants.modelString.UserAttemptSnapshotModelString;

                    userQuizSubmittedSegmentPayload.sourceBulkObj = services.UserQuizSubmittedSegmentService.getUnorderedBulkOpObj;
                    userQuizSubmittedSegmentPayload.targetBulkObj = services.ArchivedServices.UserQuizSubmittedSegmentService.getUnorderedBulkOpObj;
                    userQuizSubmittedSegmentPayload.getSourceDocCount = userQuizSubmittedSegmentController.getDocCount;
                    userQuizSubmittedSegmentPayload.getSourceDocs = userQuizSubmittedSegmentController.getUserQuizSubmittedSegment;
                    userQuizSubmittedSegmentPayload.getTargetDocs = archivedUserQuizSubmittedSegmentController.getUserQuizSubmittedSegment;
                    userQuizSubmittedSegmentPayload.collectionName = libs.constants.modelString.UserQuizSubmittedSegment;

                } else {
                    console.log("Activating course ", payload.courseId);
                    userAnswerPayload.sourceBulkObj = services.ArchivedServices.UserAnswerService.getUnorderedBulkOpObj;
                    userAnswerPayload.targetBulkObj = services.UserAnswerService.getUnorderedBulkOpObj;
                    userAnswerPayload.getSourceDocCount = archivedUserAnswerController.getDocCount;
                    userAnswerPayload.getSourceDocs = archivedUserAnswerController.getUserAnswer;
                    userAnswerPayload.getTargetDocs = userAnswerController.getUserAnswer;
                    userAnswerPayload.collectionName = libs.constants.modelString.archivedUserAnswerModelString

                    userAttemptPayload.sourceBulkObj = services.ArchivedServices.UserAttemptService.getUnorderedBulkOpObj;
                    userAttemptPayload.targetBulkObj = services.UserAttemptService.getUnorderedBulkOpObj;
                    userAttemptPayload.getSourceDocCount = archivedUserAttemptController.getDocCount;
                    userAttemptPayload.getSourceDocs = archivedUserAttemptController.getUserAttempt;
                    userAttemptPayload.getTargetDocs = userAttemptController.getUserAttempt;
                    userAttemptPayload.collectionName = libs.constants.modelString.archivedUserAttemptModelString;

                    userAttemptSnapshotPayload.sourceBulkObj = services.ArchivedServices.UserAttemptSnapshotService.getUnorderedBulkOpObj;
                    userAttemptSnapshotPayload.targetBulkObj = services.UserAttemptSnapshotService.getUnorderedBulkOpObj;
                    userAttemptSnapshotPayload.getSourceDocCount = archivedUserAttemptSnapshotController.getDocCount;
                    userAttemptSnapshotPayload.getSourceDocs = archivedUserAttemptSnapshotController.getUserAttemptSnapshot;
                    userAttemptSnapshotPayload.getTargetDocs = userAttemptSnapshotController.getUserAttemptSnapshot;
                    userAttemptSnapshotPayload.collectionName = libs.constants.modelString.archivedUserAttemptSnapshotModelString;

                    userQuizSubmittedSegmentPayload.sourceBulkObj = services.ArchivedServices.UserQuizSubmittedSegmentService.getUnorderedBulkOpObj;
                    userQuizSubmittedSegmentPayload.targetBulkObj = services.UserQuizSubmittedSegmentService.getUnorderedBulkOpObj;
                    userQuizSubmittedSegmentPayload.getSourceDocCount = archivedUserQuizSubmittedSegmentController.getDocCount;
                    userQuizSubmittedSegmentPayload.getSourceDocs = archivedUserQuizSubmittedSegmentController.getUserQuizSubmittedSegment;
                    userQuizSubmittedSegmentPayload.getTargetDocs = userQuizSubmittedSegmentController.getUserQuizSubmittedSegment;
                    userQuizSubmittedSegmentPayload.collectionName = libs.constants.modelString.archivedUserQuizSubmittedSegment;
                }

                copyDocsFromOneCollectionToOther(userAnswerPayload, (err, result) => {
                    if (err) {
                        logger.debug("archiveCourseOrQuiz Error while archiving userAnswers ", err);
                        logger.error("archiveCourseOrQuiz Error while archiving userAnswers");
                        callback(err);
                        services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString)
                        return;
                    }
                    userAttemptPayload.filter = {'userAnswerId': {$in: result.archivedIds}};
                    copyDocsFromOneCollectionToOther(userAttemptPayload, (errUserAttempt, resultUserAttempt) => {
                        if (errUserAttempt) {
                            logger.debug("archiveCourseOrQuiz Error while archiving userAttempts ", errUserAttempt);
                            logger.error("archiveCourseOrQuiz Error while archiving userAttempts");
                            callback(errUserAttempt);
                            services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString)
                            return;
                        }
                        result.archivedIds ? delete result.archivedIds : "";
                        userAttemptSnapshotPayload.filter = {'userAttemptId': {$in: resultUserAttempt.archivedIds}}
                        copyDocsFromOneCollectionToOther(userAttemptSnapshotPayload, (errUserAttemptSnapshot, resultUserAttemptSnapshot) => {
                            if (errUserAttemptSnapshot) {
                                logger.debug("archiveCourseOrQuiz Error while archiving userAttemptSnapshot ", errUserAttemptSnapshot);
                                logger.error("archiveCourseOrQuiz Error while archiving userAttemptSnapshot");
                                callback(errUserAttemptSnapshot);
                                services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString)
                                return;
                            }
                            resultUserAttempt.archivedIds ? delete resultUserAttempt.archivedIds : "";
                            resultUserAttemptSnapshot.archivedIds ? delete resultUserAttemptSnapshot.archivedIds : "";
                            if (payload.quizId) {
                                copyDocsFromOneCollectionToOther(userQuizSubmittedSegmentPayload, (errUserQuizSubmittedSegment, resultUserQuizSubmittedSegment) => {
                                    if (errUserQuizSubmittedSegment) {
                                        logger.debug("archiveCourseOrQuiz Error while archiving userQuizSubmittedSegment ", errUserQuizSubmittedSegment);
                                        logger.error("archiveCourseOrQuiz Error while archiving userQuizSubmittedSegment");
                                        callback(errUserQuizSubmittedSegment);
                                        services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString)
                                        return;
                                    }
                                    resultUserQuizSubmittedSegment.archivedIds ? delete resultUserQuizSubmittedSegment.archivedIds : "";
                                    console.log("Total time taken while archiving is ", ( Date.now() - startTime ) / 1000);
                                    callback(errUserQuizSubmittedSegment, {
                                        'msg': "Test Archived Successfully",
                                        'userAnswerData': result,
                                        'userAttemptData': resultUserAttempt,
                                        'userAttemptSnapshotData': resultUserAttemptSnapshot,
                                        'userQuizSubmittedSegmentData': resultUserQuizSubmittedSegment,
                                        'time_taken': ( Date.now() - startTime ) / 1000
                                    })
                                    services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString)
                                });
                            } else {
                                console.log("Total time taken while archiving is ", ( Date.now() - startTime ) / 1000);
                                callback(errUserAttemptSnapshot, {
                                    'msg': "Course Archived Successfully",
                                    'userAnswerData': result,
                                    'userAttemptData': resultUserAttempt,
                                    'userAttemptSnapshotData': resultUserAttemptSnapshot,
                                    'time_taken': ( Date.now() - startTime ) / 1000
                                })
                                services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString)
                            }
                        });
                    })
                })
            });
        });
    });
};

//! do not touch these may be important for migrations.
const moveErrorsToArchive = function (req, callback) {
    if (!( req && callback && typeof callback === 'function')) {
        logger.debug("moveErrorsToArchive Invalid Payload")
        logger.error("moveErrorsToArchive Invalid Payload");
        callback({'error': "invalid payload"});
        return;
    }
    libs.util.getActiveUsersInQuiz({}, (err, activeUsers) => {
        if (err) {
            callback({'error': "Error while getting active users in quiz"});
            return;
        } else if (activeUsers > 0) {
            callback({'error': "Test is active, plz try after some time"});
            return;
        }
        services.RedisServices.redis("get", libs.constants.redisKeys.isArchiving, (errRedis, value) => {
            if (errRedis) {
                logger.debug("moveErrorsToArchive Error while getting redis key", errRedis);
                logger.error("moveErrorsToArchive Error while getting redis key");
                callback({'error': "Unable to process request "});
                return;
            } else if (value === libs.constants.redisKeys.compilationInProgress) {
                logger.debug("Another Archive already in progress");
                callback({'error': "Another archive already in progress, please try after some time"});
                return;
            }
            services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.compilationInProgress, (errRedis) => {
                if (errRedis) {
                    logger.debug("moveErrorsToArchive Error while setting up redis key", errRedis);
                    logger.error("moveErrorsToArchive Error while setting up redis key");
                    callback({'error': "Unable to process request "});
                    services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString);
                    return;
                }
                let startTime = Date.now();
                let errorPayload = {
                    'batchSize': 1000,
                    'filter' : req.body.filter === libs.constants.redisKeys.all ? {} : req.body.filter
                };
                if ( req.body.archaic == "true" ) {
                    errorPayload.sourceBulkObj = services.ErrorService.getUnorderedBulkOpObj;
                    errorPayload.targetBulkObj = services.ArchivedServices.ErrorService.getUnorderedBulkOpObj;
                    errorPayload.getSourceDocCount = errorController.getDocCount;
                    errorPayload.getSourceDocs = errorController.getError;
                    errorPayload.getTargetDocs = archivedErrorController.getError;
                    errorPayload.collectionName = libs.constants.modelString.errorModelString;
                } else {
                    errorPayload.sourceBulkObj = services.ArchivedServices.ErrorService.getUnorderedBulkOpObj;
                    errorPayload.targetBulkObj = services.ErrorService.getUnorderedBulkOpObj;
                    errorPayload.getSourceDocCount = archivedErrorController.getDocCount;
                    errorPayload.getSourceDocs = archivedErrorController.getError;
                    errorPayload.getTargetDocs = errorController.getError;
                    errorPayload.collectionName = libs.constants.modelString.archivedErrorModelString;
                }
                copyDocsFromOneCollectionToOther(errorPayload, (err, result) => {
                    if (err) {
                        logger.debug("moveErrorsToArchive Error while archiving errors ", err);
                        logger.error("moveErrorsToArchive Error while archiving errors");

                        callback(err);
                        services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString);
                        return;
                    }
                    console.log("Total time taken while archiving is ", ( Date.now() - startTime ) / 1000);
                    callback(err, {
                        'msg': "Errors Archived Successfully",
                        'errors': result,
                    })
                    services.RedisServices.redis("set", libs.constants.redisKeys.isArchiving, libs.constants.redisKeys.emptyString)
                });
            })
        })
    });
};


//TODO required in /attemptedQuestion/quiz/:email/:quizid/:questionid need to check if used or not
const getLatestAttemptOfQuestion = async function (payload, cb) {
    try {
        let { userId, quizId, questionId, session, userObj }  = payload;
        if ( ! userId )         throw new Error("UserId is null");
        if ( ! quizId )         throw new Error("QuizId is null");
        if ( ! questionId )     throw new Error("QuestionId is null");
        if ( ! session )        throw new Error("Session is null");

        let userAttemptsRedisKey = `${redisKeys.userQuesAttemptsList}:${quizId}:${userId}:${questionId}`;
        let attemptsListSizeFromRedis = await services.RedisServices.redis2("llen", userAttemptsRedisKey);
        if ( attemptsListSizeFromRedis ) {
            payload.attemptId = attemptsListSizeFromRedis - 1;
            payload.quesId = payload.questionId;
            let attemptObj = await userAttemptController.getAttemptForPreviewFromRedis(payload);
            if ( attemptObj && attemptObj.ques && 
                attemptObj.ques.type != constants.questionTypeNumeric.questionTypeCoding && 
                ! ( attemptObj.ques.type == constants.questionTypeNumeric.questionTypeWeb && attemptObj.ques.questionTypeWeb && attemptObj.ques.questionTypeWeb.isReactQuestion )
                ) {      // Coding submissions are not saved in redis
                let obj = {
                    questionId: [attemptObj.ques],
                    latestAttempt: attemptObj.userAttempt && attemptObj.userAttempt.attemptData && attemptObj.userAttempt.attemptData[0],
                }
                if (cb)     cb(null, obj);
                return obj;
            }
        }
        return getLatestAttemptOfQuestionFromDB(payload, cb);
    } catch (e) {
        console.log("getLatestAttemptOfQuestion, ERror = ", e);
        if (cb)     cb(e);
        return {error: e.message};
    }
}

//TODO required in /attemptedQuestion/quiz/:email/:quizid/:questionid need to check if used or not
var getLatestAttemptOfQuestionFromDB = async function ( params, callback) {
    const isCB = callback && typeof callback === 'function';
    try {
        if( !params || !params.isQuiz){
            logger.error("getLatestAttemptOfQuestionFromDB Parameters not valid");
            throw new Error(`getLatestAttemptOfQuestionFromDB Parameters not valid ${params}`);
        }
        let queryObj = {'questionId' : mongoose.Types.ObjectId(params.questionId), 'userId' : mongoose.Types.ObjectId( params.userId ) }
        queryObj.quizId = mongoose.Types.ObjectId( params.containerId );
        let lookUpObj = {
            from: "userattempts",
            localField: "_id",
            foreignField: "userAnswerId",
            as: "attemptDocs"
        }
        let aggregationArray = [
            {'$match' : queryObj },
            // { '$lookup' : lookUpObj },
            { $lookup : {
                'from' : 'questions',
                'localField' : 'questionId',
                'foreignField' : '_id',
                'as' : 'questionId'} },
            { $lookup : {
                'from' : 'quizzes',
                'localField' : 'quizId',
                'foreignField' : '_id',
                'as' : 'quizId'} },
            { $lookup : {
                'from' : 'userattempts',
                'localField' : '_id',
                'foreignField' :  'userAnswerId',
                'as' : 'userattempts'} } ,
            { $match : { 'userattempts.attemptData.finalSubmission' : true } },
            // {$unwind : "$userattempts"},
            { $project: { 'userattempts.createdAt' : 1, 'userattempts.attemptData.userInputMCQ':1 ,'userattempts.attemptData.userOutputCoding':1 ,
                'userattempts.attemptData.userCompilationError':1 ,'courseId._id':1 , 'quizId:_id' : 1, 'userattempts.attemptData.userProgram':1 ,
                'userattempts.attemptData.finalSubmission':1,
                'userattempts.attemptData.html':1, 'userattempts.attemptData.css':1, 'userattempts.attemptData.js':1, 'userattempts.attemptData.webTestCaseStatus':1,
                'courseId.title' : 1 ,'quizId.title' : 1, 'quizId.quizContent' : 1, 'questionId._id': 1 ,'questionId.questionTypeCoding' : 1 ,
                'questionId.questionTypeMCQ' : 1 ,  'questionId.title':1 , 'questionId.type' : 1 , 'lastActive' : 1, 'questionId.score' : 1, 'questionId.text' : 1 ,
                'questionId.questionTypeMCQ' : 1, 'questionId.questionTypeCoding' : 1, 'questionId.type' : 1, 'questionId.questionTypeWeb' : 1,'userattempts.attemptData.userLanguage':1} } ,
    
        ];
        const result = await userAnswerController.performAggregation(aggregationArray);
        if (!(result && result.length )){
            throw new Error(`getLatestAttemptOfQuestionFromDB no result found for ${params}`);
        }
        let latestAttempt = null, referenceTime = null;
        result = result[0];
        if( result.userattempts && result.userattempts.length){
            for( let i =0 ; i < result.userattempts.length; i += 1) {
                let userAttemptsObj = result.userattempts[i];
                //if( !referenceTime ){
                    referenceTime = userAttemptsObj.createdAt;
                //}
                for( let j=0 ; j< userAttemptsObj.attemptData.length ; j+=1){
                    if ( referenceTime >= userAttemptsObj.createdAt && userAttemptsObj.attemptData && userAttemptsObj.attemptData.length && userAttemptsObj.attemptData[j].finalSubmission == true
                ) {
                        latestAttempt = userAttemptsObj.attemptData[ j ];
                    }
                }
            }
        }
        if( result.questionId[0].type == '4'){
            for(var i=0 ; i < result.quizId[0].quizContent.length ; i++ ){
                if( result.questionId[0]._id.toString() == result.quizId[0].quizContent[i].id.toString()){
                    result.executionType = result.quizId[0].quizContent[i].executionType ;
                    result.showHead = result.quizId[0].quizContent[i].showHead ;
                    result.showTail = result.quizId[0].quizContent[i].showTail ;
                    delete result.quizId[0].quizContent ;
                    break;
                }
            }
        }
        result.latestAttempt = latestAttempt;
        if(isCB) return callback( err, result )
        return result;
    } catch (error) {
        console.trace(error);
        if(isCB) return callback(error?.message);
        throw new Error(error);
    }
}


//TODO this function is required.
const addSuperAdminIdToRedis = async function () {
    try {
        const superAdminId = await services.RedisServices.redis('get', 'superAdminId');
        if (superAdminId) {
            global.superAdminId = superAdminId;
            return ;
        }
        const user = await getOneUser({role: libs.constants.roleNumberFromString.admin}, {_id: 1}, {});
        if (!user) {
            throw new Error(`Super admin not found`);
        }
        await services.RedisServices.redis(`set`, libs.constants.redisKeys.superAdminId, user._id.toString());

        //
        const isExists= await services.RedisServices.redis(`exists`, 'applyUserIds');
        if (isExists) return;
        const subUsers = await getUser({email: libs.constants.applySubAdminEmail}, {_id: 1}, {});
        services.RedisServices.redis(`set`, libs.constants.redisKeys.superAdminId, user._id.toString());
        global.superAdminId = user._id.toString();
        console.log(`SuperAdmin Id = `, global.superAdminId);
    } catch (error) {
        console.log(`Error while adding super admin into redis ${error.message}`);
    }
};

//TODO this functions is required.
const getUsersByRole = function(session, callback) {
    let rolesArr = [ 
        libs.constants.roleNumberFromString.admin,
        libs.constants.roleNumberFromString.superOrg,
        libs.constants.roleNumberFromString.subAdmin,
        libs.constants.roleNumberFromString.mentor,
        libs.constants.roleNumberFromString.contentCreator,
        libs.constants.roleNumberFromString.recruiter,
    ];
    let findQuery = {
        role: { $in: rolesArr },
        status: libs.constants.displayStatus.active
    };
    if (session.role === libs.constants.roleNumberFromString.admin) {
        findQuery.$or = [ { createdBy: mongoose.Types.ObjectId(session.userId) }, { _id: mongoose.Types.ObjectId(session.userId) } ];
    } else {
        findQuery.orgId = session.orgId;
    }
    if (session.role === libs.constants.roleNumberFromString.superOrg) {
        findQuery.orgId = [...(session.orgIdsAssigned), session.orgId];
    }
    let projectionQuery = { displayname: 1 };
    return getUser( findQuery, projectionQuery, {}, callback);
}

const getUserArrayForFilter = async (sessionObj) => {
    if (!sessionObj) throw new Error('Invalid payload session obj was not provided');
    let users = await getUsersByRole(sessionObj);
    if (users.length) {
        users = users.filter((element) => (element._id.toString() !== sessionObj?.userId?.toString()));
    }
    return users;
}

const getSubmittedQuestionDataOfQuizFromRedis = async function( payload, cb ){
    try {
        const { userId, quizId, isArchived, session } = payload;
        if ( ! userId )     throw new Error("UserId is null");
        if ( ! quizId )     throw new Error("QuizId is null");
        if ( ! session )     throw new Error("Session is null");

        const { allowedIP, quizSegments, quizContent, languagesAllowed, quizTime, randomizeQuestion,        poolQuestion, createdBy,
            copyPasteAllowed, revisitAllowed, title, tabSwitchAllowed, isWebCamAllowed } = libs.constants.quizParamsHMSET;

        let projections = [quizSegments.key,
            quizContent.key, languagesAllowed.key, quizTime.key,
            copyPasteAllowed.key, revisitAllowed.key, title.key,
            tabSwitchAllowed.key, allowedIP.key, isWebCamAllowed.key, 
        randomizeQuestion.key, poolQuestion.key, createdBy.key]
        
        let quizObj = await quizController.getQuizFromDb({id: quizId, projections}, {} );
        if ( ! quizObj )    throw new Error("Quiz Id is invalid");

				if(session && session.role == libs.constants.roleNumberFromString.user ){
					if (!quizObj.showResults) {
						logger.debug("Trying to view attempt beofe submission");
						throw new Error("Not authorized to view attempt before submission.");
					}
				}

        let quesIds = ( quizObj.quizContent || [] ).map(contentObj => contentObj.id);
        let quesObjArr = await questionController.getQuestion({_id: {$in: quesIds}}, {}, {});

        let userObj = await getOneUser({_id: userId}, {email: 1, displayname: 1, enrollmentId: 1}, {});
        if ( ! userObj )    throw new Error("UserId not exists");

        ( quizObj.quizContent || [] ).map(contentObj => {
            for (let index = 0; index < quesObjArr.length; index++) {
                let element = quesObjArr[index];
                if ( element._id && contentObj.id && element._id.toString() == contentObj.id.toString() ) {
                    contentObj.question = element;
                    break;
                }
            }
        })

        let ongoingUserQuizRedisKey = `${constants.redisKeys.ongoingQuiz}:${userId}:${quizId}`;
        let userQuizSubmittedSegmentObj = await services.RedisServices.redis('hgetall', ongoingUserQuizRedisKey);
        if ( Object.keys(userQuizSubmittedSegmentObj || {}).length ) {
            let submittedQuesIdsRedisKey = `${constants.redisKeys.ongoingQuiz}:${userId}:${quizId}:${constants.redisKeys.submittedQuesScores}`;
            let submittedQuesScoresObj = await services.RedisServices.redis('hgetall', submittedQuesIdsRedisKey);
            let submittedQuesIds = Object.keys(submittedQuesScoresObj || {});
            if( userQuizSubmittedSegmentObj.endTime ){
                let endTime = parseInt( userQuizSubmittedSegmentObj.endTime )
                userQuizSubmittedSegmentObj.endTime  = new Date( isNaN( endTime) ? userQuizSubmittedSegmentObj.endTime : endTime  )
            }
            if( userQuizSubmittedSegmentObj.startTime ){
                let startTime = parseInt( userQuizSubmittedSegmentObj.startTime )
                userQuizSubmittedSegmentObj.startTime  = new Date( isNaN( startTime) ? userQuizSubmittedSegmentObj.startTime : startTime  )
            }
            for( let i = 0 ; i < ( quizObj.quizContent || [] ).length; i++ )
              {
                let contentObj = quizObj.quizContent[i];
                let id = ( contentObj.id || '').toString()
                if ( submittedQuesIds.indexOf(id) != -1 || contentObj.contentType === libs.constants.contentTypeString.contentTypeCoding ) {
                    let keyName = `${constants.redisKeys.questionIdString}:${id}`;
                    if ( userQuizSubmittedSegmentObj[keyName] ) {
                        contentObj.quizSubmittedQuestion = JSON.parse(userQuizSubmittedSegmentObj[keyName]);
                        if (submittedQuesIds.indexOf(id) !== -1) {
                            contentObj.quizSubmittedQuestion.hasSubmitted = true;       
                        }
                        contentObj.quizSubmittedQuestion.score = submittedQuesScoresObj[id];
                        if (!contentObj?.quizSubmittedQuestion?.hasSubmitted) {
                            contentObj.quizSubmittedQuestion.score = 0;
                        }
                        contentObj.attempts = await getUserQuesAttemptsFromRedis({ email : session.email, userId , container : quizObj , questionId : contentObj.id, session,
                        quizId : quizObj._id.toString(), userObj } )
                        if ( !contentObj.quizSubmittedQuestion?.hasSubmitted && !(contentObj?.attempts.userAttempts[0]?.attemptData.length ?? 0)) {
                            delete contentObj.quizSubmittedQuestion;
                        }
                    }
                    //TODO Check If This Code Is Required.
                    // contentObj.quizSubmittedQuestion.score = submittedQuesScoresObj[id];
                    // if (!contentObj?.quizSubmittedQuestion?.hasSubmitted) {
                    //     contentObj.quizSubmittedQuestion.score = 0;
                    // }
                    // contentObj.attempts = await getUserQuesAttemptsFromRedis({ email : session.email, userId , container : quizObj , questionId : contentObj.id, session,
                    // quizId : quizObj._id.toString(), userObj } )
                }
                // }
            }
            userQuizSubmittedSegmentObj.segmentSubmissionHistory = await userQuizSubmittedSegmentController.getSegmentSubmissionHistoryFromRedis(quizId, userId, {skipDB: true});
            quizObj.userQuizSubmittedSegment = userQuizSubmittedSegmentObj;
        }
        else {
            
            userQuizSubmittedSegmentObj = await userQuizSubmittedSegmentController.getOneUserQuizSubmittedSegment({quizId, userId}, {}, {});
            if ( ! userQuizSubmittedSegmentObj )    throw new Error("UserSubmittedSegment obj is not found");
            if( userQuizSubmittedSegmentObj.endTime ){
                let endTime = parseInt( userQuizSubmittedSegmentObj.endTime )
                userQuizSubmittedSegmentObj.endTime  = new Date( isNaN( endTime) ? userQuizSubmittedSegmentObj.endTime : endTime  )
            }
            if( userQuizSubmittedSegmentObj.startTime ){
                let startTime = parseInt( userQuizSubmittedSegmentObj.startTime )
                userQuizSubmittedSegmentObj.startTime  = new Date( isNaN( startTime) ? userQuizSubmittedSegmentObj.startTime : startTime  )
            }
            quizObj.userQuizSubmittedSegment = userQuizSubmittedSegmentObj;
            quizObj.quizSubmittedQuestion = userQuizSubmittedSegmentObj.quizSubmittedQuestion || [];

            for (let i = 0; i < (quizObj.quizContent || []).length; i += 1) {
                let contentObj = quizObj.quizContent[i];
                if(quizObj && quizObj.quizSubmittedQuestion) {
                    for (let index = 0; index < quizObj.quizSubmittedQuestion.length; index++) {
                        let element = quizObj.quizSubmittedQuestion[index];
                        if (element.questionId && contentObj.id && element.questionId.toString() == contentObj.id.toString()) {
                            // if (element.hasSubmitted) {
                                contentObj.quizSubmittedQuestion = element;
                                contentObj.attempts = await getUserQuesAttemptsFromRedis({ email : session.email, userId , container : quizObj , questionId : contentObj.id, session, quizId : quizObj._id.toString(), userObj } )
                                if ( !contentObj.quizSubmittedQuestion?.hasSubmitted && !(contentObj?.attempts.userAttempts[0]?.attemptData.length ?? 0)) {
                                    delete contentObj.quizSubmittedQuestion;
                                }
                            // }
                            break;
                        }
                    }
                }
            }
        }
	    quizObj.displayname = userObj.displayname || '';
	    quizObj.email = userObj.email || '';
        quizObj.enrollmentId = userObj.enrollmentId ?? "";
        let responseObj = {quiz: quizObj, userQuizSubmittedSegmentObj }
        if (cb)     cb(null, responseObj);
        return responseObj;
    } 
    catch (e) {
        console.log("getSubmittedQuestionDataOfQuiz Error = ", e);
        if (cb)     cb(e);
        return {error: e.message};
    }
}


const getSubmittedQuestionDataOfQuiz = async ( payload, cb ) => {
    const isCB = cb && typeof cb === 'function';
    try {
        const { userId, quizId, isArchived } = payload;
        let obj = {'userId': mongoose.Types.ObjectId(userId), 'quizId': mongoose.Types.ObjectId(quizId) };
        let getUserQuizSubmittedSegment = null;
        if( isArchived === "true" ){
            getUserQuizSubmittedSegment = archivedUserQuizSubmittedSegmentController.getUserQuizSubmittedSegment;
        }else{
            getUserQuizSubmittedSegment = userQuizSubmittedSegmentController.getUserQuizSubmittedSegment;
        }
        const resultStart = getUserQuizSubmittedSegment(obj, {}, {});
        if ( !( resultStart && resultStart.length) ){
            throw new Error('SubmittedQuestion is empty!');
        }
        resultStart = resultStart[0];
        const resultQuiz = await quizController.getOneQuiz({'_id': quizId}, {}, {});
        if (!resultQuiz) {
           
            throw new Error('Error in getting quiz');
        }
        resultQuiz.userQuizSubmittedSegment = resultStart;
        resultQuiz.quizSubmittedQuestion = resultStart.quizSubmittedQuestion;
        resultQuiz.submittedSegId = resultStart._id.toString();
        const userObj = await getOneUser({_id: resultStart.userId}, {email: 1, displayname: 1}, {});
        if ( ! userObj) {
            throw new Error('Error in getting user');
        }
        resultQuiz.displayname = userObj.displayname || '';
        resultQuiz.email = userObj.email || '';
        const result =  await getQuizDataForQuestionAttempt(resultQuiz, {});
                        // result = [result];
        let totalSubmittedQuestions = getSubmittedQuestionsAsQuizContent(resultStart, resultQuiz.quizContent);
        if (isCB) return cb( err ,{'quiz' : resultQuiz, 'userId' : userId,  ...totalSubmittedQuestions   } )
        return {'quiz' : resultQuiz, 'userId' : userId,  ...totalSubmittedQuestions   };
    } catch (error) {
        console.log(`/attemptedQuestion/quiz/:userId/:quizId error in getting quiz ${quizId}`, errQuiz);
        if(isCB) return cb(error?.message);
        throw new Error(error?.message);
    }
}

//TODO this function is required.
const logOutSpecificSession = async function (userId, sid) {
    try {
        if ( ! userId )     throw new Error("User Id is null");
        await services.RedisServices.redis('del', `sess:${sid}`);
        const key = libs.constants.redisKeys.userSessionIdsHash + ':' + userId;
        let isDeleted = await services.RedisServices.sharedRedis('hdel', key, sid);
        return {msg: isDeleted ? "Success" : "Failure"};
    } catch (e) {
        console.log("Error in logOutSpecificSession = ", e);
        return {error: e.message};
    }
}


//TODO this function is required.
const editGlobalVariables = async function (payload = "{}") {
    try {
        console.log("Editing global data")
        payload = JSON.parse( payload )
        Object.keys( payload ).map( ( key )=>{
            let val = payload[key] == "0" || payload[key] == "false" || payload[key] == false || payload[key] == 0 ?
            false : true ;
            console.log(`setting ${key} = ${val}`)
            global[ key ] = val
        });
        return ({ msg : "Global variable succesfully changed"})
    } catch (e) {
        console.error("edit global variable error", e);
        return ({ error: e.message });
    }
}


//TODO this function is required.
const updateQuizActiveInfo = async function( payload = {} ){
    try{
        let { quizId, userId, start, time, userIds } = payload;
        if( !quizId )   throw new Error("quiz id not present");
        // if( !userId )   throw new Error("user id not present");
        console.log("update quiz active info", payload)
        let redisPipeline = redisClient.pipeline()
        redisPipeline
        .sadd(`${redisKeys.activeQuizSet}`, quizId )
        .set(`${redisKeys.quizLastActivityTime}:${quizId}`, time || Date.now() )

        if( userId )
            redisPipeline.sadd(`${redisKeys.joinedUserIds}:${quizId}`, userId)

        if( userIds && userIds.length ){
            userIds.map( id =>{
                redisPipeline.sadd(`${redisKeys.joinedUserIds}:${quizId}`, id )
            })
        }

        if( start ){
            let key = libs.constants.redisKeys.ongoingQuiz + ":" + userId + ":" + quizId;
            redisPipeline.persist( key )
            .persist( `${key}:${redisKeys.submittedQuestions}`)
            .persist( `${key}:${redisKeys.submittedQuesScores}`);
        }
        let result = await redisPipeline.exec();
        return result;
    } catch ( e ){
        console.log("Error while updating quiz active info", e );
        return false;
    }
}


//TODO this function is required.
const getUpdatedDashboardForStudent = async function(payload = { } ){
    try{
        let { userId, quizId } = payload;
        if(!userId)     throw new Error("User id not present")
        if(!quizId)     throw new Error("Quiz id not present")
        let { updatedAt, quizContent, quizSegments, randomizeQuestion, poolQuestion,
             questionId } = libs.constants.quizParamsHMSET;
        let quiz = await quizController.getQuizFromDb({ id : quizId, projections : [
            updatedAt.key, quizContent.key, quizSegments.key, randomizeQuestion.key, poolQuestion.key
        ]});

        // if( new Date( quiz[ updatedAt.key ] ) <= new Date( lastUpdateTime ) ) 
        //     throw new Error("Quiz content is already updated")

        if(!quiz )  throw new Error("Quiz id not valid");

        let resultUserQuiz = await userQuizSubmittedSegmentController.getUserQuizSubmittedSegmentFromDb(
            {  userId, quizId }, { });
        
        if( !resultUserQuiz ){
            throw new Error("User quiz submitted segment not present", req.body )   
        }

        let key = redisKeys.ongoingQuiz + ':' + userId + ":" + quizId;
        if( quiz[ randomizeQuestion.key ] ){
            let questionIds = quiz[ questionId.key] || [];
            let userQuestions = resultUserQuiz.questionId || [], updatedUserQuestions=[];
            let update = false, presentQuestions = {}, startIndex = 0;
						let segmentWiseInfo = { };

            for( let i =0; i < userQuestions.length; i+=1){
                let id = userQuestions[i],isFound = false;

                for( let  j = 0; j < quiz.quizContent.length; j+=1){
                    if( id.toString() == quiz.quizContent[j].id.toString()){
                        found = true;
                        let segmentData = libs.util.getSegmentFromIndexOfContent( quiz.quizSegments, j );
                        segmentWiseInfo[ segmentData.segmentIndex ] =  segmentWiseInfo[ segmentData.segmentIndex ] || [];
                        presentQuestions[ id.toString() ] = 1
                        segmentWiseInfo[ segmentData.segmentIndex ].push( id.toString() )
                        // questionIds.splice(j,1)
                        break;
                    }
                }
                if(!isFound ){
                    update = true;
                }
            }
            for( let i = 0; i < quiz.quizSegments.length; i +=1){
                if(!segmentWiseInfo[i]) {
                    segmentWiseInfo[i] = [];
                }
                let segmentWiseQuestions = segmentWiseInfo[i] || [];
                let segmentObj = quiz.quizSegments[i]
                if( segmentWiseQuestions.length != segmentObj.count ){
                        update = true;

                        let segmentQuestions = quiz.quizContent.slice( startIndex, startIndex + segmentObj.count )
                        segmentQuestions = libs.util.shuffle( segmentQuestions );
                        for( let k =0; (k < segmentObj.count ) && ( segmentWiseQuestions.length != segmentObj.count ); k+=1 ){
                                let id = segmentQuestions[ k ]?.id?.toString();
                                if( id && !presentQuestions[id] ){
                                        segmentWiseQuestions.push(id)
                                }
                        }
                }
                startIndex += segmentObj.count;
            }
            if( update ){
                for( let i = 0; i < quiz.quizSegments.length; i +=1){
                        let segmentWiseQuestions = segmentWiseInfo[i] || [];
                        updatedUserQuestions = updatedUserQuestions.concat( segmentWiseQuestions )
                }  
                await services.RedisServices.redis('hset', key, `questionId`, JSON.stringify(updatedUserQuestions));
                resultUserQuiz.questionId = updatedUserQuestions;  
            }
            return updatedUserQuestions;
        } else if( quiz[ poolQuestion.key ] ){
            let questionIds = quiz[ questionId.key] || [];
            let userQuestions = resultUserQuiz.questionId || [], updatedUserQuestions = [];
            let update = false, presentQuestions = {}, startIndex = 0 ;
            let segmentWiseInfo = { }
            for( let i =0; i < userQuestions.length; i+=1){
                let id = userQuestions[i],isFound = false;

                for( let  j = 0; j < quiz.quizContent.length; j+=1){
                    if( id.toString() == quiz.quizContent[j].id.toString()){
                        found = true;
                        let segmentData = libs.util.getSegmentFromIndexOfContent( quiz.quizSegments, j );
                        segmentWiseInfo[ segmentData.segmentIndex ] =  segmentWiseInfo[ segmentData.segmentIndex ] || [];
                        presentQuestions[ id.toString() ] = 1
                        segmentWiseInfo[ segmentData.segmentIndex ].push( id.toString() )
                        // questionIds.splice(j,1)
                        break;
                    }
                }
                if(!isFound ){
                    update = true;
                }
            }
            for( let i = 0; i < quiz.quizSegments.length; i +=1){
								if(!segmentWiseInfo[i]) {
									segmentWiseInfo[i] = [];
								}
                let segmentWiseQuestions = segmentWiseInfo[i] || [];
                let segmentObj = quiz.quizSegments[i]
                if( segmentWiseQuestions.length != segmentObj.pollNumber ){
                    update = true;
                    if( segmentWiseQuestions.length < segmentObj.pollNumber ){
                        let segmentQuestions = quiz.quizContent.slice( startIndex, startIndex + segmentObj.count )
                        segmentQuestions = libs.util.shuffle( segmentQuestions );
                        for( let k =0; (k < segmentObj.count ) && ( segmentWiseQuestions.length != segmentObj.pollNumber ); k+=1 ){
                            let id = segmentQuestions[ k ].id.toString();
                            if( !presentQuestions[id] ){
                                segmentWiseQuestions.push(id)
                            }
                        }
                    } else {
                        segmentWiseQuestions.splice( segmentObj.pollNumber - segmentWiseQuestions.length, 
                            segmentWiseQuestions.length - segmentObj.pollNumber)
                    }
                }
                startIndex += segmentObj.count;
            }
            if( update ){
                for( let i = 0; i < quiz.quizSegments.length; i +=1){
                    let segmentWiseQuestions = segmentWiseInfo[i] || [];
                    updatedUserQuestions = updatedUserQuestions.concat( segmentWiseQuestions )
                }  
                await services.RedisServices.redis('hset', key, `questionId`, JSON.stringify(updatedUserQuestions));
                resultUserQuiz.questionId = updatedUserQuestions;  
            }
            return updatedUserQuestions;
            
        } 
        return [];
    } catch(e){
        console.log("Error while getting updated dashboard for user",e, payload );
        return null;
    }
}

//TODO this function is required.
const checkTabSwitchAlertLimit = async (payload = {}) => {
    try {
        console.log("checkTabSwitchAlertLimit payload = ", payload);
        const {currentTabSwitchCount = 0, quizId, userId, sessionObj} = payload;
        if (!quizId)    throw new Error("QuizId is null");
        if (!userId)    throw new Error("UserId is null");

        const tabSwitchAlertLimitKeyName = libs.constants.quizParamsHMSET.tabSwitchAlertLimit.key;
        const submitTestOnTabSwitchLimitBreach = libs.constants.quizParamsHMSET.submitTestOnTabSwitchLimitBreach.key;
        const quizObj = await quizController.getQuizFromDb({id: quizId, projections: [tabSwitchAlertLimitKeyName, submitTestOnTabSwitchLimitBreach]});
        if (!quizObj)   throw new Error("QuizObj not found");
        //console.log("QuizObj = ", quizObj);
        const tabSwitchAlertLimit = parseInt(quizObj[tabSwitchAlertLimitKeyName]);
        // console.log("tt = ", tabSwitchAlertLimit, currentTabSwitchCount)
        if (!tabSwitchAlertLimit || parseInt(currentTabSwitchCount) % tabSwitchAlertLimit != 0 )  return ;

        if (quizObj.submitTestOnTabSwitchLimitBreach) {
            console.info(`Submitting quiz for\nUserId: ${userId}\nQuizId: ${quizId} due to tab switch limit breached`);
            await quizController.submitQuizForSingleUser(userId, quizId, 'Test is submitted due to tab switch limit exceeded.', true);
            return;
        }
        const terminationString = libs.util.createTerminationRedisString(
            Date.now(),
            libs.constants.forceLogoutType.dueToTabSwitch,
            sessionObj.session,
        )
        await services.RedisServices.redis('rpush', libs.constants.redisKeys.getTerminationList(quizId, userId), terminationString);
        await logoutUserFromQuiz({userId, quizId, logoutMessage: "Tab switch limit breached"});
        return ;

    } catch (error) {
        console.log("Error in checkTabSwitchAlertLimit ", error);
        return ;
    }
}

//TODO this function is required.
const calculateRemainingTime = async function( req, res ){
    try {
        // controller.QuizController.getRemainingTimeOfQuiz(req.params.quizId, req.session.userId, (errQuiz, result) => {
        const { quizTime, updatedAt } = libs.constants.quizParamsHMSET;
        let result = await quizController.getQuizFromDb({ id: req.params?.quizId, projections: [quizTime.key, updatedAt.key] });

        if (!result) {
            throw new Error('Quiz not present while calculating remainingTime',req.params);
        }
        if( !req?.session?.quizId ) {
            throw new Error("Quiz id not present, invalid request", req.session );
        } else if( req?.session?.quizId != req?.params?.quizId ){
            throw new Error("Invalid access for remaining time", req?.session?.quizId ,req?.params?.quizId );
        }
        let key = libs.constants.redisKeys.ongoingQuiz + ':' + req.session.userId + ':' + req.params.quizId;
        let times = await services.RedisServices.redis("hmget", key,
            [ libs.constants.redisKeys.extraTime, libs.constants.redisKeys.sessionTime,
            libs.constants.redisKeys.userSessions,
            libs.constants.redisKeys.tabSwitchCount,
            libs.constants.redisKeys.tabSwitchInCount,
            libs.constants.redisKeys.fullScreenInCount,
            libs.constants.redisKeys.fullScreenOutCount,
            libs.constants.redisKeys.userId,
            ])
        //  (err, times ) => {
        if (!times.length) {
            throw new Error("unable to get time for this test");
        } else if( !times[7]){
            throw new Error("Invalid remaining time request", times, req.session)
        }
        let extraTime = times[0], sessionTime = times[1];
        let userSessions =  times[2] ? JSON.parse( times[2]) : [];
        let remainingTime = quizController.getRemainingTimeOfQuiz({ extraTime ,  sessionTime, userSessions, quiz : result, key, session : req.session } )
        
        if (isNaN(remainingTime)) {
            console.log("Error while getting remaining time", req.session);
            res.json({ 'error': "unable to get remaining time" });
            return;
        }
        return ({ 'remainingTime': remainingTime, 'extraTime': 0, quizUpdatedAt: result.updatedAt, 
        tabSwitchCount : times[3] || 0,
        tabSwitchInCount : times[4] || 0,
        fullScreenInCount : times[5] || 0,
        fullScreenOutCount : times[6] || 0,
     });
    } catch (e) {
        console.log("Error while getting remaining time", e)
        return { 'remainingTime': -1 };
    }
}

//TODO this function is required.
var getQuizContentInSequence = function (containerObj, startIndex, endIndex, payload ) {
    const { questions, tutorials, quiz, project } = payload;

    let containerContentArray = [], questionsArrayClone, quizArrayClone;
    let tempResultArray = [];
    let containerContent = containerObj.quizContent;
    for (let i = startIndex; i < endIndex && i < containerContent.length; i += 1) {
        containerContentArray.push(containerContent[i].id);
    }

    if (containerObj.attemptInSequence && startIndex != 0) {
        tempResultArray.push({'_id': containerContent[startIndex - 1].id, type : containerContent[startIndex - 1].contentType });
        // tempResultArray[0].isTutorial =
        //     containerContent[startIndex - 1].contentType === contentTypeString.contentTypeTutorial
    }

     questionsArrayClone = questions ?  questions.slice(0) : questions;
     quizArrayClone = quiz ? quiz.slice(0) : quiz;

    containerContentArray.forEach((objectId, i ) => {
        let reorderedObj = {};
        let isFound = false;
        if ( quizArrayClone &&
            containerContent[startIndex].contentType === contentTypeString.contentTypeQuiz ) {
            for (let i = 0; i < quizArrayClone.length; i += 1) {
                if (objectId.equals(quizArrayClone[i]._id)) {
                    isFound = true;
                    reorderedObj = quizArrayClone[i];
                    reorderedObj.type = contentTypeString.contentTypeQuiz
                    quizArrayClone.splice(i, 1);
                    break;
                }
            }
        }
        else if (questionsArrayClone) {
            for (let j = 0; j < questionsArrayClone.length; j += 1) {
                if (objectId.toString() == (questionsArrayClone[j]._id).toString()) {
                    isFound = true;
                    reorderedObj = questionsArrayClone[j];
                    reorderedObj.type = containerContent[ startIndex ].contentType
                    questionsArrayClone.splice(j, 1);
                    break;
                }
            }
        }
        if (isFound)
            tempResultArray.push(reorderedObj);
        startIndex += 1;
    });
    return tempResultArray;
};

module.exports = {    
    removeContentFromCache,
    quizOfUser: quizOfUser,
    calculateRemainingTime,
    editGlobalVariables,
    updateOneUser: updateOneUser,
    updateUsers: updateUsers,
    addNewUser: addNewUser,
    getOneUser: getOneUser,
    getUsersCount: getUsersCount,
    getAttemptsOfQuestion: getAttemptsOfQuestion,
    getAttemptsFromUserAnswerId: getAttemptsFromUserAnswerId,
    getUser: getUser,
    getQuestionsAndTutorialsFromCourseOrQuiz: getQuestionsAndTutorialsFromCourseOrQuiz,
    getQuizDashboard: getQuizDashboard,
    checkLogIn: checkLogIn,
    getQuizDataForQuestionAttempt: getQuizDataForQuestionAttempt,
    getSubmittedQuestionData: getSubmittedQuestionData,
    getLastAttemptOfQuestion: getLastAttemptOfQuestion,
    logoutUser: logoutUser,
    checkBadLoginAttemptForQuiz: checkBadLoginAttemptForQuiz,
    loginForQuiz: loginForQuiz,
    logoutUserFromQuiz: logoutUserFromQuiz,
    getSubmittedQuestionsAsQuizContent: getSubmittedQuestionsAsQuizContent,
    addExtraTime: addExtraTime,
    archiveCourseOrQuiz: archiveCourseOrQuiz,
    moveErrorsToArchive: moveErrorsToArchive,
    getLatestAttemptOfQuestion,
    getLatestAttemptOfQuestionFromDB : getLatestAttemptOfQuestionFromDB,
    addSuperAdminIdToRedis : addSuperAdminIdToRedis,
    getUsersByRole : getUsersByRole,
    getSubmittedQuestionDataOfQuiz,
	logOutSpecificSession,
    getUserQuesAttemptsFromRedis,
    getSubmittedQuestionDataOfQuizFromRedis,
    updateQuizActiveInfo,
    getUpdatedDashboardForStudent,
    setQuizParametersInSessionForQuiz,
    checkTabSwitchAlertLimit,
    getQuestionForQuiz,
    validateInviteToken,
    getUserArrayForFilter,
};


