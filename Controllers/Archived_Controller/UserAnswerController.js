const services = require('../../Services');
const libs = require("../../Lib");
const userAttemptController = require("../../Controllers/UserAttemptController");
const questionController = require("../../Controllers/QuestionController");
// const courseController = require("../delete_CourseController");
// const tutorialController = require("../delete_TutorialController");
const quizController = require("../../Controllers/QuizController");
const userQuizSubmittedSegmentController = require("../../Controllers/UserQuizSubmittedSegmentController");
const mongoose = require('mongoose');
const logger = console

var getUserAnswer = function (criteria, projections, options, callback) {
    return services.ArchivedServices.UserAnswerService.getUserAnswer(criteria, projections, options, callback);
};

var getOneUserAnswer = function (criteria, projections, options, callback) {
    return services.ArchivedServices.UserAnswerService.getOneUserAnswer(criteria, projections, options, callback);
};

var updateUserAnswer = function (criteria, options, updateObj, callback) {
    if (!( criteria && updateObj && options && callback && typeof callback === 'function')) {
        logger.debug("invalid parameters archived updateUserAnswer criteria-", criteria, " updateObj-", updateObj, " options-", options);
        logger.error("invalid parameters archived updateUserAnswer");
        callback({ 'error' : "invalid parameters updateUserAnswer"});
        return;
    }
    services.ArchivedServices.UserAnswerService.updateUserAnswer(criteria, options, updateObj, callback);
};

var performAggregation = function (aggregationArray, callback) {
    services.ArchivedServices.UserAnswerService.aggregate(aggregationArray, callback);
};

var getUserAnswerPagination = function (req, userId, callback) {
    if (req.body.search.value) {
        searchRegex = new RegExp(req.body.search.value, 'i');
        courseId = mongoose.Types.ObjectId(req.body.courseId);
        courseController.getOneCourse( { '_id' : courseId }, { 'questionId' : 1 }, {}, ( errCourse, course)=>{
            if( errCourse ){
                logger.error("Error while finding course ", errCourse, " courseId ", payload );
                callback({ 'error' : "Course not found"});
                return;
            }
            if( !( course && course.questionId && course.questionId.length) ){
                callback( errCourse , { 'data' : []});
                return;
            }
            var payload = {};
            payload.title = searchRegex;
            payload._id = { $in : course.questionId };
            questionController.getQuestion(payload, {'_id': 1}, {}, (error, result) => {
                if (error) {
//error to fetch question list
                    return callback("error to fetch question list");
                } else {
//question list is fetched now find user answers
                    let criteria = {};
                    criteria.questionId = {$in: result};
                    criteria.userId = mongoose.Types.ObjectId(userId);
                    criteria.courseId = mongoose.Types.ObjectId(req.body.courseId);
                    req.body.criteria = criteria;
                    getAnswersofUserWithPagination(req, callback);
                }

            });
        })
    } else {
        req.body.criteria = {
            'userId': mongoose.Types.ObjectId(userId),
            'courseId': mongoose.Types.ObjectId(req.body.courseId), questionId: {$exists: true}
        };
        getAnswersofUserWithPagination(req, callback);

    }
}

var getAnswersofUserWithPagination = function (req, callback) {
    //services.ArchivedServices.UserAnswerService.getUserAnswer(criteria, {}, { sort:{lastActive:"desc"} ,skip: start , limit: length }, callback);
    services.ArchivedServices.Paginationservices.ArchivedServices.serverSidePagination(req, libs.constants.modelForPagination.UserAnswer, (error, result) => {
        if (error) {
            return callback(error);
        }
        else {
            let previousQId  , removingAnswer = [];
            for(let i=0 ; i < result.data.length ; i++){
                let record = result.data[i] ;
                let compilationError = 0, testcaseFailed = 0, successful = 0;
                let question = record.questionId[0];
                if (question.type == libs.constants.questionTypeNumeric.questionTypeCoding || question.type == libs.constants.questionTypeNumeric.questionTypeStepwise) {

                        // record.userattempts.forEach((attemptCollection) => {
                        record.userattempts.attemptData.forEach((attempt) => {
                                if (attempt.userCompilationError)
                                    compilationError += 1;
                                else {
                                    let attemptResult;
                                    if (question.type == libs.constants.questionTypeNumeric.questionTypeCoding)
                                        attemptResult = libs.util.compareTestCaseAndResult(question.questionTypeCoding.testCase, attempt.userOutputCoding);
                                    else {
                                        attemptResult = libs.util.compareTestCaseAndResult(question.questionTypeCoding.testCase, attempt.userOutputCoding, 1);
                                    }
                                    //check success or failure
                                    if (attemptResult && attemptResult.totalTestCases == attemptResult.testCasePassed)
                                        successful += 1;
                                    else testcaseFailed += 1;

                                }
                            });
                        // });

                        record.reportCard = {
                            "testcaseFailed": testcaseFailed,
                            "successful": successful,
                            "compilationError": compilationError
                        };
                }
                else if (question.type == libs.constants.questionTypeNumeric.questionTypeMCQ) {
                      //  record.userattempts.forEach((attemptCollection) => {
                        record.userattempts.attemptData.forEach((attempt) => {
                                if (question.questionTypeMCQ.correctAnswers[0] == attempt.userInputMCQ) successful += 1;
                                else testcaseFailed += 1;
                            });
                       // });

                        record.reportCard = {"testcaseFailed": testcaseFailed, "successful": successful, "compilationError": compilationError};

                }
                delete record.userattempts;
                record.questionId[0] = {_id: question._id, title: question.title, type: question.type};
                // record.courseId[0] = {_id: record.courseId[0]._id, title: record.courseId[0].title};
                if(previousQId && previousQId.toString() == record.questionId[0]._id.toString()){
                    record.reportCard.successful += result.data[i-1].reportCard.successful ;
                    record.reportCard.testcaseFailed += result.data[i-1].reportCard.testcaseFailed ;
                    record.reportCard.compilationError += result.data[i-1].reportCard.compilationError ;
                    removingAnswer.push(i-1) ;
                }
                previousQId = record.questionId[0]._id ;
            }
            let count = 0 ;
            removingAnswer.forEach((remove)=>{
                result.data.splice(remove-count, 1);
                count++ ;
            })
            return callback(null, result);
        }
    });
};

var getDocCount = function ( criteria, callback) {
    services.ArchivedServices.UserAnswerService.getDocCount( criteria, callback )
}

module.exports = {
    getUserAnswer: getUserAnswer,
    getOneUserAnswer: getOneUserAnswer,
    updateUserAnswer: updateUserAnswer,
    performAggregation: performAggregation,
    getUserAnswerPagination: getUserAnswerPagination,
    getDocCount : getDocCount
};
