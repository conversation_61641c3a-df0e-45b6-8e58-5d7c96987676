const services = require('../../Services');
const libs = require("../../Lib");
const mongoose = require('mongoose');
const logger = console;

var getUserQuizSubmittedSegment = function (criteria, projections, options, callback) {
    if (!( criteria && projections && options && callback && typeof callback === 'function')) {
        libs.logger.debug("getUserQuizSubmittedSegment archived invalid parameters criteria-", criteria, " projections-", projections, " options-", options);
        libs.logger.error("getUserQuizSubmittedSegment archived");
        callback({ 'error' : "invalid parameters getOneUserQuizSubmittedSegment"});
        return;
    }
    return services.ArchivedServices.UserQuizSubmittedSegmentService.getUserQuizSubmittedSegment(criteria, projections, options, callback);
};

var getOneUserQuizSubmittedSegment = function (criteria, projections, options, callback) {
    if (!( criteria && projections && options && callback && typeof callback === 'function')) {
        logger.debug("invalid parameters archived getOneUserQuizSubmittedSegment criteria-", criteria, " projections-", projections, " options-", options);
        logger.error("invalid parameters archived getOneUserQuizSubmittedSegment");
        callback({ 'error' : "invalid parameters getOneUserQuizSubmittedSegment"});
        return;
    }
    services.ArchivedServices.UserQuizSubmittedSegmentService.getOneUserQuizSubmittedSegment(criteria, projections, options, callback);
};

var updateUserQuizSubmittedSegment = function (criteria, updateObj, options, callback) {
    if (!( criteria && updateObj && options && callback && typeof callback === 'function')) {
        logger.debug("invalid parameters archived updateUserQuizSubmittedSegment criteria-", criteria, " updateObj-", updateObj, " options-", options);
        logger.error("invalid parameters archived updateUserQuizSubmittedSegment");
        callback({ 'error' : "invalid parameters updateUserQuizSubmittedSegment"});
        return;
    }
    services.ArchivedServices.UserQuizSubmittedSegmentService.updateUserQuizSubmittedSegment(criteria, updateObj, options, callback);
};

var performAggregation = function (aggregationArray, callback) {
    services.ArchivedServices.UserQuizSubmittedSegmentService.aggregate(aggregationArray, callback);
    
};

var addUserQuizSubmittedSegment = function (obj, callback) {
    services.ArchivedServices.UserQuizSubmittedSegmentService.addUserQuizSubmittedSegment(obj, callback);
};

var getDocCount = function ( criteria, callback) {
    services.ArchivedServices.UserAnswerService.getDocCount( criteria, callback )
};

module.exports = {
    getUserQuizSubmittedSegment: getUserQuizSubmittedSegment,
    getOneUserQuizSubmittedSegment: getOneUserQuizSubmittedSegment,
    updateUserQuizSubmittedSegment: updateUserQuizSubmittedSegment,
    performAggregation: performAggregation,
    addUserQuizSubmittedSegment: addUserQuizSubmittedSegment,
    getDocCount : getDocCount
}