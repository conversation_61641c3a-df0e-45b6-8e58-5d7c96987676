const services = require('../../Services');
const libs = require("../../Lib");
const logger = console
const mongoose = require('mongoose');
const ejs = require('ejs');

var updateUserAttemptInDb = function (criteria, updateObj, options, callback) {
    services.ArchivedServices.UserAttemptService.updateUserAttempt(criteria, updateObj, options, callback);
};

var getUserAttempt = function (criteria, projections, options, callback) {
    return services.ArchivedServices.UserAttemptService.getUserAttempt(criteria, projections, options, callback);
};

var getOneUserAttempt = function (criteria, projections, options, callback) {
    return services.ArchivedServices.UserAttemptService.getOneUserAttempt(criteria, projections, options, callback);
};

var createUserAttemptInDb = function (userAttempt, callback) {
    services.ArchivedServices.UserAttemptService.addUserAttempt(userAttempt, callback);
};

var getUserAttemptFromUserAnswerId = function (userAnswerId, callback) {

    if (!( userAnswerId && callback && typeof callback === 'function' )) {
        logger.error(" arguments are invalid----", "userAnswerId-", userAnswerId);
        callback(" arguments are invalid ", null);
        return;
    }
    services.ArchivedServices.UserAttemptService.getUserAttempt({"userAnswerId": userAnswerId}, {}, {}, callback);
};

var getDistinctUserAttempts = function (criteria, groupFields, callback) {
    services.ArchivedServices.UserAttemptService.getDistinctUserAttempts(criteria, groupFields, callback);
};

var performAggregation = function (aggregationArray, callback) {
    services.ArchivedServices.UserAttemptService.aggregate(aggregationArray, callback);
};

var getDocCount = function ( criteria, callback) {
    services.ArchivedServices.UserAttemptService.getDocCount( criteria, callback )
};

module.exports = {
    getUserAttempt: getUserAttempt,
    getOneUserAttempt: getOneUserAttempt,
    updateUserAttemptInDb: updateUserAttemptInDb,
    createUserAttemptInDb: createUserAttemptInDb,
    getUserAttemptFromUserAnswerId: getUserAttemptFromUserAnswerId,
    getDistinctUserAttempts: getDistinctUserAttempts,
    performAggregation : performAggregation,
    getDocCount : getDocCount
};
