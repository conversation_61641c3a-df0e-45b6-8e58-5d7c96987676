const services = require("../../Services");
const libs = require("../../Lib");
const userAttemptController = require('../../Controllers/UserAttemptController');
const questionController = require('../../Controllers/QuestionController');
const logger = console

var getUserAttemptSnapshot = function (criteria, projections, options, callback) {
    services.ArchivedServices.UserAttemptSnapshotService.getUserAttemptSnapshot(criteria, projections, options, callback);
};

var getOneUserAttemptSnapshot = function (criteria, projections, options, callback) {
    services.ArchivedServices.UserAttemptSnapshotService.getOneUserAttemptSnapshot(criteria, projections, options, callback);
};

var updateUserAttemptSnapshotInDb = function (criteria, updateObj, options, callback) {
    services.ArchivedServices.UserAttemptSnapshotService.updateUserAttemptSnapshot(criteria, updateObj, options, callback);
};

var createUserAttemptSnapshotInDb = function (userAttempt, callback) {
    services.ArchivedServices.UserAttemptSnapshotService.createUserAttemptSnapshot(userAttempt, callback);
};

var userAttemptSnapshotFromAttemptId = function (userAttemptId, callback) {

    if (!( userAttemptId && callback && typeof  callback === 'function' )) {
        logger.error(" arguments are invalid userAttemptSnapshotFromAttemptId----", "userId-", userAttemptId);
        callback(" arguments are invalid ", null);
        return;
    }

    getOneUserAttemptSnapshot({userAttemptId: userAttemptId}, {}, {}, (error, result) => {
        if (error) {
            logger.error(" error in getting userattemptsnapshot ----", "userAttemptId-", userAttemptId);
            callback(error, null);
        }
        else {
            logger.error(" user Attempted Snapshotof  questions are--", result);
            callback(error, result);
        }
    });

};

var getDocCount = function ( criteria, callback) {
    services.ArchivedServices.UserAttemptSnapshotService.getDocCount( criteria, callback )
};

module.exports = {
    getUserAttemptSnapshot: getUserAttemptSnapshot,
    getOneUserAttemptSnapshot: getOneUserAttemptSnapshot,
    userAttemptSnapshotFromAttemptId: userAttemptSnapshotFromAttemptId,
    getDocCount : getDocCount
}
