const libs = require("../Lib");
const config = require('../config');
const services = require('../Services');
const UserController = require("../Controllers/UserController");
const QuizController = require("../Controllers/QuizController");

const createInviteToken = (quizId, emailId, enrollmentId) => {
    return quizId && emailId && libs.util.encrypt(`${quizId}-${emailId}-${enrollmentId ?? ''}`);
}

const createInviteLink = (host, inviteToken, quizId, emailId) => {
    if ( ! inviteToken )    inviteToken = createInviteToken(quizId, emailId);
    const inviteHost = libs.constants.inviteHost[host] ?? host
    return `${process.env.isHttps?'https':'http'}://${inviteHost}/test/invite/${inviteToken}`;
}

const addQuizInviteesEmails = async function(payload) {
    let error;
    try {
        let {quizId, emailIds = '', mailContent, mailSubject, expireTime, sessionObj, hostname} = payload;
        emailIds = emailIds.split(',');

        const isUserCreationAllowed = config.featuresEnabled.createAccountOnTestInvite;
        
        if ( ! quizId )             throw new Error("Quiz Id is null");
        if ( ! emailIds )           throw new Error("EmailIds is null");
        if ( ! mailContent )        throw new Error("Mail Content is null");
        if ( ! mailSubject )        throw new Error("Mail Subject is null");
        if ( ! emailIds.length )    throw new Error("EmailIds array is of zero length");

        const emailLeft = await libs.util.dailyEmailCountLeft(sessionObj.orgId);
        if (emailLeft <= 0) throw new Error(libs.messages.errorMessage.dailyLimitOfEmail);

        let quizObj = await QuizController.getOneQuiz({_id: quizId}, {}, {});
        if ( ! quizObj )    throw new Error("Quiz not found");
        const superAdminId = await libs.util.getSuperAdminIdFromRedis();
        if (quizObj.orgId.toString() !== superAdminId && config.featuresEnabled.inviteCountForQuiz) {
            const orgObj = await UserController.getOneUser({_id: quizObj.orgId, status: libs.constants.displayStatus.active},  {invitesLeft: 1, negativeThreshold: 1}, {});
            if (!orgObj) throw new Error('Creator org not found');
            if (orgObj.invitesLeft <= 0) {
                QuizController.sendInvitesLeftZeroMailToOrg(quizObj.orgId, 'Your Invites left Zero. Please add invites. Thank You');
            }
            if (orgObj.invitesLeft + orgObj.negativeThreshold <= 0 ) {
                throw new Error('Invites finished, Abort invite user');
            }
        }
        // let quizObj = await  QuizController.checkInvitesAndGetQuiz(quizId, emailIds.length, sessionObj);

        if (quizObj.endTime) {
            const currentDate = new Date();
            if (quizObj.endTime < currentDate) {
                throw new Error("Quiz has been ended");
            } 
        }

        if(quizObj.endTime && expireTime) {
            const expireDate = new Date(expireTime);
            if(quizObj.endTime < expireDate) {
                throw new Error("Expire time can not be greater than the test end time");
            }
        }

        userEmailIdsObj = {};
        let userIdsObjArr = await services.UserService.getUser({email: {$in: emailIds}}, {email: 1, displayname: 1, enrollmentId: 1}, {});
        userIdsObjArr.map(userObj => userEmailIdsObj[userObj.email] = {
            'displayname': userObj.displayname,
            'rollNumber': userObj.enrollmentId,
        })
        const errorWhileCreatingUser = [];

        let emailNotAbleToBeInserted = {};
        let emailIdsNotExist = emailIds.filter(emailId => ! (emailId in userEmailIdsObj) );

        if (!isUserCreationAllowed) {
            emailIdsNotExist.forEach(email => emailNotAbleToBeInserted[email] = 1);
        }

        if ( emailIdsNotExist.length && isUserCreationAllowed ) {
            for (let index = 0; index < emailIdsNotExist.length; index++) {
                try{
                    const emailId = emailIdsNotExist[index];
                    const name = libs.util.createNameFromEmail(emailId);
                    if (!name) throw new Error(`Not able to create a valid name for ${emailId}`);
                    const userObj = {
                        stemail: emailId,
                        stname: name,
                        role: libs.constants.roleNumberFromString.user,
                    }
                // TODO - make addNewUser async and handle error
                    await UserController.addNewUser(userObj,sessionObj, {
                        hostname
                    });
                    userEmailIdsObj[emailId] = {'displayname': name, 'rollNumber': ''}
                }
                catch(err){
                    errorWhileCreatingUser.push(err?.message ?? err);
                    emailNotAbleToBeInserted[emailIdsNotExist[index]] = 1;
                }
            }
        }

        let countOfEmailNotAbleToBeInserted = 0;
        emailIds = emailIds.filter((element)=>{
            if(emailNotAbleToBeInserted[element] == 1){
                countOfEmailNotAbleToBeInserted++;
                return false;
            }
            return true;
        });

        if(countOfEmailNotAbleToBeInserted != 0 ){
            error = `${countOfEmailNotAbleToBeInserted} student invite failed.`;
        }

        let inviteesEmailsAdded = [];
        let emailAlreadyAdded = [];
        let emailPresentInAllowedList = [];

        let quizAllowedEmailsObj = await services.QuizAllowedEmailsService.getOneQuizAllowedEmails({quizId}, {}, {});

        if ( ! quizAllowedEmailsObj ) {
            let obj = {
                quizId,
                invitees: ( emailIds || [] ).map(emailId => {
                    let token = createInviteToken(quizId, emailId, userEmailIdsObj[emailId]?.rollNumber ?? '');
                    userEmailIdsObj[emailId].token = token;
                    const obj = {
                        email: emailId,
                        token,
                    }
                    if (expireTime) {
                        obj.validTill = new Date(expireTime)
                    }
                    return obj;
                })
            }
            await services.QuizAllowedEmailsService.addQuizAllowedEmails(obj);
            inviteesEmailsAdded = emailIds;
        }
        else {
            let quizAllowedEmailIds = quizAllowedEmailsObj.emails || [];
            let quizInviteesEmailIds = ( quizAllowedEmailsObj.invitees || [] ).map(obj => obj.email);
            let quizInvitesObjArr = [];

            for (let index = 0; index < emailIds.length; index++) {
                const emailId = emailIds[index];
                if (quizAllowedEmailIds.indexOf(emailId) != -1) {
                    emailPresentInAllowedList.push(emailId);
                }

                let token = createInviteToken(quizId, emailId, userEmailIdsObj[emailId]?.rollNumber ?? '');
                userEmailIdsObj[emailId].token = token;

                if (quizInviteesEmailIds.indexOf(emailId) != -1) {
                    emailAlreadyAdded.push(emailId);
                    // update validTill in DB
                    if (expireTime) {
                        await services.QuizAllowedEmailsService.updateQuizAllowedEmails({quizId, 'invitees.email': emailId }, {$set: {'invitees.$.validTill': new Date(expireTime)}}, {});
                    } else {
                        await services.QuizAllowedEmailsService.updateQuizAllowedEmails({quizId, 'invitees.email': emailId }, {$unset: {'invitees.$.validTill': 1}}, {});
                    }
                    continue;
                }
                const obj  =  {'email': emailId, token};
                if (expireTime) {
                    obj.validTill = new Date(expireTime)
                }
                quizInvitesObjArr.push(obj);
                //let updateObj = {$push: {invitees: {'email': emailId, 'validTill': new Date(expireTime), token}}};
                //await services.QuizAllowedEmailsService.updateQuizAllowedEmails({quizId}, updateObj, {});
                inviteesEmailsAdded.push(emailId);
            }
            if (quizInvitesObjArr.length) {
                const updateObj = {$push: {invitees: {$each: quizInvitesObjArr}}};
                if (emailPresentInAllowedList.length)   updateObj['$pullAll'] = {emails: emailPresentInAllowedList};
                await services.QuizAllowedEmailsService.updateQuizAllowedEmails({quizId}, updateObj, {});
            }
        }
        inviteesEmailsAdded = inviteesEmailsAdded.concat(emailAlreadyAdded);
        mailContent = libs.util.changeEmailTemplateToHtml(mailContent)
        for (let index = 0; index < inviteesEmailsAdded.length; index++) {
            const emailId = inviteesEmailsAdded[index];
            
            if ( ! userEmailIdsObj[emailId] )   continue;
            const {displayname, token, rollNumber} = userEmailIdsObj[emailId];
            let content = libs.util.parseEmailTemplate1(mailContent, {name: displayname, testInviteLink: createInviteLink(hostname, token), testTitle: quizObj.title, testDurationInMins: `${quizObj.quizTime} Mins`, rollNumber: rollNumber ?? '', senderName: sessionObj.displayname, senderEmail: sessionObj.email}, true,);
            const senderEmail = libs.util.getSenderEmailFromHostName(hostname);
            await libs.util.sendEmailDB(emailId, mailSubject, content, true, null, libs.constants.emailTypes.TestInvite, sessionObj, senderEmail);
        }
        return {data: { inviteesEmailsAdded, emailAlreadyAdded,error, errorWhileCreatingUser }};
    } catch (e) {
        console.log("addQuizInviteesEmails, Error = ", e);
        return {'error': e.message};
    }
}

const updateQuizAllowedEmailsInRAM = async(payload) => {
    try {
        if ( typeof(payload) == "string" )  payload = JSON.parse(payload);

        const {quizId, allowedEmailIds = []} = payload;
        if ( ! quizId )     throw new Error("QuizId is null");

        const {processMapName} = libs.constants.cachingKeys.quizAllowedEmailIds;
        if ( ! global[processMapName] )     global[processMapName] = {};

        global[processMapName][quizId] = allowedEmailIds;
        return ;
    } catch (error) {
        console.log("Error in updateQuizAllowedEmailsInRAM, ", error);
        return ;
    }
}

const addQuizAllowedEmailIds = async (payload) => {
    try {
        let { quizId, allowedEmailIds = [] } = payload || {};

        if ( ! quizId )     throw new Error("QuizId is null");
        if ( ! allowedEmailIds.length )   return ;

        let updateObj = { $addToSet: { emails: { $each: allowedEmailIds } } };

        let quizEmailsAllowedObj = await services.QuizAllowedEmailsService.updateQuizAllowedEmails({quizId}, updateObj, {upsert: true});
        allowedEmailIds = ( quizEmailsAllowedObj && quizEmailsAllowedObj.emails ) || [];

        services.RedisServices.redis('publish', libs.constants.redisKeys.quizAllowedEmailIdsChannel, JSON.stringify({ 
            quizId,
            type: libs.constants.cachingKeys.quizAllowedEmailIds.type,
            allowedEmailIds,
        }))

        return ;
    } catch (error) {
        console.log("Error addQuizAllowedEmailIds = ", error);
        return ;
    }
}

module.exports = {
    addQuizInviteesEmails,
    createInviteLink,
    updateQuizAllowedEmailsInRAM,
    addQuizAllowedEmailIds,
}
