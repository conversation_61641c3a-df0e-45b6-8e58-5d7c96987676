const mongoose = require('mongoose');
const ejs = require('ejs');
const dayjs = require('dayjs');

const Services = require('../Services');
const libs = require('../Lib');


const save = (objToSave) => {
    return Services.StreamRoomService.save(objToSave);
}

const getRoom = (criteria, projection, options) => {
    return Services.StreamRoomService.getStreamRoom(criteria, projection, options);
}

const checkIfNameAvailable = async (name, id) => {
    const room =  await getRoom({
        _id: {$ne: id},
        roomName: name,
    });
    return !room;
}

const getUserRoom = async (quizId, userId) => {
    try {
        const key = libs.constants.redisKeys.getLiveStreamRoomKey(quizId, userId);
        const roomNames = await Services.RedisServices.redis('smembers', key);
        if (roomNames.length) {
            return roomNames;
        }
        const streamRoom = await  Services.StreamRoomService.getStreamRoom({
            quizId: quizId,
            userIds: userId,
            displaystatus: libs.constants.displayStatus.active,
        }, {roomName: 1}, {});
        const rooms = [
            libs.util.createDefaultRoom(quizId, userId),
            quizId.toString(),
        ]
        if (streamRoom) {
            rooms.push(streamRoom.roomName);
        }
        await Services.RedisServices.redis('sadd', key, rooms);
        return rooms;
    } catch (error) {
        console.log(error);
        throw error;
    }
}

const getAllStreamRoomOfQuiz = async (quizId) => {
    const allLiveStreamRoom = await Services.StreamRoomService.getStreamRooms({
        quizId: quizId,
        displaystatus: libs.constants.displayStatus.active,
    });
    if (!allLiveStreamRoom?.length) {
        return  {rooms: [], userData: {}};
    }
    const setOfUsersToGet = new Set();
    allLiveStreamRoom.forEach((room) => {
        room.userIds.forEach((userId) => {
            if (userId){
                setOfUsersToGet.add(userId);
            }
        });
        room.invigilatorIds.forEach((invigilatorId) => {
            if (invigilatorId) {
                setOfUsersToGet.add(invigilatorId);
            }
        });
    });

    const users = await Services.UserService.getUser({
        _id: Array.from(setOfUsersToGet)
    }, {
        displayname: 1,
        email: 1,
        enrollmentId: 1,
    }, {});
    
    const userDataObj = {};
    users.forEach((user) => {
        userDataObj[user._id.toString()] = user;
    })

    return {rooms: allLiveStreamRoom, userData: userDataObj}
}

/**
 * 
 * @param {{id: string, userEmails: []string, invigilatorEmails: []string, roomName: string}} roomData 
 * @param {{[key: string]: string}} sessionObj
 * @param {string} hostname
 */
const updateRoomData = async (roomData, sessionObj, hostname) => {
    let quizObj = null;
    const setOfAlreadyAddedInvigilators = new Set();
    const instructorIds = [];
    const previousUserIdsSet = new Set();

    if (roomData.id) {
        const room = await getRoom({_id: roomData.id}, {}, {});
        for (let userId of room.userIds) {
            previousUserIdsSet.add(userId.toString());
        }
        if (room.roomName === roomData.roomName) {
            (room.invigilatorIds ?? []).forEach((invigilatorId) => {
                setOfAlreadyAddedInvigilators.add(invigilatorId.toString());
            });
        }
        if (!room) {
            throw new Error('Room not found');
        }
        quizObj = await Services.QuizService.updateQuiz(
            {
                _id: room.quizId,
            },
            {
                $pull: {
                    'usersInLiveStreamRoom': { $in: room.userIds }
                }
            }, {}
        );
    } else if (roomData.quizId) {
        quizObj = await Services.QuizService.getOneQuiz(
            {
                _id: roomData.quizId,
            },
            {}, {}
        );
    };

    if (!quizObj) throw new Error('Quiz not found');
    const isAvailable = await checkIfNameAvailable(roomData.roomName, roomData.id);
    if (!isAvailable) throw new Error('Room Name already in use');

    const usersAlreadyPresent = new Set();
    (quizObj.usersInLiveStreamRoom ?? []).forEach(
        (userId) => usersAlreadyPresent.add(userId.toString())
    );
    
    const userIdsFromEmail = await Services.UserService.getUser({
        email: roomData.userEmails,
        '$or': [
            {role: libs.constants.roleNumberFromString.user},
            {role: libs.constants.roleNumberFromString.mentor},
        ]
    }, {_id: 1}, {});

    const newUserArray = [];
    userIdsFromEmail.forEach((userData) => {
        if (!usersAlreadyPresent.has(userData._id.toString())) {
            newUserArray.push(userData._id);
            usersAlreadyPresent.add(userData._id.toString())
        }
    });

    const setOfInstructorIds = new Set();
    const arrayOfInstructorWhereEmailNeedToBeSent = [];

    const instructorFindObj = {
        email: roomData.invigilatorEmails,
        role: {$ne: libs.constants.roleNumberFromString.user},
    }

    switch (sessionObj.role) {
        case libs.constants.roleNumberFromString.admin: {
            break;
        }
        case libs.constants.roleNumberFromString.superOrg: {
            instructorFindObj.orgId = [...sessionObj.orgIdsAssigned, sessionObj.orgId];
            break;
        }
        case libs.constants.roleNumberFromString.subAdmin: {
            instructorFindObj.orgId = sessionObj.orgId;
            break;
        }
        case libs.constants.roleNumberFromString.custom: {
            if (session.roleObj.containsOrg) {
                instructorFindObj.orgId = sessionObj.orgIdsAssigned
            }
        }
        default: {
            instructorFindObj.orgId = sessionObj.orgId;
        }
    }

    const instructorsData = await Services.UserService.getUser(instructorFindObj, {id: 1, email: 1}, {});

    (instructorsData ?? []).forEach((userData) => {
        if (!setOfInstructorIds.has(userData._id.toString()) && !usersAlreadyPresent.has(userData._id.toString())) {
            instructorIds.push(userData._id)
            if (!setOfAlreadyAddedInvigilators.has(userData._id.toString())) {
                arrayOfInstructorWhereEmailNeedToBeSent.push(userData.email);
            }
            setOfInstructorIds.add(userData._id);
        }
    });

    await Services.QuizService.updateQuiz({_id: quizObj._id}, {
        $addToSet: {
            'usersInLiveStreamRoom': {$each: newUserArray},
        }
    });

    let result;
    if (!roomData.id) {
        const saveObj = {
            roomName: roomData.roomName,
            quizId: quizObj._id,
            createdBy: sessionObj.userId,
            updatedBy: sessionObj.userId,
            orgId: sessionObj.orgId,
            userIds: newUserArray,
            invigilatorIds: instructorIds,
        }
        result = await Services.StreamRoomService.save(saveObj);
    } else {

        const updateObj = {
            $set: {
                roomName: roomData.roomName,
                userIds: newUserArray,
                invigilatorIds: instructorIds,
            },
            $setOnInsert: {
                orgId: sessionObj.orgId,
                quizId: quizObj._id,
                createdBy: sessionObj.userId,
                updatedBy: sessionObj.userId,
                
            }
        }
        result = await Services.StreamRoomService.updateRoom({_id: roomData.id}, updateObj, {});
    }
    const redisPipe = Services.RedisServices.getRedisInstance('redis').pipeline();

    const usersWhoseCacheNeedToBeDeleted = [];
    const newUserSet = new Set(newUserArray);
    newUserSet.forEach((userId) => {
        if (!previousUserIdsSet.has(userId.toString())) {
            usersWhoseCacheNeedToBeDeleted.push(userId.toString());
        }
    });
    previousUserIdsSet.forEach((userId) => {
        if (!newUserSet.has(userId.toString())) {
            usersWhoseCacheNeedToBeDeleted.push(userId.toString());
        }
    })

    usersWhoseCacheNeedToBeDeleted.forEach((userId) => {
        const key = libs.constants.redisKeys.getLiveStreamRoomKey(result.quizId.toString(), userId);
        redisPipe.del(key);
    })
    await redisPipe.exec();
    sendLiveStreamEmailForInvigilator({
        roomName: roomData.roomName,
        invigilatorEmailArray: arrayOfInstructorWhereEmailNeedToBeSent,
        hostname: hostname,
        session: sessionObj,
    })
    return result;
}

/**
 * 
 * @param {string} quizId 
 * @param {string} roomName 
 * @param {Array<string>} userIds 
 * @param {Array<string>} invigilatorIds 
 * @param {Map<string, {email: string, _id: string>}} userMap 
 */
const addUserToRoom = async (quizId, roomName, userIds, invigilatorIds, userMap,
    {
        hostname,
        sessionObj,
    }
) => {
    const usersAlreadyPresentInRoom = await Services.StreamRoomService.updateRooms({
        quizId: quizId,
        roomName: {$ne: roomName},
    }, {
        $pull: {
            userIds: [...userIds, ...invigilatorIds],
        },
    });
    const room = await Services.StreamRoomService.getStreamRoom({
        roomName: roomName,
    }, {}, {});
    let updatedRoom = null;
    const invigilatersWhereEmailHaveToBeSent = new Set(invigilatorIds ?? []);
    if (!room) {
        updatedRoom = await Services.StreamRoomService.save({
            roomName: roomName,
            quizId: quizId,
            createdBy: sessionObj.userId,
            updatedBy: sessionObj.userId,
            orgId: sessionObj.orgId,
            userIds: userIds,
            invigilatorIds: invigilatorIds,
        });
    } else  {
        room.invigilatorIds.forEach((ele) => {
            invigilatersWhereEmailHaveToBeSent.delete(ele.toString());
        });
        updatedRoom = await Services.StreamRoomService.updateRoom({
            _id: room._id
        }, {
            $addToSet: {
                userIds: {$each: userIds},
                invigilatorIds: {$each: invigilatorIds},
            }
        }, {});
    }
    const invigilatorEmailArray = [];
    invigilatersWhereEmailHaveToBeSent.forEach((invId) => {
        const email = userMap.get(invId)?.email;
        if (email) {
            invigilatorEmailArray.push(email);
        }
    });
    const redisPipe = Services.RedisServices.getRedisInstance('redis').pipeline();
    invigilatorIds.forEach((userId) => {
        const key = libs.constants.redisKeys.getLiveStreamRoomKey(updatedRoom.quizId.toString(), userId);
        redisPipe.del(key);
    });
    userIds.forEach((userId) => {
        const key = libs.constants.redisKeys.getLiveStreamRoomKey(updatedRoom.quizId.toString(), userId);
        redisPipe.del(key);
    })
    await redisPipe.exec();
    sendLiveStreamEmailForInvigilator({
        roomName: roomName,
        invigilatorEmailArray: invigilatorEmailArray,
        hostname: hostname,
        session: sessionObj,
    })
}

/**
 * @param {string} roomId
 * @param {{[key: string]: string}} sessionObj
 */
const deleteRoom = async (roomId, sessionObj) => {
    const roomObj = await Services.StreamRoomService.getStreamRoom({_id: roomId}, {}, {});
    if (!roomObj) throw new Error('Room not found');
    
    if (roomObj.userIds?.length) {
        await Services.QuizService.updateQuiz({_id: roomObj.quizId}, {
            $pullAll: {
                'usersInLiveStreamRoom' : roomObj.userIds,
            }
        })
    };
    return Services.StreamRoomService.updateRoom(
        {_id: roomId},
        {
            $set: {
                'roomName': roomObj.roomName + '#deleted' + Date.now(),
                'userIds': [],
                'deletedBy': sessionObj.userId,
                'displaystatus': libs.constants.displayStatus.deleted
            }
        }
    );
}

/**
 * 
 * @param {{invigilatorEmailArray: []string, roomName: string, hostname: string, session: {[key: string]: string}}} config 
 */
const sendLiveStreamEmailForInvigilator = async (config) => {
    try {
        const {roomName} = config;
        const roomObj = await Services.StreamRoomService.getStreamRoom({roomName: roomName}, {}, {});
        if (!roomObj) {
            throw new Error(`Room Not found with name: ${roomName}`);
        }
        const quizObj = await  Services.QuizService.getOneQuiz({_id: roomObj.quizId}, {}, {});
        if (quizObj.startTime) {
            quizObj.startTime = `${dayjs(quizObj.startTime).add(5,'hours').add(30,'minutes').format('DD MMM YYYY hh:mm:ss a')} IST`
        }
        if (quizObj.endTime) {
            quizObj.endTime = `${dayjs(quizObj.endTime).add(5,'hours').add(30,'minutes').format('DD MMM YYYY hh:mm:ss a')} IST`
        }
        if (!quizObj) {
            throw new Error('Quiz Obj Not Found');
        }
        const roomUrl = `${libs.util.getHostLink(config.hostname)}/meeting/rooms/${roomObj._id}`;
        const senderEmail = libs.util.getSenderEmailFromHostName(config.hostname);
        const usersData = await Services.UserService.getUser({_id: roomObj.userIds}, {displayname: 1, email: 1, enrollmentId: 1}, {});
        for (let invigilatorEmails of config.invigilatorEmailArray) {
            try {
                await libs.util.sendEmailDB(invigilatorEmails,
                    libs.constants.emailContent.invigilatorInvite.subject,
                    ejs.render(libs.constants.emailContent.invigilatorInvite.content, {
                        meetJoinUrl: roomUrl,
                        quiz: quizObj,
                        users: usersData,
                        ...libs.constants.mailConfig,
                    })
                ,true, null, libs.constants.emailTypes.InvigilatorInvite, config.session, senderEmail)
            } catch (error) {
                console.log(error);
            }
        }
    } catch (error) {
        console.log(error);
    }
}

/**
 * 
 * @param {[key: string]: string} criteria
 * @param {[key: string]: string} projection
 * @param {[key: string]: string} options 
 * @param {{[key: string]: string}} session 
 */
const getRoomObjAccordingToPermission = (criteria, projection, options, session) => {
    const finalFindObj = {
        ...criteria,
        $or: []
    }
    if (session.role === libs.constants.roleNumberFromString.user) {
        throw new Error('User Not allowed to join room as instructor');
    }
    const findObj = finalFindObj.$or;
    switch(session.role) {
        case libs.constants.roleNumberFromString.admin: {
            break;
        }
        case libs.constants.roleNumberFromString.superOrg: {
            findObj.push({
                'orgId': [...session.orgIdsAssigned, session.orgId],
            })
        }
        case libs.constants.roleNumberFromString.subAdmin: {
            findObj.push({
                'orgId': session.orgId,
            })
        }
        case libs.constants.roleNumberFromString.custom: {
            if (session.roleObj.containsOrg) {
                findObj.push({
                    'orgId': session.orgIdsAssigned,
                })
            }
        }
        default: {
            findObj.push({
                invigilatorIds: mongoose.Types.ObjectId(session.userId),
            })
        }
    };
    findObj.push({
        'createdBy': mongoose.Types.ObjectId(session.userId)
    })
    finalFindObj.$or = findObj;
    return getRoom(finalFindObj, projection, options);
};

module.exports = {
    save,
    getRoom,
    deleteRoom,
    getUserRoom,
    addUserToRoom,
    updateRoomData,
    checkIfNameAvailable,
    getAllStreamRoomOfQuiz,
    getRoomObjAccordingToPermission,
}
