const services = require('../Services');
const libs = require("../Lib");
const mongoose = require('mongoose');
const logger = console

var getBadActivity = function (criteria, projections, options, callback) {
    if (!( criteria && projections && options && callback && typeof callback === 'function')) {
        logger.debug("invalid parameters getBadActivity criteria-", criteria, " projections-", projections, " options-", options);
        logger.error("invalid parameters getBadActivity criteria-");
        callback({ 'error' : "invalid parameters getOneBadActivity"});
        return;
    }
    services.BadActivityService.getBadActivity(criteria, projections, options, callback);
};

var getOneBadActivity = function (criteria, projections, options, callback) {
    if (!( criteria && projections && options && callback && typeof callback === 'function')) {
        logger.debug("invalid parameters getOneBadActivity criteria-", criteria, " projections-", projections, " options-", options);
        logger.error("invalid parameters getOneBadActivity criteria-");
        callback({ 'error' :"invalid parameters getOneBadActivity"});
        return;
    }
    services.BadActivityService.getOneBadActivity(criteria, projections, options, callback);
};

var updateBadActivity = function (criteria, updateObj, options, callback) {
    if (!( criteria && updateObj && options && callback && typeof callback === 'function')) {
        logger.debug("invalid parameters updateBadActivity criteria-", criteria, " updateObj-", updateObj, " options-", options);
        logger.error("invalid parameters updateBadActivity criteria-");
        callback({ 'error' : "invalid parameters updateBadActivity"});
        return;
    }
    services.BadActivityService.updateBadActivity(criteria, updateObj, options, callback);
};

var performAggregation = function (aggregationArray, callback) {
    services.BadActivityService.aggregate(aggregationArray, callback);
};

var addBadActivity = function (obj, callback) {
    if (!( obj && obj.userId && obj.quizName && callback && typeof callback === 'function' )) {
        logger.debug("addBadActivity- arguments are invalid , obj-", obj);
        logger.error("addBadActivity- arguments are invalid");
        callback({'error' : "arguments are invalid "} );
        return false;
    }
    let quizName = obj.quizName;
    const { _id } =  libs.constants.quizParamsHMSET;
    services.RedisServices.redis("hget", quizName,  _id.key, (errQuizName, quizParams) => {
        if (!quizParams) {
            logger.error(`quiz Params not saved in redis for quizname ${quizName} addBadActivity`);
            callback(`quiz Params not saved in redis for quizname ${quizName} addBadActivity` );
            return
        }
        let findQuery = {'userId': mongoose.Types.ObjectId(obj.userId), 'quizId': mongoose.Types.ObjectId(quizParams)};
        let aggregationArray = [{$match: findQuery}, {$sort: {'createdAt': -1}}, {$limit: 1},
            {$project: {'activityLength': {$size: '$activity'}}}];
        // performA ggregation(aggregationArray, { 'attemptData' : 1 }, { sort : {'createdAt' : -1 }, limit : 1 }, function (error, userAttempt) {
        performAggregation(aggregationArray, function (error, badActivity) {
            if (error) {
                logger.debug(" error in Bad activity retrival ", error);
                logger.error(" error in Bad activity retrival ");
                callback(error, null);
            }
            else {
                let badActivityObj = {
                    '_id': mongoose.Types.ObjectId(),
                    'level': obj.level,
                    'msg': obj.msg,
                    'time': obj.time,
                    'code' : obj.code
                };
                badActivity = badActivity[0];
                if (!(badActivity ) || (   badActivity.activityLength >= libs.constants.bucketSize)) {
                    services.BadActivityService.addBadActivity({
                        'userId': obj.userId, 'quizId': mongoose.Types.ObjectId(quizParams),
                        'activity': [badActivityObj]
                    }, callback);
                }
                else {
                    updateBadActivity(findQuery, {$push: {activity: badActivityObj}}, {}, callback);
                }
            }
        });
    });
};

var removeBadActivity = function (criteria, callback) {
    services.BadActivityService.removeBadActivity(criteria, callback);
}

module.exports = {
    getBadActivity: getBadActivity,
    getOneBadActivity: getOneBadActivity,
    updateBadActivity: updateBadActivity,
    performAggregation: performAggregation,
    addBadActivity: addBadActivity,
    removeBadActivity: removeBadActivity
};
