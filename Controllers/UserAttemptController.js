const services = require('../Services');
const libs = require("../Lib");
const logger = console
const mongoose = require('mongoose');
const archivedUserAttemptController = require("./Archived_Controller/UserAttemptController");
const ejs = require('ejs');
const constants = require('../Lib/constants');

var setModelValuesMCQ = function (payload, newQues) {
    if (!payload || !newQues || typeof payload !== 'object' || typeof newQues !== 'object' || !Object.prototype.hasOwnProperty.call(payload, 'inputMCQ')) {
        return false;
    }

    newQues.userInputMCQ = parseInt(payload.inputMCQ.replace("chkOpt", ""), 10) - 1;
    return true;
};

var setModelValuesCoding = function (userData, payload, newQues) {

    if ( !(payload && newQues && userData && typeof payload === 'object' && typeof newQues === 'object') ) {
        logger.debug(`payload invalid setModelValuesCoding payload- `,payload, "--newQues-- ",newQues," userData ",userData);
        logger.error(`payload invalid setModelValuesCoding payload`);
        return false;
    }
    newQues.userOutputCoding = [];
    newQues.userProgram = payload.txtCodeAt;
    newQues.userLanguage = payload.progLang;

    if (payload.errors && payload.errors.trim()) {
        newQues.userCompilationError = payload.errors;
        return false;
    }

    if( userData.payload.isCustomInput ){
        newQues.userOutputCoding.push( payload.outputArray[0].userOutput );
        newQues.isCustomInput = true;
        newQues.customInput = userData.payload.stdin ? userData.payload.stdin.replace( libs.constants.endOfInputString,"" )
            : ""
    }else {
        let outputArray = userData.outputArray || payload.outputArray || []
        let testCase = Math.max (  
            payload.testCase  
            ? 
                 Array.isArray( payload.testCase ) 
                    ? payload.testCase.length 
                    : payload.testCase
            :
         ( userData.question && userData.question.questionTypeCoding && 
            ( userData.question.questionTypeCoding.tc  || userData.question.questionTypeCoding.testCase ) 
                 ? 
                    ( userData.question.questionTypeCoding.tc 
                        ? userData.question.questionTypeCoding.tc.length 
                        : userData.question.questionTypeCoding.testCase.length 
                    )
                 : 0 ), outputArray.length );
        if ( testCase  ) {
            for (let i = 0; i < testCase; i += 1) {
                newQues.userOutputCoding.push( outputArray[i] ?   // this condition is used for compile only case
                    outputArray[i].userOutput || ( typeof outputArray[i] == 'string' ? outputArray[i].replace(/[\r]/g, '') : "" ) : "");
            }
        }
    }

    if ( userData.testCaseComparisonResult) {
        newQues.testCasePassed = userData.testCaseComparisonResult.testCasePassed;
        newQues.totalScore = userData.testCaseComparisonResult.totalScore;
        newQues.totalTestCases = userData.testCaseComparisonResult.totalTestCases;
    }
 
    return true;
};

var setModelValuesWeb = function (payload, newQues) {
    if (!payload || !newQues || typeof payload !== 'object' || typeof newQues !== 'object') {
        return false;
    }
    newQues.html = payload.html;
    newQues.css = payload.css;
    newQues.js = payload.js;

    if ( payload.testCaseComparisonResult) {
        newQues.testCasePassed = payload.testCaseComparisonResult.testCasePassed;
        newQues.totalScore = payload.testCaseComparisonResult.totalScore;
        newQues.totalTestCases = payload.testCaseComparisonResult.totalTestCases;
        newQues.webTestCaseStatus = payload.testCaseComparisonResult.webTestCaseStatus;
    }

    return true;
};

var setModelValuesSubjective = function (payload, newQues) {
    if (!payload || !newQues || typeof payload !== 'object' || typeof newQues !== 'object' || !Object.prototype.hasOwnProperty.call(payload, 'txtSub')) {
        return false;
    }
    newQues.userInputSubjective = payload.txtSub;
    if ( payload.files ) {
        newQues.files = [];
        for (let index = 0; index < payload.files.length; index++) {
            let filePath = payload.files[index].path;
            let dirSeparator = filePath.indexOf('/') != -1 ? '/' : '\\'; 
            let fileName = filePath.substring(filePath.lastIndexOf(dirSeparator)+1);
            newQues.files.push(fileName);
        }
    }

    return true;
};

var setModelValuesStepwise = function (payload, newQues) {
    if (!payload || !newQues || typeof payload !== 'object' || typeof newQues !== 'object') {
        return false;
    }
    let userOutput = [];
    if (payload.outputArray) {
        userOutput = payload.outputArray;
    } else if (payload.userOutputArray) {
        payload.userOutputArray.forEach((obj) => {
            userOutput.push(obj.userOutput);
        })
    } else {
        return false;
    }
    newQues.userOutputCoding = userOutput;
    return true;
};

var setModelValuesAttempt = function (userData, payload, type, userAttemptObj) {
    if (!payload || !userAttemptObj || !type || typeof payload !== 'object' || typeof userAttemptObj !== 'object') {
        return false;
    }
    if (type === libs.constants.questionTypeNumeric.questionTypeMCQ)
        return setModelValuesMCQ(payload, userAttemptObj);
    else if (type === libs.constants.questionTypeNumeric.questionTypeSubjective)
        return setModelValuesSubjective(userData.payload, userAttemptObj);
    else if (type === libs.constants.questionTypeNumeric.questionTypeWeb){
        userData.payload.testCaseComparisonResult = userData.testCaseComparisonResult || userData.payload.testCaseComparisonResult;
        return setModelValuesWeb(userData.payload, userAttemptObj);
    }
    else if (type === libs.constants.questionTypeNumeric.questionTypeCoding)
        return setModelValuesCoding(userData, payload, userAttemptObj);
    else if (type === libs.constants.questionTypeNumeric.questionTypeStepwise)
        return setModelValuesStepwise(payload, userAttemptObj);
    else {
        logger.error("invalid module setModelValuesAttempt");
        return undefined
    }

};

const updateUserAttemptInDb = function (criteria, updateObj, options, callback) {
    return services.UserAttemptService.updateUserAttempt(criteria, updateObj, options, callback);
};

// var updateUserAttempt = function (userAnswerId, updateObj, callback) {
//     updateUserAttemptInDb({_id: userAnswerId}, updateObj, {upsert: true}, function (error, result) {
//         if (error) {
//             logger.debug("updateUserAttempt error in userAttempt update ", error);
//             logger.error("updateUserAttempt error in userAttempt update");
//             callback(error, null);
//         }
//         else {
//             // logger.error(" user attempt updated successfully ", result);
//             callback(error, result);
//         }
//     });
// }

var createUserAttempt = function (userAnswerId, userData, resultData, questionType, callback) {

    if (!( userAnswerId && userData && resultData && questionType && callback && typeof callback === 'function' )) {
        logger.error("arguments are invalid createUserAttempt, useranswerId-", userAnswerId, " userData-", userData, " resultData-", resultData, " questionType-", questionType);
        logger.error("arguments are invalid createUserAttempt");
        callback( "arguments are invalid ");
        return false;
    }

    let findQuery = { 'userAnswerId' : userAnswerId };
    let aggregationArray = [ { $match : findQuery } , {$sort: {'createdAt': -1 } },{$limit: 1},
        { $project : { 'attemptDataLength' : { $size: '$attemptData' } } } ];
    if (userData.payload.isTutorialQuestion)
        findQuery.tutorialSegmentId = userData.payload.questionId;

    // performAggregation(aggregationArray, { 'attemptData' : 1 }, { sort : {'createdAt' : -1 }, limit : 1 }, function (error, userAttempt) {
    performAggregation(aggregationArray, function (error, userAttempt) {
        if (error) {
            logger.debug("createUserAttempt error in userAttempt retrival ", error);
            logger.error("createUserAttempt error in userAttempt retrival ");
            callback(error, null);
        }
        else {
            let userAttemptObj = { '_id' : mongoose.Types.ObjectId()};
            if( userData.payload.questionSubmission ) {
                userAttemptObj.finalSubmission = true;
            }
            if (userData.payload.isTutorialQuestion) {
                userAttemptObj.userProgram = userData.txtCodeAt;
                userAttemptObj.userLanguage = userData.payload.language
            } else {
                setModelValuesAttempt( userData, resultData, questionType, userAttemptObj);
            }
            if( !( userAttempt && userAttempt.length ) )
                userAttempt = null;
            else{
                userAttempt = userAttempt[0];
            }
            userAttemptObj.timeOfCreation = userData.payload && userData.payload.time ? userData.payload.time : Date.now();
            if (!(userAttempt) || (userAttempt.attemptDataLength >= libs.constants.bucketSize)) {
                
                createUserAttemptInDb({
                    tutorialSegmentId: (userData.payload.isTutorialQuestion) ? userData.payload.questionId : null,
                    userAnswerId: userAnswerId,
                    attemptData: [userAttemptObj]
                }, (err, result) => {
                    if (err) return callback(err);
                    callback(err, { userAttemptObj, result })
                });
            }
            else {
                updateUserAttemptInDb({ '_id': userAttempt._id },
                    {
                        $push: { attemptData: userAttemptObj }
                    }, {}, (err, result) => {
                        if (err) return callback(err);
                        callback(err, { userAttemptObj, result })
                    });
            }
        }
    });
};


var getUserAttempt = function (criteria, projections = {}, options = {}, callback) {
    return services.UserAttemptService.getUserAttempt(criteria, projections, options, callback);
};

var getOneUserAttempt = function (criteria, projections, options, callback) {
    return services.UserAttemptService.getOneUserAttempt(criteria, projections, options, callback);
};

var createUserAttemptInDb = function (userAttempt, callback) {
    services.UserAttemptService.addUserAttempt(userAttempt, callback);
};

var getUserAttemptFromUserAnswerId = function (userAnswerId, callback) {

    if (!( userAnswerId && callback && typeof callback === 'function' )) {
        logger.error("getUserAttemptFromUserAnswerId arguments are invalid");
        callback({ 'error' : " arguments are invalid"});
        return;
    }
    services.UserAttemptService.getUserAttempt({"userAnswerId": userAnswerId}, {}, {}, callback);
};

var getDistinctUserAttempts = function (criteria, groupFields, callback) {
    services.UserAttemptService.getDistinctUserAttempts(criteria, groupFields, callback);
};

var getAttemptForPreview = async function ( req, userAttemptDataId, userAttemptId, callback) {
    const isCB = callback && typeof callback === 'function';
    try {
        let sessionObj = req.session;
        if (!( sessionObj && userAttemptDataId && userAttemptId )) {
            logger.debug("getAttemptForPreview arguments are invalid----", "sessionObj-", sessionObj, " userAttemptId-", userAttemptId, "userAttemptDataId-", userAttemptDataId);
            logger.error("getAttemptForPreview arguments are invalid");
            throw new Error('Argumets are invalid');
        }
    
        if( req.query && req.query.isArchived ){
            var getAttempt = archivedUserAttemptController.getOneUserAttempt;
        }else{
            var getAttempt = getOneUserAttempt;
        }
        const result = await getAttempt({'_id': userAttemptId, "attemptData._id": userAttemptDataId}, {
            'attemptData.$': 1,
            'userAnswerId': 1
        }, {});
        if (!result) {
            logger.debug(" error in finding userattempt getAttempt getAttemptForPreview ids may be wrong-", error, "userAttemptId-", userAttemptId, "userAttemptDataId-", userAttemptDataId);
            logger.error(" error in finding userattempt getAttempt getAttemptForPreview ids may be wrong");
            throw new Error('Error in finding userattempt, ids may be wrong');
        } else if ( 0 && sessionObj.role === libs.constants.roleNumberFromString.user &&
            result.userAnswerId.userId.email !== sessionObj.email) {
            logger.debug("not allowed to see other student attempts--- attempt student id-", result.userAnswerId.userId.email,
                " userId--", sessionObj.email);
            throw new Error('not allowed to seee other student attempts');
        }
        let containerObj = result.userAnswerId.courseId;
        if( result.userAnswerId.quizId ){
            containerObj = result.userAnswerId.quizId;
            containerObj.isQuiz = true;
        }
        setQuestionValuesFromUserAttemptAndContainer(sessionObj.role, result, result.userAnswerId.questionId, containerObj );
        result.userAnswerId.questionId.displayName = result.userAnswerId.userId.displayname;
        if (isCB) return callback(null, {ques: result.userAnswerId.questionId, userAttempt: result});
        return {ques: result.userAnswerId.questionId, userAttempt: result};
    } catch (error) {
        if(isCB) return callback(error.message ?? error);
        throw new Error(error?.message ?? error);
    }
};

const getAttemptForPreviewFromRedis = async function (payload, cb) {
    try {
        let {session, userId, quizId, quesId, attemptId} = payload;
        let isMongoId = false;
        if ( ! session )                throw new Error("Session is null");
        if ( ! userId )                 throw new Error("UserId is null");
        if ( ! quizId )                 throw new Error("QuizId is null");
        if ( ! quesId )                 throw new Error("QuesId is null");
        if ( ! attemptId )                 throw new Error("Attempt Id is null");
        if ( isNaN(attemptId) ) {
            try{
                attemptId = mongoose.Types.ObjectId(attemptId)
                isMongoId = true
            } catch(e){
                console.log("Invalid attempt id", attemptId)
                throw new Error("Attempt Id not valid");
            }
        }       

        if ( session.role == libs.constants.roleNumberFromString.user && session.userId != userId )     throw new Error("Not Authorized");

        const userAttemptsListRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${quizId}:${userId}:${quesId}`;
        let attemptStr;
        if ( isMongoId ) {
            let attemptsList = await services.RedisServices.redis2("lrange", userAttemptsListRedisKey, 0, -1);
            for( let i = 0; i < attemptsList.length; i+= 1){
                let attemptObj = JSON.parse( attemptsList[i]);
                if( attemptObj?._id?.toString() == attemptId.toString() ){
                    attemptStr = attemptsList[i]
                    break;
                }
            }
        } else {
            attemptStr = await services.RedisServices.redis2('lindex', userAttemptsListRedisKey, attemptId);
        }

	    if (!attemptStr) {
		    const userAnswerId = (await services.UserAnswerService.getOneUserAnswer({questionId: quesId, userId, quizId}, {_id: 1}, {}) || {})._id;
		    if (!userAnswerId)     throw new Error("Link not valid");
		    const userAttemptObj = await services.UserAttemptService.getOneUserAttempt({userAnswerId}, {_id: 1, attemptData: 1}, {});
		    const userAttemptId = userAttemptObj._id;
            let userAttemptDataObj;
            if ( isMongoId ) {
                (userAttemptObj.attemptData || []).map( attemptObj =>{
                    if( attemptObj._id.toString() == attemptId ) {
                        userAttemptDataObj = attemptObj
                    }
                })
            } else {
                userAttemptDataObj = ((userAttemptObj.attemptData || [])[parseInt(attemptId)-1]) || {};
            }
			const userAttemptDataId = userAttemptDataObj._id;
		    if (!userAttemptDataId)     throw new Error("Link not valid");
		    return {redirectUrl: `/quest/preview/${userAttemptId}/${userAttemptDataId}`};
	    }


        let quesObj = await services.QuestionService.getOneQuestion({_id: quesId}, {}, {});
        if ( ! quesObj )    throw   new Error("QuesId is null");

        let quizObj = await services.QuizService.getOneQuiz({_id: quizId}, {}, {});
        if ( ! quizObj )    throw   new Error("QuizId is null");

        let attemptObj = {
            attemptData: [JSON.parse(attemptStr)],
            userAnswerId: {
                quizId: quizObj,
                questionId: quesObj,
                userId,
            }
        }

        let userAttemptData = attemptObj.attemptData[0];
        if ( userAttemptData ) {
            switch (quesObj.type) {
                case libs.constants.questionTypeNumeric.questionTypeMCQ:
                    userAttemptData.userInputMCQ = parseInt((userAttemptData.inputMCQ || '').replace("chkOpt", "")) - 1;
                    break;
                case libs.constants.questionTypeNumeric.questionTypeStepwise:
                    userAttemptData.userOutputCoding = userAttemptData.stepwiseUserInput;
                    break;
                case libs.constants.questionTypeNumeric.questionTypeCoding:
                    userAttemptData.userOutputCoding = userAttemptData.userOutputCoding || [];
                    break;
                case libs.constants.questionTypeNumeric.questionTypeSubjective:
                    userAttemptData.userInputSubjective = userAttemptData.userProgram;
                    break;
                default:
                    break;
            }
        }

        quizObj.isQuiz = true;
        setQuestionValuesFromUserAttemptAndContainer(session.role, attemptObj, quesObj, quizObj );

        let obj = {ques: quesObj, userAttempt: attemptObj}
        if (cb)     cb(null, obj);
        return obj;
    }
    catch (e) {
        console.log("getAttemptForPreviewFromRedis, Error = ", e);
        if (cb)    cb(e);
	    throw new Error(e.message);
    }
}

var setQuestionValuesFromUserAttemptAndContainer = function (role, userAttempt, question, container) {
    let containerContent = container.isQuiz ? container.quizContent : container.courseContent;
    if (question.type === libs.constants.questionTypeNumeric.questionTypeCoding ||
        question.type === libs.constants.questionTypeNumeric.questionTypeStepwise) {
        let counter = 0;
        let tempObj;
        let isLangPresent=false;

        question.questionTypeCoding.codeproglang.forEach((langObj) => {
            if (langObj.language === userAttempt.attemptData[0].userLanguage) {
                isLangPresent=true;
                question.questionTypeCoding.codeproglang = [langObj];
                if (question.type == '3' || question.type == '4')
                    question.questionTypeCoding.codeproglang[0].codeComponents.body = userAttempt.attemptData[0].userProgram;
                return;
            }
            counter += 1;
        });


        // if userattempt lang not present in questionObj
        if(question.type != libs.constants.questionTypeNumeric.questionTypeStepwise && question.questionTypeCoding  && question.questionTypeCoding.codeproglang && userAttempt.attemptData && !isLangPresent && (question.questionTypeCoding.codeproglang[0].language != userAttempt.attemptData[0].userLanguage)){
            question.questionTypeCoding.codeproglang.push({
                language:userAttempt.attemptData[0].userLanguage,
                codeComponents:{
                    head:'',
                    tail:'',
                    solution:'',
                    body:userAttempt.attemptData[0].userProgram,
                }
            })
        }

        containerContent.forEach((containerContentObj) => {
            if (containerContentObj.id.equals(question._id)) {
                question.questionTypeCoding.executionType = containerContentObj.executionType;
            }
            return;
        });
    } else if (question.type === libs.constants.questionTypeNumeric.questionTypeMCQ) {
        containerContent.forEach((containerContentObj) => {
            if (containerContentObj.id.equals(question._id)) {
                question.questionTypeMCQ.showExplanation =
                    ( containerContentObj.executionType === ( libs.constants.codingQuestionExecutionType.resultWithTestCases ) || (
                        role === libs.constants.roleNumberFromString.admin || role === libs.constants.roleNumberFromString.mentor || role === libs.constants.roleNumberFromString.recruiter ) ) ?
                        true : false;
            }
            return;
        });
    }
};
var performAggregation = function (aggregationArray, callback) {
    services.UserAttemptService.aggregate(aggregationArray, callback);
};

var createProjectAttempt = function( userAnswerId, payload, callback ){
    if (!( userAnswerId && payload && callback && typeof callback === 'function' )) {
        logger.debug(" arguments are invalid createProjectAttempt, useranswerId-", userAnswerId, "payload" , payload );
        logger.error(" arguments are invalid createProjectAttempt");
        callback( { 'error' : "arguments are invalid"});
        return false;
    }

    let findQuery = { 'userAnswerId' : userAnswerId };
    let aggregationArray = [ { $match : findQuery } , {$sort: {'createdAt': -1 } },{$limit: 1},
        { $project : { 'attemptDataLength' : { $size: '$attemptData' } } } ];
    performAggregation(aggregationArray, function (error, userAttempt) {
        if (error) {
            logger.debug("createProjectAttempt error in userAttempt retrival ", error);
            logger.error("createProjectAttempt error in userAttempt retrival");
            callback(error, null);
        }
        else {
            let userAttemptObj = { '_id' : mongoose.Types.ObjectId()};
                userAttemptObj.userLanguage = payload.language;
                userAttemptObj.projectLink = payload.projectLink;
            if( !( userAttempt && userAttempt.length ) )
                userAttempt = null;
            else{
                userAttempt = userAttempt[0];
            }
            if (!(userAttempt ) || (   userAttempt.attemptDataLength >= libs.constants.bucketSize)) {
                createUserAttemptInDb({
                    userAnswerId: userAnswerId,
                    attemptData: [userAttemptObj]
                }, callback);
            }
            else {
                updateUserAttemptInDb({'_id' : userAttempt._id},
                    {
                        $push: {attemptData: userAttemptObj}
                    }, {}, callback);
            }
        }
    });
};

var getDocCount = function ( criteria, callback) {
    services.UserAttemptService.getDocCount( criteria, callback )
};

const createAttemptInDbFromRedisData = async function( payload = {} ){
    try{
        let { userAnswerId, attempts } = payload;
        if(!userAnswerId ) throw new Error("Useranswer id not present");
        if( !( attempts && attempts.length )) throw new Error("Attempts not present");
    
        let result = await createUserAttemptInDb({
            userAnswerId,
            attemptData: attempts
        });
        return result;
    } catch(e){
        console.log("Error while saving attempts from redis", e );
        return false;
    }
}
module.exports = {
    // updateUserAttempt: updateUserAttempt,
    getAttemptForPreview: getAttemptForPreview,
    getUserAttempt: getUserAttempt,
    getOneUserAttempt: getOneUserAttempt,
    updateUserAttemptInDb: updateUserAttemptInDb,
    createUserAttemptInDb: createUserAttemptInDb,
    createUserAttempt: createUserAttempt,
    getUserAttemptFromUserAnswerId: getUserAttemptFromUserAnswerId,
    getDistinctUserAttempts: getDistinctUserAttempts,
    setModelValuesMCQ: setModelValuesMCQ,
    setModelValuesAttempt: setModelValuesAttempt,
    setQuestionValuesFromUserAttemptAndContainer : setQuestionValuesFromUserAttemptAndContainer,
    performAggregation : performAggregation,
    createProjectAttempt : createProjectAttempt,
    getDocCount : getDocCount,
    getAttemptForPreviewFromRedis,
    createAttemptInDbFromRedisData,
};
