const libs = require('../Lib');
const services = require('../Services');


/**
 * 
 * @param {{userId, quizId, email}} param0 
 * @param {boolean?} isMod 
 * @returns 
 */
exports.getLiveStreamKey = async ({userId, quizId, email}, isMod = false) => {
    const key = libs.constants.redisKeys.getLiveStreamKey(userId, quizId);
    const redisPipeLine = redisClient.pipeline();
    redisPipeLine.ttl(key);
    redisPipeLine.get(key);
    const redisPipeLineResult = await redisPipeLine.exec();
    const [ttl, liveStreamRoomIdFromRedis] = [redisPipeLineResult?.[0]?.[1] ?? 0, redisPipeLineResult?.[1]?.[1]];
    if (ttl > libs.constants.liveStreamTokenUpto_Sec) {
        if (liveStreamRoomIdFromRedis) {
            const result = JSON.parse(liveStreamRoomIdFromRedis);
            return result.authToken;
        }
    }
    const authToken = libs.jitsi.createAuthToken({
        userId: userId,
        email: email,
        isMod: isMod,
    })
    await services.RedisServices.redis('set', key, JSON.stringify({authToken: authToken}), 'EX', libs.constants.liveStreamRedisCacheTime_sec);
    return authToken;
}