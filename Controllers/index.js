/*Controller Index*/
module.exports = {
    AwsSpaceController: require('./AwsSpaceController'),
    UserController: require("./UserController"),
    QuestionController: require("./QuestionController"),
    TagController: require("./TagController"),
    QuizController: require("./QuizController"),
    UserAnswerController: require("./UserAnswerController"),
    CompileController: require("./CompileController"),
    UserAttemptController: require("./UserAttemptController"),
    SessionController : require("./SessionController"),
    UserQuizSubmittedSegmentController : require('./UserQuizSubmittedSegmentController'),
    UserQuizSubmittedSegmentMetaDataController: require('./UserQuizSubmittedSegmentMetaDataController'),
    QuizNameController : require("./QuizNameController"),
    BadActivityController : require("./BadAcitvityController"),
    ArchivedController : require("./Archived_Controller"),
    IndexController : require("./IndexController"),
		QuizApiController : require("./QuizApiController"),
    QuizAllowedEmailsController : require("./QuizAllowedEmailsController"),
    LiveStreamController: require('./LiveStreamController'),
    StreamRoomController: require('./StreamRoomController'),
    TeamOnlyController: require('./TeamOnlyController'),
    JitsiBucketController: require('./JitsiBucketController'),
    FeedBackController: require('./FeedbackController'),
}

