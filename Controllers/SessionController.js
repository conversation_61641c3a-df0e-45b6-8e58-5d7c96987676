const _ = require('lodash');
const axios = require('axios');
const utils = require('../Lib/Util');
const constants = require('../Lib/constants')

const createSessionProxyObject = (session) => {
    return new Proxy(session, {
        set(target, key, value, receiver) {
            return Reflect.set(target, key, value, receiver);
        },
        get(target, key, receiver) {
            if (key === 'save' ) {
                return async (callback) => {
                    try {
                        await RedisServices.sharedRedis('set',`${sessionKey}:${session.id}`, JSON.stringify(session));
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                    } catch (error) {
                        console.error(error);
                        if (callback && typeof callback === 'function') {
                            callback(error?.message ?? error);
                        }
                    }
                    return;
                }
                
            }
            return Reflect.get(target, key, receiver);

        }
    })
}

const createSession = (req, session) => {
    if (!session) return false;
    req.session = session;
    if (req.session.userId) {
        req.isAuthComplete = true;
    } else {
        req.isAuthComplete = false;
    }
    if (!session.save) {
        session = createSessionProxyObject(session);
    }
    req.session = session;
}


// const saveToSession = async (req, data) => {
    // const response = await axios.post(`${utils.getLoginServiceIntenalUrl()}/saveToSession`,data,
//         {
//             headers:{
//                 'authorization': req.session.id,
//                 'Content-Type': 'application/json',
//         }
//     });
//     if(response.data.error) {
//         throw new Error(response.error);
//     }
//     return response.data;
// };

// const getSessionBySessionID = async (token, callback) => {
//     const isCB = callback && typeof callback === "function";
//     try{
//         const response = await axios.get(`${utils.getLoginServiceIntenalUrl()}/verifySession`,{
//             headers: {
//                 'authorization': token,
//             }
//         });
//         if(response.data.error) {
//             throw new Error(response.data.error);
//         }
//         if(isCB) return callback(null, response.data.data);
//         return response.data.data;
//     } catch (error) {
//         if(isCB) return callback(error?.message ?? error);
//         throw new Error(error?.message ?? error);
//     }
// }


// const forceLogout = async (token) => {
//     try{
//         let url = `${utils.getLoginServiceIntenalUrl()}/logout`;
//         const response = await axios.default.post(url,{
//             token: token
//         },{
//             headers:{
//                 'Content-Type': 'application/json'
//             }
//         })
//         if(response.data.error) {
//             throw new Error(response.data.error);
//         }
//         return true;
//     } catch (error) {
//         console.log(`Error while logging out ${req.session.email}`);
//         throw new Error(error);
//     }
// };

// const logoutUser = async (req, res) => {
//     try{
//         let url = `${utils.getLoginServiceIntenalUrl()}/logout`;
//         const response = await axios.default.post(url,{
//             token: req.session.id
//         },{
//             headers:{
//                 'Content-Type': 'application/json'
//             }
//         })
//         if(response?.headers?.['set-cookie'] && response?.headers?.['set-cookie'][0]) {
//             res.set('Set-Cookie',response?.headers?.['set-cookie'][0]);
//         }
//         if(response.data.error) {
//             throw new Error(response.data.error);
//         }
//         return true;
//     } catch (error) {
//         console.log(`Error while logging out ${req.session.email}`);
//         throw new Error(error);
//     }
// }


// module.exports = {
//     saveToSession,
//     getSessionBySessionID,
//     logoutUser,
//     forceLogout,
//     createSession
// }

//PRODUCTION
const { RedisServices } = require('../Services');
const sessionKey = 'sessionToken';

const saveToSession = async (req, dataToSave) => {
    const sessionData = await RedisServices.sharedRedis('get',`${sessionKey}:${req.session.id}`);
    if(!sessionData) {
        throw new Error('Session Expired');
    }
    const data = JSON.parse(sessionData);
    Object.keys(dataToSave).forEach((element) => {
        if (dataToSave[element] ===  null ) {
            delete data[element];
        }
        data[element] = dataToSave[element];
    });
    await RedisServices.sharedRedis('set',`${sessionKey}:${req.session.id}`, JSON.stringify(data));
    return data;
}


const getSessionBySessionID = async (token, callback) => {
    const isCB = callback && typeof callback === "function";
    try{
        const userData = await RedisServices.sharedRedis('get',`${sessionKey}:${token}`);
		if(!userData) {
			throw new Error('Session not present');
		}
		const parsedData = JSON.parse(userData);
		let finalData = parsedData;
        finalData.id = token;
        await RedisServices.sharedRedis('expire',`${sessionKey}:${token}`,constants.sessionExpireTimeSeconds);
        finalData  = createSessionProxyObject(finalData);
        if(callback) return callback(null, finalData);
        return finalData;
    } catch (error) {
        if(isCB) return callback(error?.message ?? error);
        throw new Error(error?.message ?? error);
    }
}


const forceLogout = async (token) => {
    try{
        await RedisServices.sharedRedis('del', `${sessionKey}:${token}`);
        return true;
    } catch (error) {
        console.log(`Error while logging out user with sessionId ${token}`);
        throw new Error(error?.message ?? error);
    }
};

const logoutUser = async (req, res) => {
    try{
        let url = `${utils.getLoginServiceIntenalUrl()}/logout`;
        const response = await axios.default.post(url,{
            token: req.session.id
        },{
            headers:{
                'Content-Type': 'application/json'
            }
        })
        if(response?.headers?.['set-cookie'] && response?.headers?.['set-cookie'][0]) {
            res.set('Set-Cookie',response?.headers?.['set-cookie'][0]);
        }
        if(response.data.error) {
            throw new Error(response.data.error);
        }
        return true;
    } catch (error) {
        console.log(`Error while logging out ${req.session.email}`);
        throw new Error(error?.message ?? error);
    }
}


module.exports = {
    createSession,
    saveToSession,
    getSessionBySessionID,
    logoutUser,
    forceLogout
}
