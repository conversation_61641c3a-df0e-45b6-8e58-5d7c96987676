const services = require('../Services');
const libs = require("../Lib");
const mongoose = require('mongoose');
const logger = console;

const redisKeys = libs.constants.redisKeys;

var getUserQuizSubmittedSegment = function (criteria, projections, options, callback) {
    if (!( criteria && projections && options )) {
        logger.debug("invalid parameters getUserQuizSubmittedSegment criteria-", criteria, " projections-", projections, " options-", options);
        logger.error("invalid parameters getUserQuizSubmittedSegment");
        callback({ 'error' : "invalid parameters getOneUserQuizSubmittedSegment"});
        return;
    }
    return services.UserQuizSubmittedSegmentService.getUserQuizSubmittedSegment(criteria, projections, options, callback);
};

var getOneUserQuizSubmittedSegment = function (criteria, projections, options, callback) {
    if (!( criteria && projections && options )) {
        logger.debug("invalid parameters getOneUserQuizSubmittedSegment criteria-", criteria, " projections-", projections, " options-", options);
        logger.error("invalid parameters getOneUserQuizSubmittedSegment");
        callback({ 'error' : "invalid parameters getOneUserQuizSubmittedSegment"});
        return;
    }
    return services.UserQuizSubmittedSegmentService.getOneUserQuizSubmittedSegment(criteria, projections, options, callback);
};

var updateUserQuizSubmittedSegment = function (criteria, updateObj, options, callback) {
    return services.UserQuizSubmittedSegmentService.updateUserQuizSubmittedSegment(criteria, updateObj, options, callback);
};

var updateManyUserQuizSubmittedSegment = function (criteria, updateObj, options, callback) {
    return services.UserQuizSubmittedSegmentService.updateManyUserQuizSubmittedSegment(criteria, updateObj, options, callback);
};

var performAggregation = function (aggregationArray, callback) {
    return services.UserQuizSubmittedSegmentService.aggregate(aggregationArray, callback);
};

var submitSegment = async function (payload, quizId, qId, segment, sessionObj, callback) {
    const isCB = callback && typeof callback === 'function';
    try {
        if (!( payload && quizId && qId && sessionObj )) {
            throw new Error('parameters not valid');
        }
        let findObj = {'userId': mongoose.Types.ObjectId(sessionObj.userId), 'quizId': mongoose.Types.ObjectId(quizId)}
        let updateObj = {$addToSet: {'quizSubmittedSegments': segment}};
        let optionObj = {'upsert': true, 'setDefaultsOnInsert': true};
        let key = libs.constants.redisKeys.ongoingQuiz + ":" + findObj.userId + ":" + findObj.quizId;
    
        const submittedSegments = await services.RedisServices.redis("hget", key, libs.constants.redisKeys.submittedSegments);
        let submittedSegmentsObj = JSON.parse( submittedSegments );
        if( submittedSegmentsObj.indexOf( segment ) !== -1 ){
            callback( err )
            return;
        }
        submittedSegmentsObj.push( segment );
        submittedSegmentsObj = JSON.stringify( submittedSegmentsObj );
        await services.RedisServices.redis("hset", key, libs.constants.redisKeys.submittedSegments, submittedSegmentsObj);
        const data = {
            time: new Date(),
            submissionType: libs.constants.segmentHistoryType.submission,
            createdBy: sessionObj.userId,
            segmentIndex: segment,
        }
        if (payload.imageURL) {
            data.imageURL = payload.imageURL;
            data.otp = payload.otp;
        }
        const segmentHistoryString = libs.util.createSegmentSubmissionHistoryRedisString(data);
        await services.RedisServices.redis("lpush", libs.constants.redisKeys.getSegmentSubmissionHistory(sessionObj.quizId, sessionObj.userId), segmentHistoryString);
    } catch (error) {
        if (isCB) return callback(error?.message);
        throw new Error(error?.message ?? error);
    }

};

var addUserQuizSubmittedSegment = function (obj, callback) {
    return services.UserQuizSubmittedSegmentService.addUserQuizSubmittedSegment(obj, callback);
};

var setQuizSubmittedSegmentsParamsInRedis = async function (quizSubmittedSegmentObj, queryObj, callback) {
    let isCB = callback && typeof callback === 'function';
    try {
        if (!(quizSubmittedSegmentObj && queryObj)) {
            console.log("setQuizSubmittedSegmentsParamsInRedis Payload is not valid");
            throw new Error("Payload not valid");
        }
        let { redisKeys } = libs.constants;
        let userSessionArray = ( quizSubmittedSegmentObj.userSessions || [] )
        let setObj = {
            'startTime': quizSubmittedSegmentObj.startTime,
            'dashboardUpdateTime' : quizSubmittedSegmentObj.dashboardUpdateTime,
            'userId': quizSubmittedSegmentObj.userId,
            // '_id': quizSubmittedSegmentObj._id.toString(),
            'displayName': quizSubmittedSegmentObj.displayName || "",
            'email': quizSubmittedSegmentObj.email || "",
            'extraTime': quizSubmittedSegmentObj.extraTime || 0,
            'idleTime': quizSubmittedSegmentObj.idleTime || 0,
            'fullScreenInCount' : quizSubmittedSegmentObj.fullScreenInCount || 0,
            'fullScreenOutCount' : quizSubmittedSegmentObj.fullScreenOutCount || 0,
            'tabSwitchCount': quizSubmittedSegmentObj.tabSwitchCount || 0,
            'tabSwitchInCount': quizSubmittedSegmentObj.tabSwitchInCount || 0,
            'forceLoginCount': quizSubmittedSegmentObj.forceLoginCount || 0,
            'codePasteCount': quizSubmittedSegmentObj.codePasteCount || 0,
            'userExitCount': quizSubmittedSegmentObj.userExitCount ?? 0,
            'userEnterCount': quizSubmittedSegmentObj.userEnterCount ?? 0,
            'multipleUserDetected': quizSubmittedSegmentObj.multipleUserDetected ?? 0,
            'illegalObjectDetected': quizSubmittedSegmentObj.illegalObjectDetected ?? 0,
            'loginCount': quizSubmittedSegmentObj.loginCount 
            || (quizSubmittedSegmentObj.loginDetails && quizSubmittedSegmentObj.loginDetails.length ) ||  1,
            'currentIp': quizSubmittedSegmentObj.currentIp || '',
            'camBlockCount': quizSubmittedSegmentObj.camBlockCount || 0,
            'tryTest': Boolean(quizSubmittedSegmentObj.tryTest),
            [ redisKeys.quizUserIp ] : quizSubmittedSegmentObj[ redisKeys.quizUserIp ]  ? JSON.stringify(quizSubmittedSegmentObj[ redisKeys.quizUserIp ]) : "[]" ,
			'endTime': quizSubmittedSegmentObj.endTime,
            'userSessions' : JSON.stringify(userSessionArray),
            'sessionTime' : quizSubmittedSegmentObj.sessionTime || 0,
            'hasQuizStarted' : ( quizSubmittedSegmentObj.hasQuizStarted 
                || ( quizSubmittedSegmentObj.startTime && quizSubmittedSegmentObj.endTime) 
                || ( userSessionArray.length ) ) ? true : false,
            'quizUserDetails' : JSON.stringify(quizSubmittedSegmentObj.quizUserDetails || []),
        }
        if( quizSubmittedSegmentObj.questionId ){
            setObj.questionId = quizSubmittedSegmentObj.questionId ? JSON.stringify( quizSubmittedSegmentObj.questionId) : "[]"
        }
        let quesScoreObj = {};
        if (quizSubmittedSegmentObj && quizSubmittedSegmentObj.quizSubmittedQuestion && quizSubmittedSegmentObj.quizSubmittedQuestion.length) {
            for (let i = 0; i < quizSubmittedSegmentObj.quizSubmittedQuestion.length; i += 1) {
                let submittedQuestionObj = quizSubmittedSegmentObj.quizSubmittedQuestion[i];
                setObj[libs.constants.redisKeys.questionIdString + ':' + submittedQuestionObj.questionId.toString()] = JSON.stringify(submittedQuestionObj);
                if (submittedQuestionObj.hasSubmitted)
                    quesScoreObj[submittedQuestionObj.questionId.toString()] = (parseInt(submittedQuestionObj.score) || 0) + (parseInt(submittedQuestionObj.additionalScore) || 0);
            }
        }
        setObj[redisKeys.submittedSegments] = quizSubmittedSegmentObj[redisKeys.submittedSegments] ?
            JSON.stringify(quizSubmittedSegmentObj[redisKeys.submittedSegments]) : "[]";
        let key = redisKeys.ongoingQuiz + ':' + queryObj.userId + ":" + queryObj.quizId;
        let submittedQuestionKey = key + ":" + redisKeys.submittedQuestions

        await services.RedisServices.redis("hmset", key, setObj)
        // services.RedisServices.redis("expire", key, libs.constants.expiryTimeOfQuizReportInSec);

        if (quizSubmittedSegmentObj.submittedQuestions && quizSubmittedSegmentObj.submittedQuestions.length) {
            await services.RedisServices.redis("sadd", submittedQuestionKey,
                quizSubmittedSegmentObj.submittedQuestions)
            // services.RedisServices.redis("expire", submittedQuestionKey, libs.constants.expiryTimeOfQuizReportInSec);

            let ongoingQuizScoreRedisKey = redisKeys.ongoingQuiz + ":" + queryObj.userId + ":" + queryObj.quizId + ":" + redisKeys.submittedQuesScores;
            let redisQuesScoreObj = await services.RedisServices.redis('hgetall', ongoingQuizScoreRedisKey )
            let hmsetObj = {};
            if (redisQuesScoreObj && Object.keys(redisQuesScoreObj).length) {
                for (const quesId in quesScoreObj) {
                    let dbScore = parseInt(quesScoreObj[quesId]);
                    if (redisQuesScoreObj[quesId]) {
                        let redisScore = parseInt(redisQuesScoreObj[quesId]);
                        if (dbScore > redisScore) {
                            hmsetObj[quesId] = dbScore;
                        }
                    }
                    else {
                        hmsetObj[quesId] = dbScore;
                    }
                }
            }
            else hmsetObj = quesScoreObj;

            if (Object.keys(hmsetObj).length) {
                await services.RedisServices.redis('hmset', ongoingQuizScoreRedisKey, hmsetObj)
            }
        }

        if (Array.isArray(quizSubmittedSegmentObj.loginDetails)) {
            const loginDetailsArr = [];
            quizSubmittedSegmentObj.loginDetails.map((obj = {}) => {
                let loginInfoString = libs.util.createQuizLoginRedisString(obj.loginTime, obj.ip, obj.platform);
                if (loginInfoString)    loginDetailsArr.push(loginInfoString);
            })
            if (loginDetailsArr.length) {

                await services.RedisServices.redis('lpush', redisKeys.getLoginTimeStampsList(queryObj.quizId, queryObj.userId), loginDetailsArr.reverse());
            }
        }

        if (Array.isArray(quizSubmittedSegmentObj.terminationDetails)) {
            const terminationDetails = [];
            quizSubmittedSegmentObj.terminationDetails.forEach((obj) => {
                try {
                    const str = libs.util.createTerminationRedisString(obj);
                    terminationDetails.push(str);
                    return str;
                } catch (error) {}
            });
            if (terminationDetails?.length) {
                await services.RedisServices.redis('lpush', redisKeys.getTerminationList(queryObj.quizId, queryObj.userId), terminationDetails.reverse());
            }
        }

        if (Array.isArray(quizSubmittedSegmentObj.aiProctoringReport)) {
            const aiProctoringReport = [];
            quizSubmittedSegmentObj.aiProctoringReport.forEach((obj) => {
                try {
                    const data = JSON.stringify(obj);
                    aiProctoringReport.push(data);
                } catch (error) {}
            })
            if (aiProctoringReport.length) {
                await services.RedisServices.redis('lpush', redisKeys.getAiProctorReportList(queryObj.quizId, queryObj.userId), aiProctoringReport.reverse());
            }
        }

        if (Array.isArray(quizSubmittedSegmentObj.tabSwitchReport)) {
            const tabSwitchReport = [];
            quizSubmittedSegmentObj.tabSwitchReport.map((obj = {}) => {
                let tabSwitchInfoString = libs.util.createTabSwitchReportRedisString(obj.tabTime, obj.tabSwitchType);
                if (tabSwitchInfoString)    tabSwitchReport.push(tabSwitchInfoString);
            })
            if (tabSwitchReport.length) {
                await services.RedisServices.redis('lpush', redisKeys.getTabSwitchReportList(queryObj.quizId, queryObj.userId), tabSwitchReport.reverse());

            }
        }

        if (Array.isArray(quizSubmittedSegmentObj.fullScreenReport)) {
            const fullScreenReport = [];
            quizSubmittedSegmentObj.fullScreenReport.map((obj = {}) => {
                let fullScreenInfoString = libs.util.createTabSwitchReportRedisString(obj.time, obj.eventType);
                if (fullScreenInfoString)    fullScreenReport.push(fullScreenInfoString);
            })
            if (fullScreenReport.length) {
                await services.RedisServices.redis('lpush', redisKeys.getfullScreenReportList(queryObj.quizId, queryObj.userId), fullScreenReport.reverse());
            }
        }

        if (Array.isArray(quizSubmittedSegmentObj.segmentSubmissionHistory)) {
            const segmentSubmissionHistory = [];
            quizSubmittedSegmentObj.segmentSubmissionHistory.forEach((obj = {}) => {
                if (!obj) return;
                let segmentSubmissionHistoryString = libs.util.createSegmentSubmissionHistoryRedisString(obj);
                if (segmentSubmissionHistoryString) {
                    segmentSubmissionHistory.push(segmentSubmissionHistoryString)
                }
            });
            if (segmentSubmissionHistory.length) {
                await services.RedisServices.redis('lpush', redisKeys.getSegmentSubmissionHistory(queryObj.quizId, queryObj.userId), segmentSubmissionHistory.reverse());
            }
        }

        // This for loop is executed rarely
        for (let index = 0; index < quizSubmittedSegmentObj?.quizSubmittedQuestion?.length; index++) {
            const quizSubmittedQuesObj = quizSubmittedSegmentObj.quizSubmittedQuestion[index];
            if (quizSubmittedQuesObj?.scoreUpdationActivity?.length) {
                const scoreUpdationActivityRedisKey = redisKeys.getScoreUpdationActivityList(queryObj.quizId, queryObj.userId, quizSubmittedQuesObj.questionId?.toString());
                const scoreUpdationActivityList = quizSubmittedQuesObj.scoreUpdationActivity.map(obj => {
                    return JSON.stringify({
                        originalScore: obj.originalScore,
                        newScore: obj.newScore,
                        updatedAt: obj.updatedAt?.getTime(),
                        updatedBy: obj.updatedBy?.toString(),
                    })
                })
                await services.RedisServices.redis('del', scoreUpdationActivityRedisKey);
                await services.RedisServices.redis('rpush', scoreUpdationActivityRedisKey, ...scoreUpdationActivityList);
            }
        }

        if( isCB)   return callback(null, quizSubmittedSegmentObj);
        return quizSubmittedSegmentObj
    } catch (e) {
        console.log("Error while setting params in redis", e);
        if (isCB) return callback(e);
        throw e;
    }
};

var getUserQuizSubmittedSegmentFromDb = async function (queryObj, options = {}, callback) {
    let isCB = callback && typeof callback === 'function';
    try {

        if (!(queryObj && options)) {
            console.log("invalid parameters getUserQuizSubmittedSegmentFromDb UserQuizSubmittedController queryObj-", queryObj, " options ", options);
            throw new Error("invalid parameters getUserQuizSubmittedSegmentFromDbUserQuizSubmittedController");
        }

        if (!(queryObj.userId && queryObj.quizId)) {
            console.log("getUserQuizSubmittedSegmentFromDbUserQuizSubmittedController queryObj not valid", queryObj);
            throw new Error("queryObj not valid");
        }
        let { userId, quizId } = queryObj
        let key = libs.constants.redisKeys.ongoingQuiz + ":" + userId + ":" + quizId,
        quizMGETArray = [...libs.constants.quizMGETArray],
        quizMGETArrayPropertyIndex = libs.constants.quizMGETArrayPropertyIndex;
        if (options.questionId) {
            var questionKey = libs.constants.redisKeys.questionIdString + ":" + options.questionId;
            quizMGETArray.push(questionKey);
        }

        let quizSubmittedObj = await services.RedisServices.redis("hmget", key, quizMGETArray)

        if (quizSubmittedObj && quizSubmittedObj.length && 
            ( quizSubmittedObj[quizMGETArrayPropertyIndex.sessionTime] )) {
            let newObj = {};
            let submittedQuestionKeys = key + ":" + redisKeys.submittedQuestions;
            let submittedQuestions = await services.RedisServices.redis("smembers", submittedQuestionKeys)
            submittedQuestions = submittedQuestions || []
            try {
                newObj.quizSubmittedQuestion = [];
                if (quizSubmittedObj[quizMGETArrayPropertyIndex.startTime]) {
                    let startTime = parseInt(quizSubmittedObj[quizMGETArrayPropertyIndex.startTime])
                    newObj.startTime = new Date(isNaN(startTime) ? quizSubmittedObj[quizMGETArrayPropertyIndex.startTime] : startTime);
                }
                quizSubmittedObj.userSessions = JSON.parse(quizSubmittedObj.userSessions || JSON.stringify([]) );
                // quizSubmittedObj.userSessions.push({
                //     startTime : newObj.startTime,
                //     endTime : newObj.endTime,
                //     extraTime : quizSubmittedObj[quizMGETArrayPropertyIndex.extraTime]
                // })
                // newObj[libs.constants.redisKeys.userSessions] = JSON.parse(quizSubmittedObj[libs.constants.redisKeys.userSessions] || JSON.stringify([]));
                let dashboardUpdateTime = parseInt(quizSubmittedObj[quizMGETArrayPropertyIndex.dashboardUpdateTime])
                newObj.dashboardUpdateTime = new Date( isNaN( dashboardUpdateTime) ? quizSubmittedObj[quizMGETArrayPropertyIndex.dashboardUpdateTime] : dashboardUpdateTime );
                newObj._id = quizSubmittedObj[quizMGETArrayPropertyIndex._id];
                newObj.userId = quizSubmittedObj[quizMGETArrayPropertyIndex.userId];
                if (quizSubmittedObj[quizMGETArrayPropertyIndex.extraTime]) {
                    newObj.extraTime = quizSubmittedObj[quizMGETArrayPropertyIndex.extraTime];
                }
                newObj[libs.constants.redisKeys.submittedQuestions] = submittedQuestions;
                newObj[libs.constants.redisKeys.submittedSegments] = JSON.parse(quizSubmittedObj[quizMGETArrayPropertyIndex[libs.constants.redisKeys.submittedSegments]]);

                let tabSwitchCount = quizSubmittedObj[quizMGETArrayPropertyIndex.tabSwitchCount];
                let forceLoginCount = quizSubmittedObj[quizMGETArrayPropertyIndex.forceLoginCount];
                let codePasteCount = quizSubmittedObj[quizMGETArrayPropertyIndex.codePasteCount];
                let loginCount = quizSubmittedObj[quizMGETArrayPropertyIndex.loginCount];
                let camBlockCount = quizSubmittedObj[quizMGETArrayPropertyIndex.camBlockCount];
                let displayName = quizSubmittedObj[quizMGETArrayPropertyIndex.displayName];
                let email = quizSubmittedObj[quizMGETArrayPropertyIndex.email];
                let questionIds = quizSubmittedObj[quizMGETArrayPropertyIndex.questionId] ? JSON.parse( quizSubmittedObj[quizMGETArrayPropertyIndex.questionId] ) : []
                let hasQuizStarted = quizSubmittedObj[quizMGETArrayPropertyIndex.hasQuizStarted] === 'true';
                let userEnterCount =  quizSubmittedObj[quizMGETArrayPropertyIndex.userEnterCount] || 0; 
                let userExitCount =  quizSubmittedObj[quizMGETArrayPropertyIndex.userExitCount] || 0;
                let multipleUserDetected =  quizSubmittedObj[quizMGETArrayPropertyIndex.multipleUserDetected] || 0;
                let illegalObjectDetected =  quizSubmittedObj[quizMGETArrayPropertyIndex.illegalObjectDetected] || 0;
                newObj.tabSwitchCount = tabSwitchCount || 0;
                newObj.tabSwitchInCount = quizSubmittedObj[quizMGETArrayPropertyIndex.tabSwitchInCount] || 0;
                newObj.forceLoginCount = forceLoginCount || 0;
                newObj.codePasteCount = codePasteCount || 0;
                newObj.loginCount = loginCount || 1;
                newObj.fullScreenInCount = quizSubmittedObj[quizMGETArrayPropertyIndex.fullScreenInCount] || 0;
                newObj.fullScreenOutCount = quizSubmittedObj[quizMGETArrayPropertyIndex.fullScreenOutCount] || 0;
                newObj.camBlockCount = camBlockCount || 0;
                newObj.displayName = displayName || "";
                newObj.email = email || "";
                newObj.questionId = questionIds;
                newObj.hasQuizStarted= hasQuizStarted;
                newObj.userEnterCount = userEnterCount ?? 0;
                newObj.userExitCount = userExitCount ?? 0;
                newObj.multipleUserDetected = multipleUserDetected ?? 0;
                newObj.illegalObjectDetected = illegalObjectDetected ?? 0;
                newObj.userSessions = quizSubmittedObj[ quizMGETArrayPropertyIndex.userSessions ] ? 
                JSON.parse( quizSubmittedObj[ quizMGETArrayPropertyIndex.userSessions ] ) : [] ;
                newObj.endTime= quizSubmittedObj[quizMGETArrayPropertyIndex.endTime];
                if(  quizSubmittedObj[quizMGETArrayPropertyIndex.extraTimeExpiry] ){
                    newObj.extraTimeExpiry = quizSubmittedObj[quizMGETArrayPropertyIndex.extraTimeExpiry];
                }
                newObj.sessionTime = quizSubmittedObj[quizMGETArrayPropertyIndex.sessionTime] ? 
                parseInt( quizSubmittedObj[quizMGETArrayPropertyIndex.sessionTime ] ) : 0;
                // newObj.endTime = new Date( quizSubmittedObj.endTime )
                if (options.questionId) {
                    if (quizSubmittedObj[quizSubmittedObj.length - 1]) {
                        let parsedObj = JSON.parse(quizSubmittedObj[quizSubmittedObj.length - 1]);
                        newObj.quizSubmittedQuestion.push(parsedObj);
                        newObj.isPresentInDb = true;
                    }
                }
            } catch (e) {
                console.log("getUserQuizSubmittedSegmentFromDb Error while making object from redis ", e);
                throw e
            }
            if (isCB) callback(null, newObj);
            return newObj;
        } else {
            if( options.fromRedis ){
                return null;
            }
            let projectionObj = {
                'startTime': 1,
                'quizSubmittedQuestion': 1,
                'quizSubmittedSegments': 1,
                'endTime': 1,
                'submittedQuestions': 1,
                'extraTime': 1,
                'idleTime': 1,
                'tabSwitchCount': 1,
                'tabSwitchInCount': 1,
                'forceLoginCount': 1,
                'codePasteCount': 1,
                'loginCount': 1,
                'camBlockCount': 1,
                'displayName': 1,
                'email': 1,
                'userId' : 1,
                'questionId' : 1,
                'loginDetails': 1,
                'tabSwitchReport': 1,
                'userSessions' :1,
                'quizUserDetails' : 1,
                'currentIp' : 1,
                'userIp' : 1,
                'fullScreenReport' : 1,
                'fullScreenInCount' : 1,
                'fullScreenOutCount' : 1,
                'userExitCount': 1,
                'userEnterCount': 1,
                'multipleUserDetected': 1,
                'illegalObjectDetected': 1,
                'aiProctoringReport': 1,
                'segmentSubmissionHistory': 1,
            };
            let quizAttemptObj = await getOneUserQuizSubmittedSegment({
                'quizId': quizId,
                'userId': userId
            }, projectionObj, {});

            if (!quizAttemptObj) {
                 if (isCB) callback(null, quizAttemptObj);
                return false
            }
            quizAttemptObj.fromDb = true;
            quizAttemptObj.hasQuizStarted = ( quizAttemptObj.userSessions && quizAttemptObj.userSessions.length ) ? true : false
            console.log("setQuizSubmittedSegmentsParamsInRedis ", quizId, userId );
            if( !options.addMarks ){
                await setQuizSubmittedSegmentsParamsInRedis(quizAttemptObj, queryObj);
            }
            if (isCB) callback(null, quizAttemptObj);
            return quizAttemptObj;

        }
    } catch (e) {
        console.log("Error while getting user quiz submitted segment from db", e)
        if (isCB) callback({ error: e.message });
        return false;
    }
};

var getDocCount = function ( criteria, callback) {
    services.UserQuizSubmittedSegmentService.getDocCount( criteria, callback )
};

var getUnorderedBulkOpObj = function () {
   return services.UserQuizSubmittedSegmentService.getUnorderedBulkOpObj();
};

const clearAttemptOfTestUser = async (criteria, callback) => {
    let hashName = libs.constants.redisKeys.ongoingQuiz + ":" + criteria.userId + ":" + criteria.quizId;
    let submittedQuestionKeys = hashName + ":" + libs.constants.redisKeys.submittedQuestions;
    let submittedQuesScores = hashName + ":" + libs.constants.redisKeys.submittedQuesScores;
    
    let submittedUserIdKey = redisKeys.submittedUserIds+':'+criteria.quizId;
    let joinedUserIdKey = redisKeys.joinedUserIds+':'+criteria.quizId;
    try{
        await services.RedisServices.redis('del',hashName);
        await services.RedisServices.redis('del',submittedQuestionKeys);
        let obj = await services.RedisServices.redis('hgetall',submittedQuesScores);
        if ( obj && Object.keys(obj).length ) {
            let questionIds = (Object.keys(obj) || []);
            for( let i=0; i<questionIds.length ;++i ){
                let userAttemptListByQuesRedisKey = `${libs.constants.redisKeys.userQuesAttemptsList}:${criteria.quizId}:${criteria.userId}:${questionIds[i]}`;
                await services.RedisServices.redis('del', userAttemptListByQuesRedisKey);
            }
        }
        await services.RedisServices.redis('del',submittedQuesScores);
        await services.RedisServices.redis('del',redisKeys.getLoginTimeStampsList(criteria.quizId,criteria.userId));
        await services.RedisServices.redis('del',redisKeys.getTabSwitchReportList(criteria.quizId,criteria.userId));
        await services.RedisServices.redis('del',redisKeys.getfullScreenReportList(criteria.quizId,criteria.userId));
        await services.RedisServices.redis('srem',submittedUserIdKey,criteria.userId);
        await services.RedisServices.redis('srem',joinedUserIdKey,criteria.userId);
        await services.RedisServices.redis('del',hashName);
        return await services.UserQuizSubmittedSegmentService.removeTestUserAttempt(criteria,callback);
    }
    catch(err){
        if(callback && typeof callback === 'function') callback({error:err?.message ?? err});
        else throw new Error(err?.message ?? err);
    }
};
var addMarks = async function(payload, callback){
    try {
        let {quizId, questionId, userId, additionalScore, session} = payload;
        if (!session)   throw new Error("Session not present");
        let key = libs.constants.redisKeys.ongoingQuiz + ":" + userId + ":" + quizId;
        let findObj = { 'userId': userId, 'quizId': quizId };
        let result = await getUserQuizSubmittedSegmentFromDb(findObj,  { questionId, addMarks : 1 } )
        additionalScore = parseInt(additionalScore);

        // if( result.quizSubmittedQuestion && result.quizSubmittedQuestion[0] ){
        //     result.quizSubmittedQuestion[0].additionalScore = additionalScore;
        //     await services.RedisServices.redis('hset', key, libs.constants.redisKeys.questionIdString + ":" + questionId, JSON.stringify( result.quizSubmittedQuestion[0] ) );
        // }
        if (result.fromDb) {
            // Find original score
            let originalScore = 0;
            for (let index = 0; index < result?.quizSubmittedQuestion?.length; index++) {
                const obj = result?.quizSubmittedQuestion[index];
                if (obj.questionId?.toString() == questionId.toString())    originalScore = obj.score || 0;
                
            } result.quizSubmittedQuestion
            let fObj = {
                'quizId': mongoose.Types.ObjectId(quizId),
                'userId': mongoose.Types.ObjectId(userId),
                'quizSubmittedQuestion.questionId' : mongoose.Types.ObjectId(questionId),
            }
            let updateObj = { 
                $set: {'quizSubmittedQuestion.$.score': parseInt(additionalScore) },
                $push: {'quizSubmittedQuestion.$.scoreUpdationActivity' : {
                    originalScore,
                    'newScore': additionalScore,
                    'updatedBy': session.userId,
                    'updatedAt': Date.now(),
                }} 
            }
            let obj = await services.UserQuizSubmittedSegmentService.updateUserQuizSubmittedSegment(fObj, updateObj, {});
            if( obj ){
                console.log("Question socre updated successfully");
            } else{
                console.log("Question score not updated as not present in db", fObj );
            }
        } else {


            let userSubmittedQuesScoreRedisKey = `${redisKeys.ongoingQuiz}:${userId}:${quizId}:${redisKeys.submittedQuesScores}`;
            let isExist = await services.RedisServices.redis('exists', userSubmittedQuesScoreRedisKey);
            //TODO - current calculated score will be overwritten by this
            if (!isExist)   throw new Error("UserRecord not found in redis");

            const originalScore = await services.RedisServices.redis('hget', userSubmittedQuesScoreRedisKey, questionId);
            await services.RedisServices.redis('hset', userSubmittedQuesScoreRedisKey, questionId, additionalScore);
            await services.RedisServices.redis('rpush', redisKeys.getScoreUpdationActivityList(quizId, userId, questionId), JSON.stringify({
                originalScore,
                'newScore': additionalScore,
                'updatedBy': session.userId,
                'updatedAt': Date.now(),
            }))
        }

        if (callback)   callback(null, result);
        return result;
    } catch (e) {
        console.log("addMarks. Error = ", e);
        if ( callback)  callback(e);
        return {error: e.message};
    }
    
}

const getScoreUpdationActivityList = async (payload) => {
    try {
        const {userId, quizId, questionId} = payload || {};
        if (!userId)		throw new Error("UserId is null");
        if (!quizId)		throw new Error("QuizId is null");
        if (!questionId)	throw new Error("QuestionId is null");

        let scoreUpdationActivityRedisKey = redisKeys.getScoreUpdationActivityList(quizId, userId, questionId);
        let scoreUpdationActivityList = await services.RedisServices.redis('lrange', scoreUpdationActivityRedisKey, 0, -1) || [];
        if (!scoreUpdationActivityList.length) {
            const userQuizSubmittedSegmentObj = await getOneUserQuizSubmittedSegment({quizId, userId}, {}, {}) || {};
            const quizSubmittedQuestionArr = userQuizSubmittedSegmentObj?.quizSubmittedQuestion || [];
            for (let index = 0; index < quizSubmittedQuestionArr.length; index++) {
                const quizSubmittedQuesObj = quizSubmittedQuestionArr[index];
                if (quizSubmittedQuesObj?.questionId?.toString() == questionId) {
                    scoreUpdationActivityList = quizSubmittedQuesObj.scoreUpdationActivity || [];
                    break;
                }
            }
            
        }
	    else scoreUpdationActivityList = scoreUpdationActivityList.map(obj => obj && JSON.parse(obj))

        const userMap = {};
        if(scoreUpdationActivityList.length){
            let userIds = new Set();
            scoreUpdationActivityList.forEach((val) => {
                userIds.add(val.updatedBy);
            });

            userIds = Array.from(userIds);

            let fObj = { _id: { $in: userIds } };
            let pObj = { displayname: 1 };
            const userData = await services.UserService.getUser(fObj, pObj, {});

            userData.forEach((user) => {
                userMap[user._id] = user.displayname;
            });
        }
        return {scoreUpdationActivityList,userMap};
    }catch(err){
        console.log(err);
    }
}

const getAllAttemptOfUser = async (userId) => {
    const findObj =  {userId: mongoose.Types.ObjectId(userId)}
    let userIdsPresentInRedis = [], userDataArray = [];
    
    const activeQuizes = await services.RedisServices.redis('smembers', libs.constants.redisKeys.activeQuizSet);
    /** @type {import('ioredis').Redis} */
    const redisClient = global.redisClient;
    const redisPipelineToGetAllTheUsersOfActiveQuizes =  redisClient.pipeline();
    for (let activeQuiz of activeQuizes) {
        redisPipelineToGetAllTheUsersOfActiveQuizes.smembers(`${libs.constants.redisKeys.joinedUserIds}:${activeQuiz}`);
    }
    const activeUsersPipelineResult = await redisPipelineToGetAllTheUsersOfActiveQuizes.exec();
    const setOfQuizesToGet =  new Set();
    for ( let index = 0; index < activeUsersPipelineResult.length; ++index) {
        let activeUsers = activeUsersPipelineResult[index];
        activeUsers = activeUsers?.[1] ?? activeUsers[0];
        for ( let activeUser of activeUsers ) {
            if (activeUser === userId) {
                setOfQuizesToGet.add(activeQuizes[index]);
                break;
            }
        }
    };

    const dataOfQuizPresentInRedis = await services.QuizService.getQuiz({_id: Array.from(setOfQuizesToGet)}, {}, {}).populate('questionId');
    const mapOfQuizDataByQuizId = new Map();

    dataOfQuizPresentInRedis.forEach((singleQuiz) => {
        mapOfQuizDataByQuizId.set(singleQuiz._id.toString(), singleQuiz);
    });

    let arrayOfQuizs = [];

    let result = [];
    let stepSize = 500;
    const quizIter = setOfQuizesToGet.values();
    let nextQuiz = quizIter.next();
    
    while (!nextQuiz.done){
        let quizId = nextQuiz.value;
        nextQuiz = quizIter.next();
        let redisHashKeyForOngoingQuiz = libs.constants.redisKeys.ongoingQuiz + ':' + userId + ':' + quizId;
        let redisHashKeyForSubmittedQuesScores = redisHashKeyForOngoingQuiz + ':' + libs.constants.redisKeys.submittedQuesScores;
        let redisPipeline = redisClient.pipeline()
        redisPipeline.hgetall(redisHashKeyForOngoingQuiz).hgetall(redisHashKeyForSubmittedQuesScores);
        let pipelineResult = await redisPipeline.exec();
        result = result.concat(pipelineResult);
        arrayOfQuizs.push(mapOfQuizDataByQuizId.get(quizId));
    }

    // user quiz segments from redis
    for (let i = 0; i < result.length; i += 2) {
        let totalTestScore = 0;
        let userQuizSubmittedSegmentObj = result[i] && result[i][1];
        const quizObj = arrayOfQuizs[i/2];

        if (!(userQuizSubmittedSegmentObj && userQuizSubmittedSegmentObj.userId)) continue;
        if (userQuizSubmittedSegmentObj && userQuizSubmittedSegmentObj.tryTest == 'true' && !tryTestQueryParam) continue;

        let submittedQuesScoresObj = (result[i + 1] && result[i + 1][1]) || {};
        let submittedQuesIdsArray = Object.keys(submittedQuesScoresObj);

        const { userId } = userQuizSubmittedSegmentObj;
        userIdsPresentInRedis.push(userId);

        let usedIndex = [];
        let totalAttemptedQuestion = 0, totalScore = 0;

        for (let index = 0; index < quizObj?.quizContent?.length ?? 0; index++) {
            const obj = quizObj.quizContent[index];
        }

        submittedQuesIdsArray.map((questionIdString) => {
            let currentScore = parseInt(submittedQuesScoresObj[questionIdString]) || 0;
            totalScore += currentScore;
            let j = 0;
            for (j = 0; j < usedIndex.length; j++) {
                let usedIndexObj = usedIndex[j];
                if (usedIndexObj.qId === questionIdString) {
                    logger.debug(" Multiple Copy found for user Id : ", userId, " quizId : ", quizId, " questionId : ", questionIdString);
                    if (!usedIndexObj.value) {
                        totalAttemptedQuestion = totalAttemptedQuestion + 1;
                        usedIndexObj.value = true;
                    }
                    break;
                }
            }
            if (j === usedIndex.length) {
                totalAttemptedQuestion = totalAttemptedQuestion + 1;
            }
        })

        let endTime;
        if( userQuizSubmittedSegmentObj.endTime ){
            endTime = parseInt( userQuizSubmittedSegmentObj.endTime );
            endTime = new Date( isNaN( endTime )  ? userQuizSubmittedSegmentObj.endTime : endTime )
        }
        quizObj.questionId.map((question) => { totalTestScore += parseInt(question.score) || 0; });


        let obj = {
            'quizId': quizObj,
            'totalAttemptedQuestion': totalAttemptedQuestion,
            'score': totalScore,
            'percentageScore': ((totalScore / totalTestScore) * 100),
            'timeTaken': libs.util.getTimeDuration(
                parseInt(
                    (new Date(userQuizSubmittedSegmentObj.endTime) - new Date(userQuizSubmittedSegmentObj.startTime)) / 1000
                    - (parseInt((userQuizSubmittedSegmentObj.idleTime || 0) * 60))
                    , 10)),
            endTime,
            '_id': userQuizSubmittedSegmentObj.userId,
            'email': userQuizSubmittedSegmentObj.email || '',
            'displayname': userQuizSubmittedSegmentObj.displayName || '',
            'college': userQuizSubmittedSegmentObj.college || '',
            'branch': userQuizSubmittedSegmentObj.branch,
            'batch': userQuizSubmittedSegmentObj.batch || '',
            'startTime': userQuizSubmittedSegmentObj.startTime,
            'idleTime': parseInt(userQuizSubmittedSegmentObj.idleTime) || 0,
            'rollNo': userQuizSubmittedSegmentObj.rollNo,
            'extraTime': parseInt(userQuizSubmittedSegmentObj.extraTime) || 0,
            'codePasteCount': parseInt(userQuizSubmittedSegmentObj.codePasteCount) || 0,
            'tabSwitchCount': parseInt(userQuizSubmittedSegmentObj.tabSwitchCount) || 0,
            'tabSwitchInCount': parseInt(userQuizSubmittedSegmentObj.tabSwitchInCount) || 0,
            'fullScreenInCount': parseInt(userQuizSubmittedSegmentObj.fullScreenInCount) || 0,
            'fullScreenOutCount': parseInt(userQuizSubmittedSegmentObj.fullScreenOutCount) || 0,
            'loginCount': parseInt(userQuizSubmittedSegmentObj.loginCount) || 1,
            'currentIp': (userQuizSubmittedSegmentObj.currentIp) || '',
            'camBlockCount': parseInt(userQuizSubmittedSegmentObj.camBlockCount) || 0,
            'qualified': quizObj.cutOffMarks ? totalScore >= quizObj.cutOffMarks : true,
            'quizUserDetails': userQuizSubmittedSegmentObj.quizUserDetails ? 
                JSON.parse(userQuizSubmittedSegmentObj.quizUserDetails) : [],
            'userSessions' : JSON.parse( userQuizSubmittedSegmentObj.userSessions || JSON.stringify([]) ),
            'sessionTime' : userQuizSubmittedSegmentObj.sessionTime || 0,
            'isAttempting': (result[i][1].sid)?true:false,
        };
        try{
            if (userQuizSubmittedSegmentObj.questionId)     obj['assignedQuestions'] = JSON.parse(userQuizSubmittedSegmentObj.questionId)
        } catch (error) {
                console.log('Parsing failed for assignedQuestions',userQuizSubmittedSegmentObj.questionId);
                obj['assignedQuestions'] = [];
        }



        userDataArray.push(obj);
    }

    const getAttemptsFromDB = await services.UserQuizSubmittedSegmentService.getUserQuizSubmittedSegment(findObj, {}, {});
    for (singleQuiz of getAttemptsFromDB) {
        singleQuiz.score = 0;
        singleQuiz.totalAttemptedQuestionSet = new Set();
        for (singleSubmittedQuestion of (singleQuiz?.quizSubmittedQuestion ?? [])) {
            if (singleSubmittedQuestion.hasSubmitted) {
                singleQuiz.score += isNaN(parseInt(singleSubmittedQuestion.score))? 0 : parseInt(singleSubmittedQuestion.score);
                singleQuiz.score += singleSubmittedQuestion.additionalScore;
                singleQuiz.totalAttemptedQuestionSet.add(singleSubmittedQuestion._id.toString());
            }
        }
        singleQuiz.totalAttemptedQuestion = singleQuiz.totalAttemptedQuestionSet.size
        if (singleQuiz.cutOffMarks) {
            singleQuiz.qualified = (singleQuiz.cutOffMarks  <= singleQuiz.score );
        }
        delete singleQuiz.quizSubmittedQuestion;
        delete singleQuiz.totalAttemptedQuestionSet;
    }
    userDataArray = [...userDataArray, ...getAttemptsFromDB];
    return userDataArray;
}

/**
 * 
 * @param {string} quizId 
 * @param {string} userId 
 * @param {{ skipDB: boolean }} config 
 * @returns 
 */
const getSegmentSubmissionHistoryFromRedis = async (quizId, userId, config) => {
    try {
        const result = [];
        const userQuizSubmittedSegmentString = libs.constants.redisKeys.getOnGoingQuiz(userId, quizId);
        const isPresentInRedis = await services.RedisServices.redis('exists', userQuizSubmittedSegmentString);
        if (isPresentInRedis) {
            const segmentSubmissionHistory = await services.RedisServices.redis('lrange', libs.constants.redisKeys.getSegmentSubmissionHistory(quizId, userId), 0, -1);
            return (segmentSubmissionHistory ?? []).map(ele => {
                const obj = libs.util.parseSegmentSubmissionHistoryRedisString(ele);
                return obj;
            }); 
        }
        if (!config.skipDB) {
            const fromDB = await services.UserQuizSubmittedSegmentService.getOneUserQuizSubmittedSegment({
                quizId: quizId,
                userId: userId,
            }, {
                segmentSubmissionHistory: 1,
            }, {});
            return fromDB?.segmentSubmissionHistory ?? [];
        }
    } catch (error) {
        console.error(error);
        return [];
    }

}

module.exports = {
    getUserQuizSubmittedSegment: getUserQuizSubmittedSegment,
    getOneUserQuizSubmittedSegment: getOneUserQuizSubmittedSegment,
    updateUserQuizSubmittedSegment: updateUserQuizSubmittedSegment,
    updateManyUserQuizSubmittedSegment,
    performAggregation: performAggregation,
    submitSegment: submitSegment,
    addUserQuizSubmittedSegment: addUserQuizSubmittedSegment,
    getUserQuizSubmittedSegmentFromDb : getUserQuizSubmittedSegmentFromDb,
    setQuizSubmittedSegmentsParamsInRedis : setQuizSubmittedSegmentsParamsInRedis,
    getDocCount : getDocCount,
    getUnorderedBulkOpObj : getUnorderedBulkOpObj,
    clearAttemptOfTestUser : clearAttemptOfTestUser,
    addMarks : addMarks,
    getScoreUpdationActivityList,
    getAllAttemptOfUser,
    getSegmentSubmissionHistoryFromRedis,
}
