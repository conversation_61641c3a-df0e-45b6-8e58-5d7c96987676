/* import fonts */
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Black.ttf')
		format('truetype');
	font-weight: 900;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Heavy.ttf')
		format('truetype');
	font-weight: 700;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Medium.ttf')
		format('truetype');
	font-weight: 500;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir Book';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Book.ttf') format('truetype');
	font-weight: 500;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir Book';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-BookOblique.ttf')
		format('truetype');
	font-weight: 400;
	font-style: italic;
}
/* import fonts */
.row {
	width: 100%;
}
.hide {
	display: none;
}
.full-page-container{
	/* height: calc(100vh - 130px); */
	height: calc(100vh - 53px);
    overflow: auto;
}
body {
	/* font-family: 'Montserrat', sans-serif; */
	font-family: 'Hind', sans-serif;
	font-size: 14px;
	overflow: hidden;
}
.addCourseContent-container {
	/* width: 90%; */
	display: block;
	margin: auto;
	/* margin-top: 20px; */
	margin-bottom: 50px;
	padding: 150px 96px 0px 96px;
}

.coursecontent-change-container {
	width: 90%;
	display: block;
	margin: auto;
	margin-top: 20px;
}
.addCourseContent-title h2 {
	color: var(--primary-color);
	font-weight: 500;
}
.section-selection {
	margin-top: 10px;
}
.section-selection a {
	font-family: 'Montserrat', sans-serif;
	margin-right: 20px;
	cursor: pointer;
	font-weight: 400;
	font-size: 17px;
	display: inline-block;
}

.section-selection .active::after {
	content: '';
	display: block;
	width: 100%;
	border-bottom: 3px solid var(--primary-color);
	position: relative;
	top: 2px;
}
.section-container {
	margin-top: 20px;
}
.section-container p {
	color: #333333;
	margin-bottom: 15px;
}
.sections {
	margin-top: 16px;
}
.sections.ui-sortable-helper .sections-head{
	background: #f2f2f2;
}

.table-body.ui-sortable-helper {
	background: #f2f2f2;
	padding-top: 10px;
	border-radius: 5px;
}
.sections .sections-head {
	padding: 15px 20px;
	box-shadow: 0px 0px 4px #00000029;
	border-radius: 10px;
	cursor: pointer;
}
.sections .sections-head p {
	display: inline-block;
	margin-bottom: 0;
}
.sections .sections-head .section-title {
	font-weight: 500;
	cursor: default;
}
.sections .sections-head .section-edit {
	width: 15px;
	height: 15px;
	margin-left: 15px;
}
.sections .sections-head .section-desc {
	margin-left: 20px;
	font-weight: 400;
	cursor: default;
	font-size: 13px;
}
.sections .sections-head .section-arrow {
	float: right;
	margin-right: 10px;
	margin-top: -3px;
}
.sections-head.opened .section-arrow {
	float: right;
	margin-right: 10px;
	margin-top: 1px !important;
}
.sections .sections-head .section-arrow[data-customTooltip]::after {
	bottom: 14px;
}
.sections .sections-head .section-drag {
	float: right;
}
.section-drag img {
	width: 20px;
	height: 20px;
}
.sections-head.opened {
	background-color: #f8f8f8;
	border: 1px solid #d6d6d6;
	box-shadow: none;
	border-radius: 0px !important;
	border-top-left-radius: 15px !important;
	border-top-right-radius: 15px !important;
}
.sections-head.opened .edit-title-input{
	background-color: #f8f8f8 !important;
}
.title-save:hover{
	opacity: 1 !important;
}
.title-unsaved:hover{
	opacity: 1 !important;
}
.sections-head{
	position: relative;
}
.onselect-delete{
	background-color: var(--primary-light);
    width: 99.9%;
    height: 100%;
    z-index: 1;
    position: absolute;
	margin-left: -19.5px;
	margin-top: -15px;
	border-top-left-radius: 15px !important;
	border-top-right-radius: 15px !important;
	display: none;
	cursor: default;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
}
.onselect-delete .trash{
	font-family: 'Avenir';
	color: var(--primary-color);
	font-weight: 500;
	border: none;
    outline: none;
	background-color: transparent;
	cursor: pointer;
}
.onselect-delete .trash svg {
	width: 15px;
	height: 15px;
	margin-top: -5px;
	margin-right: 5px;
}
.sections .section-inside {
	border: 1px solid #d0d0d0;
	/* height: 30vh; */
	padding: 10px 5px;
	box-shadow: 0px 0px 3px #00000029;
	display: none;
	width: 100%;
	border-top: none;
	border-bottom-left-radius: 10px;
	border-bottom-right-radius: 10px;
}

.dragTr,
.content-action-icon {
	position: relative;
	right: 30%;
	top: 2%;
}
.dragTr {
	width: 20px;
	height: 20px;
}
.content-action-icon {
	width: 18px;
	height: 18px;
}
.dragTr:hover {
	width: 20px;
	height: 20px;
}

.dragTr:hover,
.content-action-icon:hover {
	position: relative;
	right: 30%;
	top: 2%;
}

.add-new- {
	/* margin-left: 10%; */
	text-align: left;
	font-weight: 500;
	padding-left: 120px;
}
.add-new- button {
	border: none;
	outline: none;
	background-color: white;
	color: var(--primary-color);
	font-weight: 500;
}
.add-new- .trash {
	float: right;
	position: relative;
	/* top: 12px; */
	color: #ACACAC;
}
.add-new- .trash:hover{
	color: #333333;
}
.add-new- .trash img {
	width: 15px;
	height: 15px;
}
/* bottom save btn css starts */
.save-btn-container {
	background-color: #fafafa;
	/* padding: 35px; */
	position: fixed;
	bottom: 0;
	width: 100%;
	height: 80px;
	padding-top: 25px;
	padding-right: 25px;
	z-index: 2;
	padding-left: 150px;
	display: none;
}
.save-btn-container .save {
	float: left;
	background: var(--primary-color);
	border: 1px solid var(--primary-color);
	border-radius: 5px;
	color: white;
	padding: 8px 30px;
	margin-top: -5px;
	font-size: 16px;
}
.save-btn-container .publish {
	float: right;
	border-radius: 5px;
	color: white;
	padding: 5px 20px;
	color: var(--primary-color);
	margin-left: 10px;
	border: none;
	background: none;
	font-weight: 500;
}
.save-btn-container p {
	float: right;
	color: #969696;
	margin-right: 20px;
	margin-top: 5px;
}
.switch {
	float: right;
	margin-right: 10px;
}
/* slider css starts */
.switch {
	position: relative;
	display: inline-block;
	width: 60px;
	height: 34px;
}

.switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: 0.4s;
	transition: 0.4s;
}

.slider:before {
	position: absolute;
	content: '';
	height: 26px;
	width: 26px;
	left: 4px;
	bottom: 4px;
	background-color: white;
	-webkit-transition: 0.4s;
	transition: 0.4s;
}

input:checked + .slider {
	background-color: var(--primary-color);
}

input:focus + .slider {
	box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
	-webkit-transform: translateX(26px);
	-ms-transform: translateX(26px);
	transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
	border-radius: 34px;
}

.slider.round:before {
	border-radius: 50%;
}

.sections-head .switch {
	width: 55px;
	height: 25px;
}
.sections-head .slider:before {
	height: 20px;
	width: 20px;
	left: 4px;
	bottom: 3px;
}
.sections-head input:checked + .slider {
	background-color: var(--slider-bg);
}
.sections-head input:checked + .slider::before {
	background-color: var(--primary-dark);
}
/* slider css ends */
/* bottom save btn css ends */
.create-new-section {
	display: block;
	margin: auto;
	margin-bottom: 150px;
	text-align: center;
	cursor: pointer;
	font-weight: 500;
	color: white !important;
	width: max-content;
	height: 40px;
	background-color: var(--primary-color);
	display: flex;
	align-items: center;
	border-radius: 4px;
	padding: 12px 22px;
	padding-top: 13px !important;
}
.fa-ellipsis-v {
	color: #b9b9b9;
}
.edit-tuts-form {
	width: 300px;
	/* height: 100px; */
	background: #ffffff;
	box-shadow: 0px 0px 10px #00000029;
	border-radius: 8px;
	/* padding: 10px; */
	z-index: 3;
	position: absolute;
	margin-left: 15px;
	/* padding-top: 30px; */
	transition: 0.2s ease-in-out;
	transform: scale(1);
}
.section-inside .edit-tuts-form{
	width: 205px !important;
	padding-top: 8px;
	padding-bottom: 8px;
}

.freezedQuestion .section-inside .edit-tuts-form{
	padding-bottom: 0px !important;
}

.section-inside .edit-tuts-form{
	margin-left: -175px;
}
.edit-tuts-form-hide{
	transition: 0.2s ease-in-out;
	transform: scale(0);
}
.edit-tuts-form img {
	width: 20px;
	height: 20px;
	display: block;
	margin: auto;
}
.edit-tuts-form img:hover {
	width: 20px;
	height: 20px;
	display: block;
	margin: auto;
}
.edit-tuts-form a {
	font-family: 'Hind', sans-serif !important;
	text-decoration: none !important;
	color: #848484;
	font-size: small;
	text-align: center;
	cursor: pointer;
	text-transform: lowercase;
}
.edit-tuts-form a:hover{
	color: #848484;
}
.edit-tuts-form.add-class-new {
	text-align: center !important;
}
.edit-tuts-form.add-class-new {
	width: 200px;
	padding: 15px 5px 20px 5px;
}
.edit-tuts-form.add-class-new .col-md-6 {
	padding: 0;
	margin: 5px 0;
}
.edit-tuts-form.bulk-optionsss{
	transition: none;
	position: absolute;
    right: -5px;
}
.hover-focus{
	padding: 5px;
	padding-top: 8px;
}
.hover-focus:hover{
	background-color: #f7f7f7;
	border-radius: 5px;
}
/* radio btn style starts */
.container-radio {
	display: block;
	position: relative;
	padding-left: 25px;
	cursor: pointer;
	font-size: 20px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	/* margin-top: -15px; */
}
.container-radio input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
}
.checkmark {
	position: absolute;
	top: -1px;
	left: 0;
	height: 20px;
	width: 20px;
	background-color: white;
	border-radius: 3px;
	box-shadow: 0px 0px 4px #00000029;
	border: 1px solid #C7C7C7;
}
.radio-btnn .checkmark{
	border-radius: 50%;
}

.checkmark:after {
	content: '';
	position: absolute;
	display: none;
}

.container-radio input:checked ~ .checkmark:after {
	display: block;
}

.container-radio .checkmark:after {
	top: 4px;
	left: 4px;
	width: 10px;
	height: 10px;
	border-radius: 3px;
	background: #4b4b4b;
}
.radio-btnn.container-radio .checkmark:after{
	border-radius: 50% !important;
}
/* radio btn style ends */
.edit-tuts-form .container-radio {
	/* height: 30px;
	margin: 0 !important;
	padding-top: 5px; */
	margin-top: 10px !important;
}
.fa-caret-up,
.fa-caret-down {
	color: var(--primary-color);
	font-size: x-large !important;
}
.edit-title-input {
	border: none;
	/* background-color: #f8f8f8; */
}
.edit-title-input:focus {
	outline: none;
}

.th-border::before {
	content: '';
	display: block;
	width: 60%;
	border-bottom: 1px solid #d3d3d3;
	position: relative;
	top: 25px;
}
.col-5.th-border::before{
	width: 20%;
}
.th-border.th-border-1::before{
	width: 50%;
}
.th-border.th-border-2::before{
	width: 20%;
}
.th-border.th-border-3::before{
	width: 30%;
}

/* css for tables starts */
.table-head {
	padding: 20px;
	font-family: 'Montserrat', sans-serif;
	margin: 0 !important;
	color: #000000;
	font-weight: 600;
	padding-bottom: 30px;
	font-size: 15px;
	padding-left: 21px !important;
	padding-bottom: 15px;
}
.q-title-th{
	padding-left: 0px !important;
	position: relative;
	right: 6px;
}
.table-body {
	height: 56px;
	/* line-height: 60px; */
	margin: 0 !important;
	transition: padding 0.2s ease-in-out, background-color 0.2s ease-in-out ,margin 0.2s ease-in-out;
	font-size: 15px;
}
.table-body .col-1,
.table-body .col-2 {
	margin-top: 10px;
}
.table-body .col-5 {
	margin-top: 10px;
}
.container-radio {
	font-size: 15px !important;
	margin-top: 0;
}
.section-inside .question-title {
	font-weight: 500;
	color: var(--primary-color);
	padding-left: 12px;
}
.table-body.selected .question-title{
	margin-top: 10px;
}
.section-inside .question-title a {
	font-weight: 500;
	color: #333333;
	text-decoration: none;
	width: fit-content;
	height: 19px;
}
.freezedQuestion .question-title a{
	/* color: #ACACAC; */
}
.freezedQuestion .q-typee .c_span_content{
	/* color: #ACACAC; */
}
.freezedQuestion .settingsPrint{
    /* color: #ACACAC; */
}
.freezedQuestion div, .freezedQuestion p{
	/* color: #ACACAC; */
}
.freeze-btn{
	margin-top: 4px;
    background: white;
    border: none !important;
	box-shadow: none;
    border-radius: 3px;
    color: #212529;
    padding: 8px 10px;
    white-space: nowrap;
    cursor: pointer;
    text-align: left;
	margin-bottom: 4px !important;
}
.freeze-btn:hover{
	color: #525252 !important;
	background: #0000001a !important;
}
.settingsPrint{
	max-height: 43px;
    overflow: auto;
	font-size: 13px;
}
.table-body.selected {
	height: 56px;
	background-color: #f2f2f2;
	/* margin: 10px !important; */
	width: 100%;
	/* height: 100px; */
	/* line-height: 10px; */
	/* padding: 5px; */
	padding-top: 7px;
	border-radius: 5px;
	margin-top: 1px !important;
	margin-bottom: 5px !important;
	transition: padding 0.2s ease-in-out, background-color 0.2s ease-in-out ,margin 0.2s ease-in-out;
}
.table-body.selected  .s-numbers{
	margin-top: 0px !important;
}
.table-body.selected .dots-btn {
	background-color: #efefef !important;
	margin-top: -3px;
}
.dots-btn-bulk-action{
	color: rgb(5, 5, 5);
    padding: 2px 7px;
    border-radius: 3px;
    cursor: pointer;
    margin-left: 20px;
    display: inline-block;
    position: relative;
    top: 10px;
    transform: rotate(90deg);
}
.dots-btn-frezz{
	color: rgb(5, 5, 5);
    padding: 2px 7px;
    border-radius: 3px;
    cursor: pointer;
    margin-left: 20px;
    display: inline-block;
    position: relative;
    top: 10px;
    transform: rotate(90deg);
}
.question-title .level {
	display: block;
	color: #848484;
	background-color: #e8e8e8;
	border-radius: 5px;
	/* height: 10px; */
	width: 80px;
	text-align: center;
	font-weight: 400;
}
/* css for tables ends */

.add-description {
	border: none;
	width: 100%;
	background-color: #fffbf8;
	height: 80px;
}
.add-description:focus {
	outline: none;
}
.add-description::placeholder {
	color: #bebebe;
}
.section-description-container {
	width: 95%;
	display: block;
	margin: auto;
	background-color: #fffbf8;
	border-radius: 5px;
	padding: 10px 20px;
	margin-top: 15px;
	position: relative;
}
.section-description-container .title {
	margin: 0;
	color: var(--primary-color);
	font-weight: 500;
}
.close-description-icon{
	width: 15px;
    height: 15px;
    position: absolute;
    top: -5px;
    right: -5px;
}
.complete-class-button{
	cursor: pointer;
}
.common-quiz-header-container {
	padding: 30px 98px 0px 98px;
	border-bottom: 1px solid #ededed;
	font-family: 'Avenir';
	position: fixed;
	width: 100%;
	background-color: var(--primary-bg);
	z-index: 8;
}
.all-tests {
	font-family: 'Avenir';
	font-weight: 400;
	font-size: 17px;
	margin-bottom: 0;
}
.all-tests a{
	font-family: 'Avenir';
	font-weight: 400;
	font-size: 17px;
	color: var(--primary-text);
	text-decoration: none;
}
.all-tests i {
	color: var(--primary-color);
	position: relative;
	left: 5px;
	top: 2px;
	font-size: 20px;
}
.quiz-name-options-container {
	display: grid;
	grid-template-columns: 2fr 1.2fr;
	gap: 10px;
}
.quiz-name {
	color: var(--primary-color);
	font-family: 'Avenir';
	font-weight: 500;
	font-size: 29px;
	margin-bottom: 5px;
	max-width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.btn-1 {
	border: none;
	border-radius: 3px;
	padding: 5px 20px;
	background: var(--primary-color);
	color: white !important;
	font-family: 'Avenir';
	font-weight: 500;
	margin-right: 10px;
	width: max-content;
	/* margin: auto; */
	margin-left: auto;
	font-size: 16px;
	cursor: pointer;
}
.theme-btn{
	border-radius: 4px;
	background: var(--primary-color);
	color: white !important;
	font-family: 'Avenir';
	font-weight: 500;
	margin-right: 10px;
}
.theme-btn-outline{
	font-family: 'Avenir';
	font-weight: 500;
    color: var(--primary-color);
}
.theme-btn-outline:hover{
	color: var(--primary-color);
}
.btn-2 {
	border: none;
	border-radius: 3px;
	padding: 5px 20px;
	background: var(--primary-light);
	color: var(--primary-color);
	font-family: 'Avenir';
	font-weight: 500;
	margin-right: 10px;
}
.page-tabs-container .tabs {
	display: inline-block;
	margin-right: 10px;
	font-family: 'Avenir';
	font-size: 17px;
	padding-bottom: 5px;
}
.page-tabs-container .tabs a {
	text-decoration: none;
	color: inherit;
}
.page-tabs-container .tabs.active {
	border-bottom: 4px solid var(--primary-color);
}

.send-invite-container {
	width: 500px;
	height: calc(100vh - 53px);
	box-shadow: 0px 3px 10px #00000029;
	position: absolute;
	right: 0;
	background-color: white;
	z-index: 11;
}
.close-sidebar-btn-container {
	font-family: 'Open Sans', sans-serif;
	color: #a7a7a7;
	font-size: 17px;
	text-align: right;
	cursor: pointer;
	margin-left: auto;
	margin-top: 20px;
	margin-right: 25px;
	width: max-content;
}
.close-sidebar-btn-container img {
	width: 18px;
	height: 18px;
	margin-top: -2px;
	margin-left: 4px;
}
.close-sidebar-btn-container img:hover {
	margin-top: -2px;
	margin-left: 4px;
}
.invite-text-container {
	padding: 15px 0px;
	margin: 0px 43px 0px 40px;
}
.invite-text {
	font-family: 'Avenir Book';
	font-weight: 400;
	font-size: 32px;
	margin-bottom: 0;
}
.invite-left-text {
	font-family: 'Avenir Book';
	font-weight: 400;
	font-size: 17px;
}
.invite-form-container {
	padding: 10px 40px;
	padding-top: 0 !important;
	max-height: calc(100vh - 200px);
	overflow: auto;
}
.invite-field {
	margin-top: 20px;
}
.invite-field .input-label {
	font-weight: 500;
	color: var(--primary-color);
	margin-bottom: 0.5rem;
	font-size: 17px;
}
.invite-field input {
	border: none;
	border-bottom: 1px solid #dedede;
	width: 100%;
	outline: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
}
.invite-field textarea {
	border: none;
	border-bottom: 1px solid #dedede;
	width: 100%;
	outline: none;
	resize: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
}
.invite-field .tag-editor {
	border: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
    background-color: white;
    min-height: unset;
	max-height: 80px;
    overflow: auto;
	/* margin-right: 50px; */
}
.invite-field .tag-editor .tag-editor-tag {
	background: white !important;
	color: #333333 !important;
	cursor: default !important;
}
.invite-field .tag-editor .tag-editor-delete {
	background: white !important;
	color: #333333 !important;
}
.invite-field .tag-editor .tag-editor-delete i {
	color: #333333 !important;
}
.invite-field .tag-editor li {
	margin: 0 !important;
	padding: 0 !important;
}
.invite-field .tag-editor .placeholder div {
	padding: 0 !important;
	color: #333333 !important;
	opacity: 0.8 !important;
}
.invite-field .tag-editor .tag-editor-delete i:before{
    color: #8ba7ba;
}
.TemplateVariable-group{
	background-color: white;
	border-bottom-left-radius: 12px;
	border-bottom-right-radius: 12px;
	border: 1px solid #f2f2f2;
	box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
}
.btn.btn-orange{
	color: #212529;
	font-size: 12px;
	white-space: nowrap;
	padding-top: 8px;
	padding-bottom: 8px;
	font-weight: 500;
	border-radius: 0;
	background-color: #FFFF;
}
.btn.btn-orange:hover{
	color: #212529;
}
.btn.btn-orange:nth-child(1){
	padding-right: 0px;
	border-bottom-left-radius: 12px !important;
}
.btn.btn-orange:nth-child(2){
	padding-left: 4px;
	padding-right: 4px;
}
.btn.btn-orange:nth-child(3){
    padding-left: 2px;
	padding-right: 4px;
}
.btn.btn-orange:nth-child(4){
	padding-left: 0px;
	padding-right: 10px;
	border-bottom-right-radius: 12px !important;
}
.btn.btn-orange:nth-child(1)::after,.btn.btn-orange:nth-child(2)::after,.btn.btn-orange:nth-child(3)::after{
	content: "";
	margin-left: 18px;
	border-right: 1px solid #212529 !important;
}
.send-invite-btn {
	background: var(--primary-color);
	border: none;
	color: white;
	padding: 8px 20px;
	border-radius: 4px;
	/* font-weight: 500; */
	font-size: 15px;
	margin-left: auto;
	display: block;
	margin-top: 20px;
	font-family: 'Avenir', sans-serif;
}

/* mobile view */
@media only screen and (max-width: 992px){
	.sections .sections-head .section-desc{
		width: 26%;
	}
	.addCourseContent-container{
		width: 95%;
	}
	.sections .section-inside{
		width: max-content;
	}
	.sections .sections-head{
		padding: 15px 10px;
	}
	.sections .sections-head .section-title{
		width: 10%;
		word-break: break-all;
		font-size: 12px;
	}
	.dots-btn{
		margin-left: 10%;
	}
}
/* mobile view */

/* new content css */
.onselect-delete{
	padding-left: 5.3%;
    font-weight: 500;
}
.selected-count-container{
	font-family: 'Avenir';
	color: var(--primary-color);
	font-weight: 500;
	font-size: 15px;
}
.selected-count{
	color: var(--primary-color);
	margin-right: 5px;
}
.add-btn-new{
	outline: none !important;
	box-shadow: none !important;
}
.add-btn-new:hover{
	background-color: var(--primary-color);
	color: white !important;
	outline: none !important;
}
.add-btn-new:focus{
	outline: none !important;
	box-shadow: none !important;
}

/* add question modal css */
button{
	outline: none !important;
	box-shadow: none !important;
}
.poll-number{
	display: inline-block;
	max-width: 150px;
	margin-left: 10px;
}

/* add question modal css */
button{
	outline: none !important;
	box-shadow: none !important;
}
.checkbox-col{
	padding-top: 10px !important;
}
table th{
	color: #333333;
	font-size: 15px;
	font-weight: 600;
	text-transform: uppercase;
	font-family: 'Avenir',sans-serif !important;
	white-space: nowrap;
}
table td{
	color: #333333;
	line-height: 21px;
	font-size: 15px;
	font-weight: 400;
	font-family: 'Hind',sans-serif;
}
table td a{
	color: var(--primary-color);
	font-weight: 500;
	text-decoration: none !important;
}
table td a:hover{
	color: var(--primary-color);
}
input[type="checkbox"]:before {
	position: relative;
	display: block;
	width: 20px;
	height: 20px;
	border: 1px solid #C7C7C7;
	border-radius: 3px;
	box-shadow: 0px 0px 4px #00000029;
	content: "";
	background: white;
	cursor: pointer;
	margin-left: -2px;
 }
 input[type="checkbox"]:after {
	position: relative;
	display: block;
	left: 0px;
	top: -20px;
	width: 20px;
	height: 20px;
	border: 1px solid #C7C7C7;
	border-radius: 3px;
	box-shadow: 0px 0px 4px #00000029;
	content: "";
	margin-left: -2px;
	cursor: pointer;
}
input[type="checkbox"]:checked:after {
	width: 12px;
	height: 12px;
	border-radius: 3px;
	background: #4b4b4b;
	margin: 0 !important;
	text-shadow: none;
	box-shadow: 0px 0px 4px #00000029 !important;
	border: none !important;
	outline: none !important; 
	position: relative;
	margin-top: 4px !important;
	margin-left: 2px !important; 
	cursor: pointer;
}
.card-header{
	color: #333333;
	font-size: 15px;
	font-weight: 500;
	font-family: 'Hind',sans-serif !important;
}

.github-shadow{
	box-shadow: rgba(27, 31, 35, 0.04) 0px 1px 0px, rgba(255, 255, 255, 0.25) 0px 1px 0px inset;
}


.ql-editor, .ql-toolbar.ql-snow{
	font-family: 'Avenir',sans-serif !important;
}
.suffle-arrow{
	width: 15px;
    height: 15px;
	margin-left: 15px;
	opacity: .5;
}
.suffle-arrow:hover{
	margin-left: 15px;
}
.suffle-arrow.active{
	opacity: .9 !important;
}
.suffle-count{
	position: relative;
	top: .8px;
	left: 2px;
	cursor: default !important;
}

.suffle-input-box{
	display: inline-block;
	width: auto;
	height: 25px;
	margin-left: 5px;
	position: relative;
	top: 8px;
	border: 1px solid #D1D1D1;
	border-radius: 2.5px;
	background-color: #fff;
	box-shadow: inset 0px 0px 2px 0px #00000029 !important;
	padding-left: 4px;
	margin-top: -9px;
}
.suffle-input{
	width: 20px;
	height: 23px;
	float: left;
	margin: 0;
	border: 0;
	padding-left: 0 !important;
	padding-right: 0px !important;
}
.suffle-input-box input[type=number]::-webkit-inner-spin-button, 
.suffle-input-box input[type=number]::-webkit-outer-spin-button { 
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                margin: 0; 
            }

.suffle-button{
	border: none !important;
	height: 20px;
	width: 20px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: var(--primary-color);
	float: right;
	margin-top: 1px;
}
.suffle-button img{
	width: 10px;
	height: 10px;
}

.question-title a{
	overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

/* scroll bar fix */
.full-page-container::-webkit-scrollbar-track{
	margin-top: 140px;
}
