var defaultCode = {
    "C": `#include <stdio.h>
int main()
{

    return 0;
}`,
    "Cpp": `#include<iostream>
#include<cstdio>
#include<cmath>
using namespace std;
int main()
{
   return 0;
}`,
    'C#' : `
using System.IO;
using System;

class Program
{
    static void Main()
    {
        // Write your code here
    }
}
`,
    'Php' : `
<?php
    // Type your code here
    `
};

var codeStubs = {
    "7": {
        lang : "C",
        head: "/* add c headers if necessary*/",
        body: defaultCode.C,
        tail: "/* add tail for c if necessary*/",
        solution: ""
    },

    "17": {
        lang : "C99",
        head: "/* add c headers if necessary*/",
        body: defaultCode.C,
        tail: "/* add tail for c if necessary*/",
        solution: ""
    }
    ,
    "77": {
        lang : "C++",
        head: "/* add c++ headers if necessary*/",
        body: defaultCode.Cpp,
        tail: "/* add tail for c++ if necessary*/",
        solution: ""
    },

    "18": {
        lang : "C++11",
        head: "/* add c++ headers if necessary*/",
        body: defaultCode.Cpp,
        tail: "/* add tail for c++ if necessary*/",
        solution: ""
    },

    "19": {
        lang : "C++14",
        head: "/* add c++ headers if necessary*/",
        body: defaultCode.Cpp,
        tail: "/* add tail for c++ if necessary*/",
        solution: ""
    },

    "8": {
        lang : "Java",
        head: "",
        body: `import java.util.Scanner;
// Other imports go here
// Do NOT change the class name
class Main{
    public static void main(String[] args)
    {
        // Write your code here
    }
}`,
        tail: "",
        solution: ""
    },
    "4" : {
        lang : "JavaScript",
        head: "",
        body: "/* Type your javascript code here */",
        tail: "",
        solution: ""
    },
    "11" : {
        lang : "Bash Script",
        head: "",
        body: "# Start writting your script",
        tail: "",
        solution: ""
    },
    "5" : {
        lang : "SQL",
        head: "CREATE TABLE COMPANY(\n" +
        "   ID INT PRIMARY KEY     NOT NULL,\n" +
        "   NAME           TEXT    NOT NULL,\n" +
        "   AGE            INT     NOT NULL,\n" +
        "   ADDRESS        CHAR(50),\n" +
        "   SALARY         REAL\n" +
        ");",
        body: "",
        tail: "",
        solution: ""
    },
    "0" : {
        lang : "Python 2",
        head: "",
        body: "# Type your code here.",
        tail: "",
        solution: ""
    },
    "20" : {
        lang : "Python 3",
        head: "",
        body: "# Type your code here",
        tail: "",
        solution: ""
    },
    "1" : {
        lang : "Ruby",
        head: "",
        body: "# Type your code here",
        tail: "",
        solution: ""
    },
    "10": {
        lang : "C#",
        head: "/* add c# headers if necessary*/",
        body: defaultCode['C#'],
        tail: "/* add tail for c# if necessary*/",
        solution: ""
    },
    "3": {
        lang : "Php",
        head: "/* add Php headers if necessary*/",
        body: defaultCode.Php,
        tail: "/* add tail for Php if necessary*/",
        solution: ""
    },
    "23" : {
        lang : "MySql",
        head: "",
        body: "",
        tail: "",
        solution: ""
    },
    "24" :{
        lang : "Oracle",
        head: "",
        body: "",
        tail: "",
        solution: ""
    },
    "22" :{
        lang : "HTML, CSS, JS",
        head: "",
        body: "",
        tail: "",
        solution: ""
    }
}

//for integrated development env



var codeStubsIde = {
    "7": {
        lang : "C",
        body: defaultCode.C,
    },
    "17": {
        lang : "C99",
        body: defaultCode.C,
    },
    "77": {
        lang : "C++",
        body: defaultCode.Cpp,
    },
    "18": {
        lang : "C++11",
        body: defaultCode.Cpp,
    },
    "19": {
        lang : "C++14",
        body: defaultCode.Cpp,
    },
    "8": {
        lang : "Java",
        body: `import java.util.Scanner;
// Other imports go here
// Do NOT change the class name
class Main
{
    public static void main(String[] args)
    {
        // Write your code here
    }
    }`,
    },
    "4" : {
        lang : "JavaScript",
        body: "//Type your code here",
    },
    "11" : {
        lang : "Bash Script",
        body: "# Start writting your script",
    },
    "5" : {
        lang : "SQL",
        body: "",
    },
    "0" : {
        lang : "Python 2",
        body: "# Type your code here",
    },
    "20" : {
        lang : "Python 3",
        body: "# Type your code here",
    },
    "1" : {
        lang : "Ruby",
        body: "# Type your code here",
    },
    "10": {
        lang : "C#",
        body: defaultCode['C#'],
    },
    "3": {
        lang : "Php",
        body: defaultCode.Php
    },
    "23" : {
        lang : "MySql",
        body: "",
    },
    "24" :{
        lang : "Oracle",
        body: "",
    },
    "22" :{
        lang : "HTML, CSS, JS",
        body: "",
    }
}

var langCodeToLangString = {
    "7": "C",
    "17": "C99",
    "77": "C++",
    "18": "C++11",
    "19": "C++14",
    "8":"Java",
    "4": "JavaScript",
    "11": "Bash Script",
    "0": "Python 2",
    "20": "Python 3",
    "1" : "Ruby",
    "10" : "C#",
    "3" : "Php",
    "24" : "Oracle",
    "22" : "HTML, CSS, JS",
    "5" : "SQL",
    "23" : "MySql",
}

var langStringToLangCode = {
    "C": "7",
    "C99": "17",
    "C++": "77",
    "C++11": "18",
    "C++14": "19",
    "Java":"8",
    "JavaScript": "4",
    "Bash Script": "11",
    "SQL":"5",
    "Python 2": "0",
    "Python 3" : "20",
    "Ruby" : "1",
    "C#" : "10",
    "Php" : "3",
    "SQL" : "5",
    "MySql": "23",
    "Oracle": "24",
    "HTML, CSS, JS": "22",
};
