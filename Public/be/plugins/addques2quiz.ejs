<!DOCTYPE html>
<html>
    <head>
        <% include partials/head %>
        <link rel="stylesheet" href="/be/css/separate/vendor/bootstrap-select/bootstrap-select.min.css">
        <link rel="stylesheet" href="/be/css/separate/vendor/bootstrap-touchspin.min.css">
        <link rel="stylesheet" href="/be/css/lib/jqueryui/jquery-ui.min.css">
        <link rel="stylesheet" href="/be/css/separate/vendor/tags_editor.min.css">
        <script src="/be/plugins/ckeditor/ckeditor.js"></script>        
        <script language="javascript">
            //--
            function addAnswerOption()
            {
                var ansdiv = document.getElementById('answeroptions');
                var ansNames = document.getElementById('answeroptions').getElementsByTagName('textarea');
                var length = ansNames.length - 1;
                //--
                if (length > 0) {
                    if (length < 7) {
                        var newRowSeq = ansNames.item(length).id.replace("txtOpt", "");
                        var i = parseInt(newRowSeq) + 1;
                        AddAnsOptionWithId(i);
                    } else {
                        alert("Maximum Options allowed are 8.");
                    }
                } else {
                    AddAnsOptionWithId(1);
                }
                reSequenceOptions();
            }

            //--
            function AddDefaultAnswerOptions()
            {
                //-- Only for MCQ
                if ($('#type').val() == 1) {
                    var ansdiv = document.getElementById('answeroptions');
                    for (var i = 1; i <= 4; i++) {
                        AddAnsOptionWithId(i);
                    }
                }
            }

            //--
            function AddAnsOptionWithId(seqCounter)
            {
                var ansdiv = document.getElementById('answeroptions');
                var newDiv = document.createElement('div');
                newDiv.setAttribute("class", "row");
                newDiv.setAttribute("id", "divOpt"+seqCounter);
                newDiv.innerHTML = '<div class="col-sm-1 text-center"> <span id="seq" class="badge">'+seqCounter+')</span> </div> <div class="col-sm-10"> <div class="form-group"> <textarea id="txtOpt'+seqCounter+'" name="txtOpt'+seqCounter+'" class="form-control" placeholder="Answer Option"></textarea> </div> </div> <div class="col-sm-1"> <div class="checkbox"> <input type="checkbox" id="chkOpt'+seqCounter+'" name="chkOpt'+seqCounter+'"><label for="chkOpt'+seqCounter+'"></label> </div> <span class="button" id="delOpt'+seqCounter+'" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span></div>';
                ansdiv.appendChild(newDiv);
            }

            //--
            function removeAnswerOption(clkId)
            {
                if (document.getElementById('answeroptions').children.length > 2) {
                    var divName = clkId.toString().replace("delOpt", "divOpt");
                    $("#"+divName).remove();
                    reSequenceOptions();
                } else {
                    alert("Minimum 2 options are compulsory for MCQ.");
                }
            }

            //--
            function reSequenceOptions()
            {
                var ansdivChildren = document.getElementById('answeroptions').children;
                for (var i = 0; i < ansdivChildren.length; i++) {
                    var seq = i + 1;
                    ansdivChildren[i].setAttribute("id", "divOpt"+seq);
                    ansdivChildren[i].children[0].innerHTML = '<span class="badge">'+seq+')</span>';
                    ansdivChildren[i].children[1].children[0].children[0].setAttribute("id", "txtOpt"+seq);
                    ansdivChildren[i].children[1].children[0].children[0].setAttribute("name", "txtOpt"+seq);
                    ansdivChildren[i].children[2].children[0].children[0].setAttribute("id", "chkOpt"+seq);
                    ansdivChildren[i].children[2].children[0].children[0].setAttribute("name", "chkOpt"+seq);
                    ansdivChildren[i].children[2].children[1].setAttribute("id", "delOpt"+seq);
                }
            }

            //--
            function addArgsOutput2Code()
            {
                var ctcdiv = document.getElementById('codewtc');
                var ctcElemnts = document.getElementById('codewtc').getElementsByTagName('span');
                var length = ctcElemnts.length - 1;
                //--
                if (length >= 0) {
                    if (length < 4) {
                        var newRowSeq = ctcElemnts.item(length).id.replace("delCTC", "");
                        var i = parseInt(newRowSeq) + 1;
                        addArgsOutputWithId(i);
                        //alert(i);
                    } else {
                        alert("No more Input Parameter options allowed.");
                    }
                } else {
                    addArgsOutputWithId(1);
                    //alert("1111");
                }
                //reSequenceArgsOutputOptions();
            }

            //--
            function addArgsOutputWithId(seqCounter)
            {
                var ctcdiv = document.getElementById('codewtc');
                var newDiv = document.createElement('div');
                //newDiv.setAttribute("class", "row");
                newDiv.setAttribute("id", "divCTC"+seqCounter);
                newDiv.innerHTML = '<div class="col-sm-11"><div class="form-group row"><div class="col-md-2"><label class="form-label">Input Parameters / Args '+seqCounter+'</label></div><div class="col-md-10"><textarea id="txtInputParams'+seqCounter+'" name="txtInputParams'+seqCounter+'" class="form-control" placeholder="Input Parameters"></textarea></div></div><div class="form-group row"><div class="col-md-2"><label class="form-label">Expected Output '+seqCounter+'</label></div><div class="col-md-10"><textarea id="txtExpectedOutput'+seqCounter+'" name="txtExpectedOutput'+seqCounter+'" class="form-control" placeholder="Expected Output"></textarea></div></div></div><div class="col-sm-1"><span class="button" id="delCTC'+seqCounter+'" onclick="removeArgsOutputOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span></div>';
                ctcdiv.appendChild(newDiv);
            }

            //--
            function removeArgsOutputOption(clkId)
            {
                //if (document.getElementById('codewtc').children.length > 2) {
                    var divName = clkId.toString().replace("delCTC", "divCTC");
                    //alert(divName);
                    $("#"+divName).remove();
                    reSequenceArgsOutputOptions();
                //} else {
                    //alert("Minimum 1 Input Parameter option is compulsory for Code.");
                //}
            }

            //--
            function reSequenceArgsOutputOptions()
            {
                var argsdivChildren = document.getElementById('codewtc').children;
                //--
                for (var i = 0; i < argsdivChildren.length; i++) {
                    var seq = i + 1;
                    argsdivChildren[i].setAttribute("id", "divCTC"+seq);
                    argsdivChildren[i].children[0].children[0].children[0].innerHTML = '<label class="form-label">Input Parameters / Args '+seq+'</label>';
                    argsdivChildren[i].children[0].children[0].children[1].children[0].setAttribute("id", "txtInputParams"+seq);
                    argsdivChildren[i].children[0].children[0].children[1].children[0].setAttribute("name", "txtInputParams"+seq);
                    argsdivChildren[i].children[0].children[1].children[0].innerHTML = '<label class="form-label">Expected Output '+seq+'</label>';
                    argsdivChildren[i].children[0].children[1].children[1].children[0].setAttribute("id", "txtExpectedOutput"+seq);
                    argsdivChildren[i].children[0].children[1].children[1].children[0].setAttribute("name", "txtExpectedOutput"+seq);
                    argsdivChildren[i].children[1].children[0].setAttribute("id", "delCTC"+seq);
                }
            }
        </script>
        <style>
            #sortableques { margin: 0; padding: 0; }
            #sortableques li { margin: 0 3px 3px 3px; padding: 0.1em; padding-left: 0.3em; }
        </style>
    </head>
    <body class="with-side-menu" onload="AddDefaultAnswerOptions()">
        <!-- Main Header Navbar -->
        <% include partials/header %>

        <div class="mobile-menu-left-overlay"></div>

        <!-- Left side column. contains the Left Sidebar Menu -->
        <% include partials/leftsidebar %>
    
        <!-- Main content -->
        <div class="page-content">
            <div class="container-fluid">
              
                <!-- Content Header (Page header) -->
                <header class="section-header">
                    <div class="tbl">
                        <div class="tbl-row">
                            <div class="tbl-cell">
                                <h5>Add New Question to Test</h5>
                            </div>
                            <div class="tbl-cell">
                                <div class="pull-right">
                                    <a class="btn btn-secondary-outline" href="/test/list">Test List</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </header>
            
                <!-- Your Page Content Here -->
                <!-- List CC Table -->
                <section class="card">
                    <div class="card-block">
                        <div class="form-group row">
						    <div class="col-md-4">
                                <%if(locals.quiz){%>
                                    <ul>
                                        <li>Title : <%=quiz.title%></li>
                                        <li>Desc. : <%=quiz.description%></li>
                                        <li>Is Timed : <%=quiz.timedtest%></li>
                                        <li>Total Ques. : <%if(locals.queslst){%> <%=queslst.length%>  <%}else{%> 0 <%}%></li>
                                    </ul>
                                    <hr>
                                    <div>
                                        <a href="/test/addQues/qzid:<%=quiz._id%>">Add new Question</a>
                                    </div>
                                    <hr>
                                    <strong>Questions:-</strong>
                                    <div id="dvqueslst">
                                        <ol id="sortableques" class="list">
                                            <%if(locals.queslst){%>
                                                <% queslst.forEach(function(quesObj) { %>
                                                    <li class="ui-state-highlight" id="<%=quesObj._id %>">
                                                        <a href="/test/addQues/qzid:<%=quiz._id%>/quesid:<%=quesObj._id%>"> <%-quesObj.text %> </a>
                                                    </li>
                                                <%});%>
                                            <%}%>
                                        </ol>
                                    </div>    
                                <%}%>
                            </div>
                            <div class="col-md-8">
                                <form onsubmit="return doValidations(this)" role="form" <% if(locals.ques && locals.quiz){%> action="/test/addQues/qzid:<%=locals.quiz._id%>/quesid:<%=ques._id%>"<%}else if(locals.ques){%> action="../add/id:<%=ques._id%>"<%}else{%>action="add<% if(locals.quiz){%>?qzid=<%=locals.quiz._id%><%}%>"<%}%>  method="post">
                                    <div class="row">
                                        
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Type</label>
                                                </div>
                                                <div class="col-md-8">
                                                    <select class="bootstrap-select bootstrap-select-arrow selectpicker show-tick" id="type" name="type">
                                                        <option value='1' <% if(locals.ques && locals.ques.type == 1){ %>selected<% } %> >MCQ</option>
                                                        <option value='2' <% if(locals.ques && locals.ques.type == 2){ %>selected<% } %> >Subjective</option>
                                                        <option value='3' <% if(locals.ques && locals.ques.type == 3){ %>selected<% } %> >Code</option>
                                                        <option value='4' <% if(locals.ques && locals.ques.type == 4){ %>selected<% } %> >Code-Multiple-TestCases</option>
													    <option value='5' <% if(locals.ques && locals.ques.type == 5){ %>selected<% } %> >Code-Multiple-TestCases-with-Confirmation</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Score</label>
                                                </div>
                                                <div class="col-md-8">
                                                    <input type="text" class="form-control" id="score" name="score" onkeyup="if (/[^0-9,.]/g.test(this.value)) this.value = this.value.replace(/[^0-9,.]/g,'');" placeholder="Score" <% if(locals.ques){%>value="<%=ques.score%>"<%}%> >
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Negative Score</label>
                                                </div>
                                                <div class="col-md-8">
                                                    <input type="text" id="negativescore" name="negativescore" onkeyup="if (/[^0-9,.]/g.test(this.value)) this.value = this.value.replace(/[^0-9,.]/g,'');" placeholder="Negative Score" <% if(locals.ques){%>value="<%=ques.negativescore%>"<%}%> >
                                                </div>
                                            </div>

                                        
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Difficulty Level</label>
                                                </div>
                                                <div class="col-md-8">
                                                    <input type="text" id="difficulty" name="difficulty" onkeyup="if (/[^0-9,.]/g.test(this.value)) this.value = this.value.replace(/[^0-9,.]/g,'');" placeholder="Difficulty Level" <% if(locals.ques){%>value="<%=ques.difficulty%>"<%}%> >
                                                </div>
                                            </div>

                                            <div class="form-group row" id="hasmutians">
                                                <div class="col-md-2">
                                                    <label class="form-label">Has Multiple Answers</label>
                                                </div>
                                                <div class="col-md-8">
                                                    <div class="checkbox-toggle -large">
                                                        <input type="checkbox" id="ismultianswer" name="ismultianswer" <% if(locals.ques && ques.hasmultipleanswers == 1){%>checked<%}%> >
                                                        <label for="ismultianswer">Multiple Answers</label>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Tags</label>
                                                </div>
                                                <div class="col-md-9">
                                                    <input type="text" class="form-control" id="txttag" name="txttag" placeholder="Tag" <% if(locals.ques){%>value="<%=ques.tags%>"<%}%> >
                                                </div>
                                            </div>

                                    </div>

                                    <div class="col-md-12 row">
                                        <div class="form-group row">
                                            <label class="form-label" for="txtQues">Question: </label>
                                            <div class="form-control"><textarea class="form-control" id="txtQues" name="txtQues"><% if(locals.ques && ques.text){%><%=ques.text%><%}%></textarea></div>
                                        </div>
                                    </div>

                                    <div id='mcq' <% if(!locals.ques){%>style="display:block"<%}else if(locals.ques && locals.ques.type == 1){%>style="display:block"<%}else{%>style="display:none"<%}%> >
                                        <% if(locals.ques && ques.type == 1){ %>
                                            <% if(ques.option1 && ques.option1.length > 0) 
                                            { %>
                                                <div class="row" id="divOpt1">
                                                    <div class="col-sm-1 text-center"> 
                                                        <span id="seq" class="badge">1)</span> 
                                                    </div> 
                                                    <div class="col-sm-10"> 
                                                        <div class="form-group"> 
                                                            <textarea id="txtOpt1" name="txtOpt1" class="form-control" placeholder="Answer Option 1"><% if(locals.ques && ques.option1){%> <%=ques.option1%> <%}%> </textarea> 
                                                        </div> 
                                                    </div> 
                                                    <div class="col-sm-1"> 
                                                        <div class="checkbox"> 
                                                            <input type="checkbox" id="chkOpt1" name="chkOpt1" <% if(locals.ques && ques.isoption1correct){%> checked <%}%> > 
                                                            <label for="chkOpt1"></label>
                                                        </div> 
                                                        <span class="button" id="delOpt1" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span>  
                                                    </div>
                                                </div>
                                            <%}
                                            if(ques.option2 && ques.option2.length > 0)
                                            {%>
                                                <div class="row" id="divOpt2">
                                                    <div class="col-sm-1 text-center"> 
                                                        <span id="seq" class="badge">2)</span> 
                                                    </div> 
                                                    <div class="col-sm-10"> 
                                                        <div class="form-group"> 
                                                            <textarea id="txtOpt2" name="txtOpt2" class="form-control" placeholder="Answer Option 2"><% if(locals.ques && ques.option2){%> <%=ques.option2%> <%}%> </textarea> 
                                                        </div> 
                                                    </div> 
                                                    <div class="col-sm-1"> 
                                                        <div class="checkbox"> 
                                                            <input type="checkbox" id="chkOpt2" name="chkOpt2" <% if(locals.ques && ques.isoption2correct){%> checked <%}%> >
                                                            <label for="chkOpt2"></label> 
                                                        </div> 
                                                        <span class="button" id="delOpt2" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span>  
                                                    </div>
                                                </div>
                                            <%}
                                            if(ques.option3 && ques.option3.length > 0)
                                            { %>
                                                <div class="row" id="divOpt3">
                                                    <div class="col-sm-1 text-center"> 
                                                        <span id="seq" class="badge">3)</span> 
                                                    </div> 
                                                    <div class="col-sm-10"> 
                                                        <div class="form-group"> 
                                                            <textarea id="txtOpt3" name="txtOpt3" class="form-control" ><% if(locals.ques && ques.option3){%> <%=ques.option3%> <%}%> </textarea> 
                                                        </div> 
                                                    </div> 
                                                    <div class="col-sm-1"> 
                                                        <div class="checkbox"> 
                                                            <input type="checkbox" id="chkOpt3" name="chkOpt3" <% if(locals.ques && ques.isoption3correct){%> checked <%}%> > 
                                                            <label for="chkOpt3"></label>
                                                        </div> 
                                                        <span class="button" id="delOpt3" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span>  
                                                    </div>
                                                </div>	
                                            <%}
                                            if(ques.option4 && ques.option4.length > 0)
                                            { %>
                                                <div class="row" id="divOpt4">
                                                    <div class="col-sm-1 text-center"> 
                                                        <span id="seq" class="badge">4)</span> 
                                                    </div> 
                                                    <div class="col-sm-10"> 
                                                        <div class="form-group"> 
                                                            <textarea id="txtOpt4" name="txtOpt4" class="form-control" placeholder="Answer Option 4"><% if(locals.ques && ques.option4){%> <%=ques.option4%> <%}%> </textarea> 
                                                        </div> 
                                                    </div> 
                                                    <div class="col-sm-1"> 
                                                        <div class="checkbox"> 
                                                            <input type="checkbox" id="chkOpt4" name="chkOpt4" <% if(locals.ques && ques.isoption4correct){%> checked <%}%> > 
                                                            <label for="chkOpt4"></label>
                                                        </div> 
                                                        <span class="button" id="delOpt4" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span>  
                                                    </div>
                                                </div>	
                                            <%}
                                            if(ques.option5 && ques.option5.length > 0)
                                            { %>
                                                <div class="row" id="divOpt5">
                                                    <div class="col-sm-1 text-center"> 
                                                        <span id="seq" class="badge">5)</span> 
                                                    </div> 
                                                    <div class="col-sm-10"> 
                                                        <div class="form-group"> 
                                                            <textarea id="txtOpt5" name="txtOpt5" class="form-control" placeholder="Answer Option 5"><% if(locals.ques && ques.option5){%> <%=ques.option5%> <%}%> </textarea> 
                                                        </div> 
                                                    </div> 
                                                    <div class="col-sm-1"> 
                                                        <div class="checkbox"> 
                                                            <input type="checkbox" id="chkOpt5" name="chkOpt5" <% if(locals.ques && ques.isoption5correct){%> checked <%}%> > 
                                                            <label for="chkOpt5"></label>
                                                        </div> 
                                                        <span class="button" id="delOpt5" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span>  
                                                    </div>
                                                </div>
                                            <% }
                                            if(ques.option6 && ques.option6.length > 0)
                                            { %>
                                                <div class="row" id="divOpt6">
                                                    <div class="col-sm-1 text-center"> 
                                                        <span id="seq" class="badge">6)</span> 
                                                    </div> 
                                                    <div class="col-sm-10"> 
                                                        <div class="form-group"> 
                                                            <textarea id="txtOpt6" name="txtOpt6" class="form-control" placeholder="Answer Option 6"><% if(locals.ques && ques.option6){%> <%=ques.option6%> <%}%> </textarea> 
                                                        </div> 
                                                    </div> 
                                                    <div class="col-sm-1"> 
                                                        <div class="checkbox"> 
                                                            <input type="checkbox" id="chkOpt6" name="chkOpt6" <% if(locals.ques && ques.isoption6correct){%> checked <%}%> > 
                                                            <label for="chkOpt6"></label>
                                                        </div> 
                                                        <span class="button" id="delOpt6" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span>  
                                                    </div>
                                                </div>
                                            <% }
                                            if(ques.option7 && ques.option7.length > 0)
                                            { %>
                                                <div class="row" id="divOpt7">
                                                    <div class="col-sm-1 text-center"> 
                                                        <span id="seq" class="badge">7)</span> 
                                                    </div> 
                                                    <div class="col-sm-10"> 
                                                        <div class="form-group"> 
                                                            <textarea id="txtOpt7" name="txtOpt7" class="form-control" placeholder="Answer Option 7"><% if(locals.ques && ques.option7){%> <%=ques.option7%> <%}%> </textarea> 
                                                        </div> 
                                                    </div> 
                                                    <div class="col-sm-1"> 
                                                        <div class="checkbox"> 
                                                            <input type="checkbox" id="chkOpt7" name="chkOpt7" <% if(locals.ques && ques.isoption7correct){%> checked <%}%> > 
                                                            <label for="chkOpt7"></label>
                                                        </div> 
                                                        <span class="button" id="delOpt7" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span>  
                                                    </div>
                                                </div>
                                            <% }
                                            if(ques.option8 && ques.option8.length > 0)
                                            {%>
                                                <div class="row" id="divOpt8">
                                                    <div class="col-sm-1 text-center"> 
                                                        <span id="seq" class="badge">8)</span> 
                                                    </div> 
                                                    <div class="col-sm-10"> 
                                                        <div class="form-group"> 
                                                            <textarea id="txtOpt8" name="txtOpt8" class="form-control" placeholder="Answer Option 8"><% if(locals.ques && ques.option8){%> <%=ques.option8%> <%}%> </textarea> 
                                                        </div> 
                                                    </div> 
                                                    <div class="col-sm-1"> 
                                                        <div class="checkbox"> 
                                                            <input type="checkbox" id="chkOpt8" name="chkOpt8" <% if(locals.ques && ques.isoption8correct){%> checked <%}%> > 
                                                            <label for="chkOpt8"></label>
                                                        </div> 
                                                        <span class="button" id="delOpt8" onclick="removeAnswerOption(this.id)"><i class="glyphicon glyphicon-trash"></i></span>  
                                                    </div>
                                                </div>

                                            <%}%>

                                        <% }else{ %>
                                            <div id="answeroptions">
                                            </div>
                                        <% } %>
                            
                                        <button type="button" class="btn btn-primary-outline btn-sm" onclick="addAnswerOption();">Add Another Answer Option</button>
                                    </div>
                                    <br>
                                    <div id="subjective" <% if(!locals.ques){%>style="display:none"<%}else if(locals.ques && locals.ques.type == 2){%>style="display:block"<%}else{%>style="display:none"<%}%> class="form-group">
                                        <div class="col-md-12 row"><textarea id="txtSub" name="txtSub" class="form-control" placeholder="Answer"></textarea></div>
                                    </div>
                                    <div id="code" <% if(!locals.ques){%>style="display:none"<%}else if(locals.ques && (locals.ques.type == 3 || locals.ques.type == 4)){%>style="display:block"<%}else{%>style="display:none"<%}%> class="form-group" >
                                        <div class="col-md-12 row">
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Programming Language</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <select class="bootstrap-select bootstrap-select-arrow selectpicker show-tick" id="progLang" name="progLang">
                                                        <option value='0' <% if(locals.ques && locals.ques.codeproglang == 0){ %>selected<% } %> > Python </option>
                                                        <option value='1' <% if(locals.ques && locals.ques.codeproglang == 1){ %>selected<% } %> > Ruby </option>
                                                        <option value='2' <% if(locals.ques && locals.ques.codeproglang == 2){ %>selected<% } %> > Clojure </option>
                                                        <option value='3' <% if(locals.ques && locals.ques.codeproglang == 3){ %>selected<% } %> > PHP </option>
                                                        <option value='4' <% if(locals.ques && locals.ques.codeproglang == 4){ %>selected<% } %> > JavaScript </option>
                                                        <option value='5' <% if(locals.ques && locals.ques.codeproglang == 5){ %>selected<% } %> > Scala </option>
                                                        <option value='6' <% if(locals.ques && locals.ques.codeproglang == 6){ %>selected<% } %> > Go </option>
                                                        <option value='7' <% if(locals.ques && locals.ques.codeproglang == 7){ %>selected<% } %> > C/C++ </option>
                                                        <option value='8' <% if(locals.ques && locals.ques.codeproglang == 8){ %>selected<% } %> > Java </option>
                                                        <option value='9' <% if(locals.ques && locals.ques.codeproglang == 9){ %>selected<% } %> > VB.NET </option>
                                                        <option value='10' <% if(locals.ques && locals.ques.codeproglang == 10){ %>selected<% } %> > C# </option>
                                                        <option value='11' <% if(locals.ques && locals.ques.codeproglang == 11){ %>selected<% } %> > Bash </option>
                                                        <option value='12' <% if(locals.ques && locals.ques.codeproglang == 12){ %>selected<% } %> > Objective-C </option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Program Code</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtCode" name="txtCode" class="form-control" placeholder="Code"><% if(locals.ques && ques.codeprogcode){%> <%=ques.codeprogcode%> <%}%></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-md-2">
                                                <label class="form-label">Input Parameters / Args</label>
                                            </div>
                                            <div class="col-md-10">
                                                <textarea id="txtInputParams" name="txtInputParams" class="form-control" placeholder="Input Parameters"><% if(locals.ques && ques.codeproginputparams){%> <%=ques.codeproginputparams%> <%}%></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-md-2">
                                                <label class="form-label">Expected Output</label>
                                            </div>
                                            <div class="col-md-10">
                                                <textarea id="txtExpectedOutput" name="txtExpectedOutput" class="form-control" placeholder="Expected Output"><% if(locals.ques && ques.codeprogexpectedoutput){%> <%=ques.codeprogexpectedoutput%> <%}%></textarea>
                                            </div>
                                        </div>

                                        <div id="codewtc" <% if(!locals.ques){%>style="display:none"<%}else if(locals.ques && locals.ques.type == 4){%>style="display:block"<%}else{%>style="display:none"<%}%> class="form-group" >
                                            
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Input Parameters / Args 1</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtInputParams1" name="txtInputParams1" class="form-control" placeholder="Input Parameters 1"><% if(locals.ques && ques.codeproginputparams1){%> <%=ques.codeproginputparams1%> <%}%></textarea>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Expected Output1</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtExpectedOutput1" name="txtExpectedOutput1" class="form-control" placeholder="Expected Output1"><% if(locals.ques && ques.codeprogexpectedoutput1){%> <%=ques.codeprogexpectedoutput1%> <%}%></textarea>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Input Parameters / Args 2</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtInputParams2" name="txtInputParams2" class="form-control" placeholder="Input Parameters 2"><% if(locals.ques && ques.codeproginputparams2){%> <%=ques.codeproginputparams2%> <%}%></textarea>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Expected Output2</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtExpectedOutput2" name="txtExpectedOutput2" class="form-control" placeholder="Expected Output2"><% if(locals.ques && ques.codeprogexpectedoutput2){%> <%=ques.codeprogexpectedoutput2%> <%}%></textarea>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Input Parameters / Args 3</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtInputParams3" name="txtInputParams3" class="form-control" placeholder="Input Parameters 3"><% if(locals.ques && ques.codeproginputparams3){%> <%=ques.codeproginputparams3%> <%}%></textarea>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Expected Output3</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtExpectedOutput3" name="txtExpectedOutput3" class="form-control" placeholder="Expected Output3"><% if(locals.ques && ques.codeprogexpectedoutput3){%> <%=ques.codeprogexpectedoutput3%> <%}%></textarea>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Input Parameters / Args 4</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtInputParams4" name="txtInputParams4" class="form-control" placeholder="Input Parameters 4"><% if(locals.ques && ques.codeproginputparams4){%> <%=ques.codeproginputparams4%> <%}%></textarea>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Expected Output4</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtExpectedOutput4" name="txtExpectedOutput4" class="form-control" placeholder="Expected Output4"><% if(locals.ques && ques.codeprogexpectedoutput4){%> <%=ques.codeprogexpectedoutput4%> <%}%></textarea>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Input Parameters / Args 5</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtInputParams5" name="txtInputParams5" class="form-control" placeholder="Input Parameters 5"><% if(locals.ques && ques.codeproginputparams5){%> <%=ques.codeproginputparams5%> <%}%></textarea>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-2">
                                                    <label class="form-label">Expected Output5</label>
                                                </div>
                                                <div class="col-md-10">
                                                    <textarea id="txtExpectedOutput5" name="txtExpectedOutput5" class="form-control" placeholder="Expected Output5"><% if(locals.ques && ques.codeprogexpectedoutput5){%> <%=ques.codeprogexpectedoutput5%> <%}%></textarea>
                                                </div>
                                            </div>

                                        </div>                                        
                                    </div>
                                    
                                    <div class="col-md-12 row">
                                        <div class="form-group">
                                            <label class="form-label">Answer Explanation:</label>
                                            <textarea id="txtExpl" name="txtExpl" class="form-control" placeholder="Explanation for correct Answer."><% if(locals.ques && ques.explanation){%> <%=ques.explanation%> <%}%></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Answer Hint:</label>
                                            <textarea id="txtHint" name="txtHint" class="form-control" placeholder="Hint For Student."><% if(locals.ques && ques.hint){%> <%=ques.hint%> <%}%></textarea>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <div class="col-md-2">
                                            <label class="form-label">Show Hint for this Question</label>
                                        </div>
                                        <div class="col-md-10">
                                            <div class="checkbox-toggle -large">
                                                <input type="checkbox" id="isShowhint" name="isShowhint" <% if(locals.ques && ques.showhint == 1){%>checked<%}%> >
                                                <label for="isShowhint">Show Hint with Question</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="box-footer">
                                        <button type="submit" class="btn btn-primary">Save Question</button>
                                        <button type="button" class="btn btn-danger" onclick="parent.location='/test/list'">Cancel</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        
                    </div><!--.card-block-->
                </section><!--.card-->

            </div><!--.container-fluid-->
        </div><!--.page-content-->

        <!-- REQUIRED JS SCRIPTS -->
        <% include partials/scripts %>
        <script src="/be/js/lib/bootstrap-select/bootstrap-select.min.js"></script>
        <script src="/be/js/lib/jqueryui/jquery-ui.min.js"></script>
        <script src="/be/js/lib/bootstrap-touchspin/jquery.bootstrap-touchspin.min.js"></script>
        <script src="/be/js/lib/notie/notie.js"></script>
        <script src="/be/js/lib/jquery-tag-editor/jquery.caret.min.js"></script>
        <script src="/be/js/lib/jquery-tag-editor/jquery.tag-editor.min.js"></script>
        <script>
            //--
			$("#type").val(1);
			//--
            $(document).ready(function() {
                //--
                $("input[name='negativescore']").TouchSpin({
                    min: 0,
                    max: 5,
                    step: 0.05,
                    decimals: 2
                });
                //--
                $("input[name='difficulty']").TouchSpin({
                    min : 0,
                    max : 10
                });
                //--
                $('#type').on('change', function() {
                    if (this.value == 1) {
                        //alert("MCQ");
                        $('#hasmutians').show();
                        $('#subjective').hide();
                        $('#codewtc').hide();
                        $('#code').hide();
                        $('#mcq').show();
                    }
                    if (this.value == 2) {
                        //alert("Subjective");
                        $('#hasmutians').hide();
                        $('#mcq').hide();
                        $('#codewtc').hide();
                        $('#code').hide();
                        $('#subjective').show();
                    }
                    if (this.value == 3) {
                        //alert("Code");
                        $('#hasmutians').hide();
                        $('#mcq').hide();
                        $('#subjective').hide();
                        $('#codewtc').hide();
                        $('#code').show();
                    }
                    if (this.value == 4) {
						//alert("Code");
						$('#hasmutians').hide();
						$('#mcq').hide();
						$('#subjective').hide();
						$('#code').show();
						$('#codewtc').show();
					}
                });
                //--
                $("#sortableques").sortable();
                //--
                $('#txttag').tagEditor({
					delimiter: ',; ', /* space comma semi-colon */
					forceLowercase: false,
					placeholder: 'Enter tags ...',
                    beforeTagSave : function(field, editor, tags, tag, value){
						if(jQuery.inArray(value,tags)!=-1){
							toggleAlertMsgModal("Tag already exists",2);
                            return false;
						}
					},
					onChange: function(field, editor, tags) {
                        $(".tag-editor-tag").click(function(){return false});
                    },
				});
                //--
                // $('.ui-state-highlight').on('click', function(e) {
                //    // alert(this.id);
                // });
            });
            //--
            function doValidations(quesfrm) {
                //-- Check for empty ques
                //alert(editor0.getData().length + " " + quesfrm.txtQues.value + ' ' + $('#type').val());
                //return false;
                if (editor0.getData().length > 0) {
                    //-- Check for mcq answer
                    if ($('#type').val() == 1) {
                        var isChkd = false;
                        var isAns = false;
                        //var ctr = 1;
                        $('#mcq div.col-sm-10 textarea').each(function () {
                            //var edtr = editor + ctr.toString();
                            if (this.value.length > 0) {
                                isAns = true;
                            } else {
                                isAns = false;
                            }
                            //alert("hi" + this.value.length);
                            //ctr++;
                        });
                        var chkctr = 0;
                        $('#mcq div.checkbox input[type=checkbox]').each(function () {
                            if (this.checked == "1") {
                                isChkd = true;
                                chkctr++;
                            }
                        });
                        //alert($('#ismultianswer').prop('checked') + ' ' + chkctr);
                        //-- Chk for multiple choice
                        if ($('#ismultianswer').prop('checked') == true) {
                            //alert(chkctr);
                            if (chkctr <= 1) {
                                notie.alert(2, 'Sorry, two or more correct answers need to be submitted. Try again.', 5);
                                return false;
                            }
                        } else {
                            //alert(chkctr);
                            if (chkctr != 1) {
                                notie.alert(2, 'Sorry, only one correct answer is to be submitted. Try again.', 5);
                                return false;
                            }
                        }
                        if (isAns && isChkd) {
                            return true;
                        } else {
                            if (!isAns) {
                                notie.alert(2, 'Sorry, empty answers cannot be submitted. Try again.', 5);
                                return false;
                            }
                            if (!isChkd) {
                                notie.alert(2, 'Sorry, please mark the correct answer checkbox. Try again.', 5);
                                return false;
                            } 
                        }
                    } else {
                        return true;
                    }
                } else {
                    notie.alert(2, 'Sorry, empty question cannot be submitted. Try again.', 5);
                    return false;
                }
            }
        </script>
        <script language="javascript">
            var editor0 = CKEDITOR.inline('txtQues');
            var editor1 = CKEDITOR.inline('txtOpt1');					
            var editor2 = CKEDITOR.inline('txtOpt2');
            var editor3 = CKEDITOR.inline('txtOpt3');
            var editor4 = CKEDITOR.inline('txtOpt4');
            var editor5 = CKEDITOR.inline('txtOpt5');
            var editor6 = CKEDITOR.inline('txtOpt6');
            var editor7 = CKEDITOR.inline('txtOpt7');
            var editor8 = CKEDITOR.inline('txtOpt8');
        </script>
        <!-- Show Notification messages -->
        <% if (messages.error) { %>
            <input id="flshMsgE" type="text" hidden value="<%=messages.error%>">
            <script>
                notie.alert(3, $('#flshMsgE').val(), 5);
            </script>
        <% } %>
        <% if (messages.info) { %>
            <input id="flshMsgI" type="text" hidden value="<%=messages.info%>">
            <script>
                notie.alert(2, $('#flshMsgI').val(), 5);
            </script>
        <% } %>
        <% if (messages.success) { %>
            <input id="flshMsgS" type="text" hidden value="<%=messages.success%>">
            <script>
                notie.alert(1, $('#flshMsgS').val(), 5);
            </script>
        <% } %>
    </body>
</html>