@font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-Black.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-BlackOblique.ttf') format('truetype');
    font-weight: 900;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-Heavy.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-HeavyOblique.ttf') format('truetype');
    font-weight: 700;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-MediumOblique.ttf') format('truetype');
    font-weight: 500;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'Avenir Book';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-Book.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'Avenir Book';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-BookOblique.ttf') format('truetype');
    font-weight: 400;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-Roman.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-Oblique.ttf') format('truetype');
    font-weight: 400;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
  }
  
  @font-face {
    font-family: 'Avenir';
    src: url('/websiteNew/assets/fonts/Avenir/Avenir-LightOblique.ttf') format('truetype');
    font-weight: 300;
    font-style: italic;
  }
  
  @font-face {
    font-family: 'chauncy';
    src: url('/websiteNew/assets/fonts/chauncy/Chauncy_Fatty.ttf') format('truetype');
    font-style: normal;
    font-weight: 500;
  }
  @font-face {
    font-family: 'chauncy';
    src: url('/websiteNew/assets/fonts/chauncy/Chauncy_Snowman.ttf') format('truetype');
    font-style: normal;
    font-weight: 400;
  }
  body{
    padding: 0px;
    margin: 0px;
  }
  /* login page css */
  .login_form_container{
    background-color: #DE6834;
    min-height: 100vh;
    padding-top: 80px;
  }
  .banner__text__one{
    text-align: center;
  }
  .banner__logo__text__part__one {
    font-family: 'Changa', sans-serif;
    font-weight: 600;
    font-size: 45px;
    color: #FFFFFF;
    letter-spacing: -1.9px;
  }
  
  .banner__logo__text__part__two {
    color: #FFFFFF;
    position: relative;
    left: -4px;
    font-family: 'Changa One', cursive;
    font-size: 45px;
    /*xx-large*/
  }
  .role_form_container{
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  .container-role-div{
    display:flex;
    }
    .container-role {
      display: block;
      position: relative;
      padding-left: 30px;
      margin-right: 15px;
      cursor: pointer;
      color:white;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .role-name{
      color: #FFFFFF;
      font-family: 'Avenir';
      font-weight: 500;
      font-size: 15px;
     position: relative;
     top:-1.5px;
    }
    
    .container-role input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
    }
    
    .checkmark {
      position: absolute;
      top: 0;
      left: 0;
      height: 20px;
      width: 20px;
      background-color: white;
      border-radius: 50%;
    }
    
    .container-role:hover input ~ .checkmark {
      background-color: #ccc;
    }
    
    .container-role input:checked ~ .checkmark {
      background-color: white;
    }
    .container-role input:checked ~ .checkmark:after{
    background-color: #DE6834;
    }
    
    .checkmark:after {
      content: "";
      position: absolute;
      display: none;
    }
    
    
    .container-role input:checked ~ .checkmark:after {
      display: block;
    }
    
    .container-role .checkmark:after {
       top: 4px;
      left: 4.2px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: white;
      box-sizing: border-box;
    }

.login_form_body{
  margin-top: 30px;
}
.login_form_body .heading{
  font-family: 'Avenir Book' !important;
  color: #FFFFFF;
  font-size: 27px;
}
.login_fields input{
  border: none !important;
    border-bottom: 1px solid #dedede !important;
    background: #de6834 !important;
    width: 100%;
    color: #FFFFFF !important;
  font-weight: 400;
  font-style: italic;
  font-size: 15px;
  font-family: 'Avenir', sans-serif !important;
    outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding-left: 20px !important;
}
.login_fields input::placeholder {
  color: #FFFFFF !important;
  font-weight: 400;
  font-style: italic;
  font-size: 15px;
  font-family: 'Avenir', sans-serif !important;
}
.CodeQ_login_form{
  width: 62%;
}
.btn__custom {
  font-size: 15px;
  font-family: 'Avenir';
  color: #DE6834;
  font-weight: 700;
  background-color: white;
  border-color: white;
  padding: .5rem 2.5rem;
  box-shadow: none !important;
  overflow: none !important;
  border: none !important;
}

.btn__custom:hover {
  color: #DE6834;
  background-color: white;
  border-color: none;
}
.sign-up-div>p{
  font-size: 15px;
  font-family: 'Avenir' !important;
  font-weight: 500 !important;
  margin-top: 20px;
  color: #FFFFFF;
}
.sign-up-div>p>a{
  color: #FFFFFF;
  text-decoration: none;
  font-size: 15px;
  font-weight: 700 !important;
  font-family: 'Avenir' !important;
}
.login_form_footer{
  margin-top: 70px;
  padding-bottom: 30px;
}
.login_form_footer>a{
  color: #FFFFFF;
  text-decoration: none;
  font-size: 15px;
  font-weight: 700 !important;
  font-family: 'Avenir' !important;
}

/* our_blog_container css start */
.our_blog_container{
  padding-top: 80px;
  padding-left: 35px;
  padding-right: 60px;
}
.blog_heading{
  font-family: 'Avenir Book' !important;
  color: #333333;
  font-size: 22px;
  margin-top: 12px;
  margin-left: 18px;
}
.recent_view{
  width: 430px;
 margin-top: -15px;
}
.recent_view .item img{
  width: 260px;
  height: 130px;
  border-radius: 4px;
}
.recent_view .item .reading_time{
  position: absolute;
  z-index: 10;
  margin-top: -25px;
  margin-left: 10px;
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 500 !important;
  font-family: 'Avenir Book' !important;
}
.recent_view .item .description{
   color: #6E6E6E !important;
   font-size: 29px !important;
   font-family: 'Avenir Book' !important;
   line-height: 27px;
   
}
.recent_view .item .author{
  color: #818181;
  font-size: 15px;
  font-family: 'Avenir Book' !important;
}
.blog_section{
  display: flex;
  flex-wrap: wrap;
}
.blog_section .item{
  width: 245px;
  margin-right: 15px;
}
.blog_section .item img{
  border-radius: 4px;
  width:200px;
  height: 100px;
}
.card-footer .description{
  font-family: 'Avenir Book' !important;
  color: #333333 !important;
  font-size: 15px;
  font-weight: 500 !important;
  padding-bottom: 0px;
  margin-bottom: 2px;
}
.card-footer .author{
  color: #818181;
  font-size: 15px;
  font-weight: 500 !important;
  font-family: 'Avenir Book' !important;

}

.login_by_others{
  display: flex;
  justify-content: center;
}

/* .google-icon,.github-icon,.facebook-icon{
  width: 40px;
  height: 40px;
  background-color: white;
  color: #DE6834;
  padding: 10px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0px 10px;
  text-decoration: none !important;
}
.google-icon:hover{
  color: #C20806;
}
.github-icon:hover{
  color: black;
} */

/* .google-icon,.github-icon,.facebook-icon{
  width: 40px;
  height: 40px;
  padding: 10px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0px 10px;
  text-decoration: none !important;
  background-color: #DE6834;
  color: white;
  border: 1px solid white;
}

.google-icon:hover,.github-icon:hover,.facebook-icon:hover{
  background-color: white;
  color: #DE6834;
  border: none;
  transition: .5s;
}
 
.facebook-icon:hover{
  color: blue;
}
.google-icon:hover{
  color: red;
}
.github-icon:hover{
  color: black;
}  */



.google-icon,.github-icon,.facebook-icon{
  width: 40px;
  height: 40px;
  padding: 10px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0px 10px;
  text-decoration: none !important;
  background-color: white !important;
}

.github-icon img{
  width: 100%;
}
.facebook-icon img{
  width: 100%;
}
.google-icon img{
  width: 100%;
}
