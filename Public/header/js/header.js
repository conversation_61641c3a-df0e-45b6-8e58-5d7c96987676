$(document).ready(() => {
	$('#div-user-img-container').click(function () {
		$('.notification-dropdown-container').toggleClass(
			'notification-dropdown-container-visible'
		);
	});
	$('#show-square-dropdown').click(function () {
		if (
			$('#square-dropdown-container').hasClass(
				'square-dropdown-container-visible'
			)
		) {
			$('#square-dropdown-container').removeClass(
				'square-dropdown-container-visible'
			);
			$('#square-icon-container').css({ background: 'none' });
		} else {
			$('#square-dropdown-container').addClass(
				'square-dropdown-container-visible'
			);
			$('#square-icon-container').css({ background: '#F4F4F4' });
		}
	});
});

const closeHeaders = (e) => {
	let container = $('#square-dropdown-container');
	let donBTn = $('#square-icon-container');
	if (
		!container.is(e.target) &&
		container.has(e.target).length === 0 &&
		!donBTn.is(e.target) &&
		donBTn.has(e.target).length === 0
	) {
		$('#square-dropdown-container').removeClass(
			'square-dropdown-container-visible'
		);
		$('#square-icon-container').css({ background: 'none' });
	}
	let container1 = $('#rect-dropdown-container');
	let donBTn1 = $('#btn-question-header');
	if (
		!container1.is(e.target) &&
		container1.has(e.target).length === 0 &&
		!donBTn1.is(e.target) &&
		donBTn1.has(e.target).length === 0
	) {
		$('#rect-dropdown-container').removeClass(
			'square-dropdown-container-visible'
		);
		// $('#square-icon-container').css({ background: 'none' });
	}

	let container2 = $('.notification-dropdown-container');
	let btnn = $('#div-user-img-container');
	if (
		!container2.is(e.target) &&
		container2.has(e.target).length === 0 &&
		!btnn.is(e.target) &&
		btnn.has(e.target).length === 0
	) {
		$('.notification-dropdown-container').removeClass(
			'notification-dropdown-container-visible'
		);
	}
}


$(window).blur(closeHeaders);
$(document).mouseup(closeHeaders);

$(document).ready(function () {
	$('#btn-question-header').click(function () {
		console.log($('#rect-dropdown-container'));
		if (
			$('#rect-dropdown-container').hasClass(
				'square-dropdown-container-visible'
			)
		) {
			$('#rect-dropdown-container').removeClass(
				'square-dropdown-container-visible'
			);
			$('#rect-icon-container').css({ background: 'none' });
		} else {
			$('#rect-dropdown-container').addClass(
				'square-dropdown-container-visible'
			);
			$('#rect-icon-container').css({ background: '#F4F4F4' });
		}
	});

  })
