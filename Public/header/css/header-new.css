@import url("/common/colors.css");

/* import fonts */
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Black.ttf')
		format('truetype');
	font-weight: 900;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Heavy.ttf')
		format('truetype');
	font-weight: 700;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Medium.ttf')
		format('truetype');
	font-weight: 500;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir Book';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Book.ttf') format('truetype');
	font-weight: 500;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir Book';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-BookOblique.ttf')
		format('truetype');
	font-weight: 400;
	font-style: italic;
}

body {
	background-color: var(--primary-bg);
	color: var(--primary-text);
}
input {
	background-color: var(--primary-bg);
	color: var(--primary-text);
}

ul,
li {
	padding: 0;
	margin: 0;
}

button,
input {
	box-shadow: none;
}

.fa:hover,
img:hover,
li:hover {
	margin: 0;
	padding: 0;
}

input[type='search']::-webkit-search-decoration,
input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-results-button,
input[type='search']::-webkit-search-results-decoration {
	display: none;
}

/* width */
::-webkit-scrollbar {
	width: 5.3px;
	height: 5px;
}

/* Scrollbar for Firefox */
* {
	scrollbar-color: #e5e5e5 transparent;
	scrollbar-width: thin;
}
*:hover {
	scrollbar-color: #ccc transparent;
}

.ql-syntax {
	scrollbar-color: auto;
	scrollbar-width: auto;
}

@media only screen and (max-width: 992px) {
	::-webkit-scrollbar {
		width: 5.3px;
		height: 5px;
	}
}

/* Track */
::-webkit-scrollbar-track {
	/* border: 1px solid #c9c9c9; */
	border-radius: 20px;
}

/* Handle */
::-webkit-scrollbar-thumb {
	background: #e5e5e5;
	border-radius: 8px;
}
.ql-syntax::-webkit-scrollbar-thumb {
	background-color: #ccc;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
	background: #8e8e8e;
}

.xterm-viewport {
	overflow: hidden !important;
	word-wrap: break-word;
}

.Hind_medium_one {
	font-family: 'Hind', sans-serif;
	font-size: 25px/18px;
	color: #595959;
	font-weight: 500;
}

.Hind_medium_two {
	font-family: 'Hind', sans-serif;
	font-size: 39px/28px;
	color: #ffffff;
	font-weight: 500;
}

.Hind_medium_three {
	font-family: 'Hind', sans-serif;
	font-size: 28px/17px;
	color: #c95928;
	font-weight: 500;
}

.Hind_regular_one {
	font-family: 'Hind', sans-serif;
	font-size: 28px/17px;
	color: #3b3b3b;
}

.Hind_regular_two {
	font-family: 'Hind', sans-serif;
	font-size: 28px/17px;
	color: var(--primary-color);
}

.Hind_regular_three {
	font-family: 'Hind', sans-serif;
	font-size: 26px/18px;
	color: #8d8d8d;
}

.Hind_regular_four {
	font-family: 'Hind', sans-serif;
	font-size: 35px;
	color: #ffffff;
}

.Hind_regular_five {
	font-family: 'Hind', sans-serif;
	font-size: 14px;
	color: #a3a3a3;
}

.Hind_bold_one {
	font-family: 'Hind', sans-serif !important;
	/* font: 'SemiBold' 25px/25px 'Hind'; */
	color: var(--primary-color);
	font-weight: 700;
}

.Hind_bold_two {
	color: #595959;
	font: Bold 20px/18px Hind;
}

.Hind_bold_three {
	font-family: 'Hind', sans-serif;
	font-size: 25px/18px;
	color: var(--primary-color);
	font-weight: 700;
}

.Hind_bold_four {
	font-family: 'Hind', sans-serif;
	font-size: 14px;
	color: #8d8d8d;
	font-weight: 700;
	letter-spacing: 0.01rem;
}

.Hind_semibold_one {
	font-family: 'Hind', sans-serif;
	font-size: 25px/18px;
	color: #595959;
	font-weight: 600;
}

.Hind_semibold_two {
	font-family: 'Hind', sans-serif;
	font-size: 25px;
	color: #595959;
	font-weight: 600;
}

.Hind_semibold_three {
	font-family: 'Hind', sans-serif;
	font-size: 25px/18px;
	color: var(--primary-color);
	font-weight: 600;
}

.Montserrat_bold_one {
	font-family: 'Montserrat', sans-serif;
	font-size: 16px;
	color: var(--primary-color);
	font-weight: 800;
}

.Montserrat_bold_two {
	font-family: 'Montserrat', sans-serif;
	font-size: 16px;
	color: #ffffff;
	font-weight: 800;
}

.Montserrat_regular {
	font-family: 'Montserrat', sans-serif;
	font-size: 28px/17px;
	color: #000000;
}

.Changa {
	font-family: 'Changa', sans-serif;
	font-size: 30px;
	color: white;
	font-weight: 600;
}

.Changa_one {
	font-family: 'Changa One', cursive;
	font-size: 30px;
	color: white;
	font-weight: 400;
}
.logo {
	background: var(--primary-color);
	padding: 1px 5px;
	border-radius: 4px;
	margin-left: 15px;
}
.modal.fade {
	opacity: 1;
}

/* end of all font-family and font-style and font-size and color */

/* project screen */
#projectCreateNewDropdown {
	cursor: default;
	width: 280px;
	font-family: 'Hind', sans-serif;
}

.active-project-header {
	font-size: 18px;
	color: var(--primary-color);
	font-weight: 600;
}

#projectCreateNewDropdown .form-group label {
	color: #808080;
	font-size: 14px;
}

#projectCreateNewDropdown .form-group input,
#projectCreateNewDropdown .form-group select {
	border: none;
	border-bottom: 1px solid #bababa;
	padding-left: 0px;
	padding-right: 0px;
	color: #000000;
}

#projectCreateNewDropdown .form-group input::placeholder {
	color: #808080;
	font-style: italic;
}

#projectCreateNewDropdown form button {
	color: white;
	font-weight: 500;
	font-size: 14px;
	background-color: var(--primary-color);
}
.project-dropdown-close {
	cursor: pointer;
	color: #808080;
}

#div-share-link-container {
	position: absolute;
	top: 80px;
	right: 12%;
	width: 320px;
	height: 280px;
	border: 1px solid #000;
	background-color: #fff;
	border-radius: 5px;
	z-index: 99999999;
}

#arrow-icon-project-link {
	margin-top: -38px;
	margin-left: 40px;
	color: #ffff;
}

#arrow-icon-project-link i {
	font-size: 40px;
}

#text-code-link-area {
	width: 230px;
	height: 100px;
	border: 1px solid #000;
	border-radius: 10px;
	overflow: hidden;
	padding: 10px;
	box-sizing: border-box;
}

.new-dropdown-container {
	width: 260px;
	/* height: 200px; */
	z-index: 100;
	position: absolute;
	right: 220px;
	margin-top: 10px;
	background-color: #ffffff;
	box-shadow: 0px 0px 10px #00000029;
	border-radius: 5px;
	padding: 15px;
	transition: 0.4s ease-in-out;
	transform: translateY(-150px) translateX(100px) scale(0);
	/* transform: scale(0); */
}
.new-dropdown-container-visible {
	transform: translateY(0px) translateX(0px) scale(1);
	/* transform: scale(1); */
}

.save-ide {
	border-right: 1px solid #00000029;
	font-family: 'Hind', sans-serif;
	font-weight: 600;
}
.playground-text {
	font-family: 'Changa', sans-serif;
	font-weight: 600;
	font-size: 20px !important;
	/* margin-left: -10px; */
}
.playground-text img {
	width: 15px;
	height: 15px;
}
/* .playground-text:hover {
	margin-left: -10px;
} */
.playground-text div:nth-child(1) {
	width: 6px;
	height: 6px;
	background-color: black;
	border-radius: 50%;
	display: inline-block;
	position: relative;
	top: -9px;
}
.playground-text div:nth-child(2) {
	width: 6px;
	height: 6px;
	background-color: black;
	border-radius: 50%;
	display: inline-block;
	position: relative;
	top: 2px;
	left: -11px;
}
.playground-text div:nth-child(3) {
	width: 6px;
	height: 6px;
	background-color: black;
	border-radius: 50%;
	display: inline-block;
	position: relative;
	left: -12px;
	top: -3px;
}

.new-link-container {
	float: left;
	text-align: center;
	background: #ffffff;
	/* margin-right: 30px; */
	width: 120px;
	position: relative;
	height: 67px;
}

.border_left {
	border-left: 1px solid #00000029;
}
.border_right {
	border-right: 1px solid #00000029;
}

#span-new-link-plus {
	position: absolute;
	margin-top: 5px;
	margin-left: 5px;
	font-size: 22px;
}

#share-link-container {
	/* float: left; */
	text-align: center;
	border-left: 1px solid #00000029;
	width: 150px;
	position: relative;
	display: inline-block;
}

#dot-container {
	position: relative;
	width: 50px;
	height: 50px;
	text-align: center;
	float: left;
	margin-right: 10px;
	margin-top: 4%;
	padding: 5px;
	box-sizing: border-box;
	border-radius: 50px;
}

#dot-1,
#dot-2,
#dot-3 {
	position: absolute;
	transform: translateY(-50%);
	top: 38%;
	width: 8px;
	height: 8px;
	transform: matrix(0, 1, -1, 0, 0, 0);
	background: #b9b9b9 0% 0% no-repeat padding-box;
	border-radius: 100%;
}

#dot-2 {
	margin-left: 10px;
}

#dot-3 {
	margin-left: 20px;
}

#show-square-dropdown img {
	width: 30px;
	height: 30px;
	margin-top: 5px;
}
#show-square-dropdown img:hover {
	margin-top: 5px;
}
/* project screen */

/* start of header styling */

#live-icon {
	float: left;
	width: 15px;
	height: 15px;
	background-color: #46b72b;
	border-radius: 100%;
	margin-top: 2px;
}

#header-main-container {
	box-shadow: 0px 1px 5px #00000029 !important;
	opacity: 1;
	z-index: 13;
	padding: 0;
	background-color: var(--primary-bg);
}
.header-main-container-project-screeen {
	height: 67px;
}

#instructor-header-container {
	position: relative;
	width: 180px;
	height: auto;
	/* margin-top: -20px; */
}
#instructor-header-container input {
	border: none;
	outline: none;
	border: 1px solid #c3c3c3;
	border-radius: 50px;
	height: 40px;
	padding-left: 10px;
	padding-right: 30px;
	box-sizing: border-box;
	width: 100%;
}
#input-search-header-container {
	position: relative;
	padding: 0;
	margin-left: 15px;
	width: 300px;
}

#input-search-header {
	border: none;
	outline: none;
	border: 1px solid #c3c3c3;
	border-radius: 50px;
	height: 41px;
	padding-left: 20px;
	padding-right: 45px;
	box-sizing: border-box;
	width: 100%;
	font-size: 15px;
	font-family: 'Hind', sans-serif;
}

#input-search-header::placeholder {
	font-family: 'Hind', sans-serif;
	font-size: 15px;
	color: #d9d9d9;
	padding-top: 10px;
	padding-bottom: 10px;
}

/* for safari */
#input-search-header::-webkit-input-placeholder {
	padding-top: 1px;
}
/* for safari */

#icon-search-header {
	position: absolute;
	bottom: 10px;
	right: 20px;
}

#btn-question-header {
	text-align: center;
    background: var(--primary-light) 0% 0% no-repeat padding-box;
    width: 50px;
    position: relative;
    margin-right: 30px;
    font-size: 25px/18px;
    font-family: 'Hind', sans-serif;
    font-weight: 600;
    letter-spacing: 0px;
    color: var(--primary-color);
    opacity: 1;
    height: 53px;
	margin-top: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}
.header-menu-option {
	padding: 10px;
	font-family: 'Hind', sans-serif;
	color: #333333;
}

#instructor-header-container input::placeholder {
	color: #c3c3c3;
	opacity: 0.8;
	padding-left: 220px;
}
#instructor-header-container input:focus {
	outline: none;
}

#span-class-name {
	position: absolute;
	left: 0;
	top: 20px;
}

#div-time-left-header-container {
	text-align: center;
	border-left: 1px solid #dedede;
	width: 100px;
	position: relative;
	display: inline-block;
	text-align: start;
}
#bolt-icon-header-container {
	width: 35px;
	height: 35px;
	border: 1px solid #333333;
	border-radius: 100%;
	margin-left: 35%;
	margin-top: 10px;
	position: relative;
}

#bolt-icon-header-container img {
	position: absolute;
	left: 6px;
	top: 6px;
}

#live-contributor-header-container {
	float: left;
	text-align: center;
	background: #ffffff;
	border-left: 1px solid #dedede;
	border-right: 1px solid #dedede;
	margin-right: 30px;
	width: 140px;
	position: relative;
	background-color: var(--primary-light);
	color: var(--primary-color);
}
#live-contributor-header-container p {
	margin-top: 20px;
}

.div-list-title {
	float: left;
	margin-left: 5px;
}

.live-contributor-list-item {
	float: left;
	width: 100%;
	height: 50px;
	box-sizing: border-box;
	border-bottom: 1px solid #dddd;
}

.live-contributor-list-item-title {
	font-size: 14px;
}

.live-contributor-list-item-subtitle {
	font-size: 10px;
	color: #a0a0a0;
	float: left;
}

#arrow-icon-live-colaborator {
	position: absolute;
	color: #ffffff;
	font-size: 30px;
	top: -26px;
	right: 15px;
}

#user-info-navbar-container {
	position: relative;
	float: left;
	margin-right: 10px;
	width: max-content;
	height: 65px;
	text-align: right;
}

#div-instructor-name {
	/* font: 'Regular' 26px/18px 'Hind'; */
	letter-spacing: 0px;
	color: #8d8d8d;
	opacity: 1;
	margin-top: 14px;
	margin-right: 0px;
	line-height: 14px;
	/* margin-bottom: 5px; */
}
#div-instructor-name span {
	line-height: 19px;
}
.displayname {
	display: inline-block;
	max-width: 120px;
	white-space: nowrap;
	overflow: hidden !important;
	text-overflow: ellipsis;
	font-size: 17px;
	font-weight: 400;
	color: #333333;
	font-family: 'Avenir Book', sans-serif !important;
}
#div-instructor-designation {
	/* font: 'Regular' 26px/18px 'Hind'; */
	letter-spacing: 0px;
	color: #8d8d8d;
	opacity: 1;
	margin-top: -8px;
	line-height: 21px;
	/* width: 93px; */
}
#div-instructor-designation span {
	display: inline-block;
	font-size: 13px;
	font-weight: 700;
	font-family: 'Avenir', sans-serif !important;
	text-transform: uppercase;
	position: relative;
	top: -3px !important;
}

#div-user-img-container {
	float: left;
}

#user-img {
	height: 35px;
	width: 35px;
	float: left;
	border-radius: 50%;
	background-size: 100% 100%;
	margin-top: 15px;
	position: relative;
	right: 1px;
	cursor: pointer;
	margin-right: 8px;
}
.project-user-img {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 0px;
	padding-top: 0em;
	font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif !important;
	font-size: 1.5em;
	font-weight: 600;
	background-color: #de6834;
	color: #eee;
}

#user-img span {
	position: absolute;
	border-radius: 100%;
	background-color: var(--primary-color);
	color: #ffff;
	left: 25px;
	top: -4px;
}
#page_name {
	font-size: 16px;
}

/* end of header styling */

/* bootstrap css copy */
.navbar-1 {
	position: relative;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-ms-flex-align: center;
	align-items: center;
	-ms-flex-pack: justify;
	justify-content: space-between;
	padding: 0.5rem 1rem;
}
#navbarNav {
	padding: 0 15px;
}
.navbar-brand-1 {
	display: inline-block;
	padding: 0.55rem 0;
	margin-right: 16px;
	font-size: 1.25rem;
	line-height: 1.2rem;
	white-space: nowrap;
	margin-left: 16px;
}

.navbar-brand-1:hover,
.navbar-brand-1:focus {
	text-decoration: none;
}

.navbar-nav-1 {
	display: -ms-flexbox;
	display: flex;
	-ms-flex-direction: column;
	flex-direction: column;
	padding-left: 0;
	margin-bottom: 0;
	list-style: none;
}

.navbar-nav-1 .nav-link {
	padding-right: 0;
	padding-left: 0;
}

.navbar-nav-1 .dropdown-menu {
	position: static;
	float: none;
}

.navbar-text {
	display: inline-block;
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
}

.navbar-collapse {
	padding-right: 15px;
	padding-left: 15px;
	-ms-flex-preferred-size: 100%;
	flex-basis: 100%;
	-ms-flex-positive: 1;
	flex-grow: 1;
	-ms-flex-align: center;
	align-items: center;
}
.navbar-collapse .nav-item {
	font-size: 15px;
	cursor: default;
}

.navbar-toggler-1 {
	padding: 0.25rem 0.75rem;
	font-size: 1.25rem;
	line-height: 1;
	background-color: transparent;
	border: 1px solid transparent;
	border-radius: 0.25rem;
}

.navbar-toggler-1:hover,
.navbar-toggler-1:focus {
	text-decoration: none;
}

.navbar-toggler-1-icon {
	display: inline-block;
	width: 1.5em;
	height: 1.5em;
	vertical-align: middle;
	content: '';
	background: no-repeat center center;
	background-size: 100% 100%;
}

@media (max-width: 575.98px) {
	.navbar-expand-sm > .container,
	.navbar-expand-sm > .container-fluid,
	.navbar-expand-sm > .container-sm,
	.navbar-expand-sm > .container-md,
	.navbar-expand-sm > .container-lg,
	.navbar-expand-sm > .container-xl {
		padding-right: 0;
		padding-left: 0;
	}
}

@media (min-width: 576px) {
	.navbar-expand-sm {
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-ms-flex-pack: start;
		justify-content: flex-start;
	}
	.navbar-expand-sm .navbar-nav-1 {
		-ms-flex-direction: row;
		flex-direction: row;
	}
	.navbar-expand-sm .navbar-nav-1 .dropdown-menu {
		position: absolute;
	}
	.navbar-expand-sm .navbar-nav-1 .nav-link {
		padding-right: 0.5rem;
		padding-left: 0.5rem;
	}
	.navbar-expand-sm > .container,
	.navbar-expand-sm > .container-fluid,
	.navbar-expand-sm > .container-sm,
	.navbar-expand-sm > .container-md,
	.navbar-expand-sm > .container-lg,
	.navbar-expand-sm > .container-xl {
		-ms-flex-wrap: nowrap;
		flex-wrap: nowrap;
	}
	.navbar-expand-sm .navbar-collapse {
		display: -ms-flexbox !important;
		display: flex !important;
		-ms-flex-preferred-size: auto;
		flex-basis: auto;
	}
	.navbar-expand-sm .navbar-toggler-1 {
		display: none;
	}
}

@media (max-width: 767.98px) {
	.navbar-expand-md > .container,
	.navbar-expand-md > .container-fluid,
	.navbar-expand-md > .container-sm,
	.navbar-expand-md > .container-md,
	.navbar-expand-md > .container-lg,
	.navbar-expand-md > .container-xl {
		padding-right: 0;
		padding-left: 0;
	}
}

@media (min-width: 768px) {
	.navbar-expand-md {
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-ms-flex-pack: start;
		justify-content: flex-start;
	}
	.navbar-expand-md .navbar-nav-1 {
		-ms-flex-direction: row;
		flex-direction: row;
	}
	.navbar-expand-md .navbar-nav-1 .dropdown-menu {
		position: absolute;
	}
	.navbar-expand-md .navbar-nav-1 .nav-link {
		padding-right: 0.5rem;
		padding-left: 0.5rem;
	}
	.navbar-expand-md > .container,
	.navbar-expand-md > .container-fluid,
	.navbar-expand-md > .container-sm,
	.navbar-expand-md > .container-md,
	.navbar-expand-md > .container-lg,
	.navbar-expand-md > .container-xl {
		-ms-flex-wrap: nowrap;
		flex-wrap: nowrap;
	}
	.navbar-expand-md .navbar-collapse {
		display: -ms-flexbox !important;
		display: flex !important;
		-ms-flex-preferred-size: auto;
		flex-basis: auto;
	}
	.navbar-expand-md .navbar-toggler-1 {
		display: none;
	}
}

@media (max-width: 991.98px) {
	.navbar-expand-lg-1 > .container,
	.navbar-expand-lg-1 > .container-fluid,
	.navbar-expand-lg-1 > .container-sm,
	.navbar-expand-lg-1 > .container-md,
	.navbar-expand-lg-1 > .container-lg,
	.navbar-expand-lg-1 > .container-xl {
		padding-right: 0;
		padding-left: 0;
	}
}

@media (min-width: 992px) {
	.navbar-expand-lg-1 {
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-ms-flex-pack: start;
		justify-content: flex-start;
	}
	.navbar-expand-lg-1 .navbar-nav-1 {
		-ms-flex-direction: row;
		flex-direction: row;
	}
	.navbar-expand-lg-1 .navbar-nav-1 .dropdown-menu {
		position: absolute;
	}
	.navbar-expand-lg-1 .navbar-nav-1 .nav-link {
		padding-right: 0.5rem;
		padding-left: 0.5rem;
	}
	.navbar-expand-lg-1 > .container,
	.navbar-expand-lg-1 > .container-fluid,
	.navbar-expand-lg-1 > .container-sm,
	.navbar-expand-lg-1 > .container-md,
	.navbar-expand-lg-1 > .container-lg,
	.navbar-expand-lg-1 > .container-xl {
		-ms-flex-wrap: nowrap;
		flex-wrap: nowrap;
	}
	.navbar-expand-lg-1 .navbar-collapse {
		display: -ms-flexbox !important;
		display: flex !important;
		-ms-flex-preferred-size: auto;
		flex-basis: auto;
	}
	.navbar-expand-lg-1 .navbar-toggler-1 {
		display: none;
	}
}

@media (max-width: 1199.98px) {
	.navbar-expand-xl > .container,
	.navbar-expand-xl > .container-fluid,
	.navbar-expand-xl > .container-sm,
	.navbar-expand-xl > .container-md,
	.navbar-expand-xl > .container-lg,
	.navbar-expand-xl > .container-xl {
		padding-right: 0;
		padding-left: 0;
	}
}

@media (min-width: 1200px) {
	.navbar-expand-xl {
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-ms-flex-pack: start;
		justify-content: flex-start;
	}
	.navbar-expand-xl .navbar-nav-1 {
		-ms-flex-direction: row;
		flex-direction: row;
	}
	.navbar-expand-xl .navbar-nav-1 .dropdown-menu {
		position: absolute;
	}
	.navbar-expand-xl .navbar-nav-1 .nav-link {
		padding-right: 0.5rem;
		padding-left: 0.5rem;
	}
	.navbar-expand-xl > .container,
	.navbar-expand-xl > .container-fluid,
	.navbar-expand-xl > .container-sm,
	.navbar-expand-xl > .container-md,
	.navbar-expand-xl > .container-lg,
	.navbar-expand-xl > .container-xl {
		-ms-flex-wrap: nowrap;
		flex-wrap: nowrap;
	}
	.navbar-expand-xl .navbar-collapse {
		display: -ms-flexbox !important;
		display: flex !important;
		-ms-flex-preferred-size: auto;
		flex-basis: auto;
	}
	.navbar-expand-xl .navbar-toggler-1 {
		display: none;
	}
}

.navbar-expand {
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-sm,
.navbar-expand > .container-md,
.navbar-expand > .container-lg,
.navbar-expand > .container-xl {
	padding-right: 0;
	padding-left: 0;
}

.navbar-expand .navbar-nav-1 {
	-ms-flex-direction: row;
	flex-direction: row;
}

.navbar-expand .navbar-nav-1 .dropdown-menu {
	position: absolute;
}

.navbar-expand .navbar-nav-1 .nav-link {
	padding-right: 0.5rem;
	padding-left: 0.5rem;
}

.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-sm,
.navbar-expand > .container-md,
.navbar-expand > .container-lg,
.navbar-expand > .container-xl {
	-ms-flex-wrap: nowrap;
	flex-wrap: nowrap;
}

.navbar-expand .navbar-collapse {
	display: -ms-flexbox !important;
	display: flex !important;
	-ms-flex-preferred-size: auto;
	flex-basis: auto;
}

.navbar-expand .navbar-toggler-1 {
	display: none;
}

.navbar-light-1 .navbar-brand-1 {
	color: rgba(0, 0, 0, 0.9);
}

.navbar-light-1 .navbar-brand-1:hover,
.navbar-light-1 .navbar-brand-1:focus {
	color: rgba(0, 0, 0, 0.9);
}

.navbar-light-1 .navbar-nav-1 .nav-link {
	color: rgba(0, 0, 0, 0.5);
}

.navbar-light-1 .navbar-nav-1 .nav-link:hover,
.navbar-light-1 .navbar-nav-1 .nav-link:focus {
	color: rgba(0, 0, 0, 0.7);
}

.navbar-light-1 .navbar-nav-1 .nav-link.disabled {
	color: rgba(0, 0, 0, 0.3);
}

.navbar-light-1 .navbar-nav-1 .show > .nav-link,
.navbar-light-1 .navbar-nav-1 .active > .nav-link,
.navbar-light-1 .navbar-nav-1 .nav-link.show,
.navbar-light-1 .navbar-nav-1 .nav-link.active {
	color: rgba(0, 0, 0, 0.9);
}

.navbar-light-1 .navbar-toggler-1 {
	color: rgba(0, 0, 0, 0.5);
	border-color: rgba(0, 0, 0, 0.1);
}

.navbar-light-1 .navbar-toggler-1-icon {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-light-1 .navbar-text {
	color: rgba(0, 0, 0, 0.5);
}

.navbar-light-1 .navbar-text a {
	color: rgba(0, 0, 0, 0.9);
}

.navbar-light-1 .navbar-text a:hover,
.navbar-light-1 .navbar-text a:focus {
	color: rgba(0, 0, 0, 0.9);
}

.navbar-span-1 {
	margin-left: 1.5rem !important;
}
.navbar-ul {
	margin-right: auto !important;
}

#square-icon-container {
	position: relative;
	width: 50px;
	height: 50px;
	text-align: center;
	float: left;
	margin-right: 0px;
	margin-top: 3%;
	padding: 5px;
	box-sizing: border-box;
	border-radius: 50px;
}
.square-icon-container-project-screen {
	margin-top: 4%;
}
#square-icon-container img {
	width: 30px;
	height: 30px;
	margin-top: 5px;
}
#square-icon-container img:hover {
	margin-top: 5px;
}

/* css for dropdown */
#show-square-dropdown {
	cursor: pointer;
}
.rect-newplus-container a{
	font-family: 'Hind', sans-serif !important;
}
.rect-newplus-container {
	font-family: 'Hind', sans-serif;
	width: 150px;
	right: 235px;
	padding-left: 0 !important;
	padding-right: 0 !important;
}
.square-dropdown-container-all {
	width: 300px;
	right: 200px;
}
.square-dropdown-container-user {
	width: 210px;
	right: 50px;
}
.square-dropdown-container {
	/* width: 260px; */
	/* height: 200px; */
	z-index: 100;
	top: 0;
	position: absolute;
	background-color: #ffffff;
	box-shadow: 0px 0px 10px #00000029;
	border-radius: 5px;
	padding: 15px;
	transition: 0.4s ease-in-out;
	opacity: 0;
	transform: translateY(-20%) translateX(50%) scale(0);
	/* transform: scale(0); */
	font-size: 15px;
}
.square-dropdown-container .row {
	width: 100%;
	margin: 0;
}
.square-dropdown-container-visible {
	opacity: 1;
    transform: translateY(50px) translateX(0%) scale(1) !important;
}

.rectmenuContainer {
	display: flex;
	align-items: center;
	padding: 0;
	text-align: center;
	padding: 0.7em 1em !important;
	text-transform: capitalize;
}
.notification-dropdown-container a {
	cursor: pointer;
	text-decoration: none;
	color: #848484;
	display: flex;
}
.notification-dropdown-container a:hover {
	text-decoration: none;
	color: #848484;
}
.rectmenuContainer img, .rectmenuContainer svg {
	flex-shrink: 0;
	width: 20px;
	height: 20px;
	margin-right: 10px;
	color: var(--primary-color);
}
.square-dropdown-container .col-md-4,
.menuContainer {
	text-align: center;
	padding: 5px 0px;
}
.square-dropdown-container .col-md-4 a,
.menuContainer a {
	cursor: pointer;
	text-decoration: none;
	color: #848484;
}
.square-dropdown-container .col-md-4 img,
.menuContainer img {
	width: 30px;
	height: 30px;
	display: block;
	margin: auto;
}
.notification-dropdown-container {
	padding-right: 0 !important;
	padding-left: 0 !important;
	width: 150px;
	/* height: 200px; */
	z-index: 100;
	position: absolute;
	right: 40px;
    top: 0px;
	background-color: #ffffff;
	box-shadow: 0px 0px 10px #00000029;
	border-radius: 5px;
	padding: 15px;
	padding-top: 0px !important;
	padding-bottom: 0px !important;
	transition: 0.4s ease-in-out;
	opacity: 0;
	transform: translateY(-20%) translateX(50%) scale(0);
	/* transform: scale(0); */
	font-size: 15px;
}
.notification-dropdown-container .row {
	width: 100%;
	margin: 0;
}
.notification-dropdown-container-visible {
	opacity: 1;
    transform: translateY(50px) translateX(0%) scale(1) !important;
}
.notification-dropdown-container .notification {
	font-family: 'Hind', sans-serif;
	margin-top: 15px;
	padding: 10px;
}
.notification-dropdown-container .notification:hover {
	background-color: #f7f7f7;
}
.notification-dropdown-container .notification p {
	margin: 0;
}
.notification-dropdown-container .log-out-btn a {
	width: 100%;
	border: none;
	background: var(--primary-light);
	color: var(--primary-color);
	font-weight: 500;
	padding: 10px 120px;
	height: 40px;
	border-radius: 0px 0px 5px 5px;
	text-decoration: none;
	cursor: pointer;
	display: block;
}
.notification-dropdown-container .log-out-btn a:focus {
	outline: none;
}

.newSpan {
	/* font-weight: 800; */
	padding-left: 2px;
	font-size: 17px;
}
.newPlus {
	margin-top: 5px;
	font-size: 22px;
	font-weight: 600;
}

.menuContainer :hover,
.notification-dropdown-container :hover {
	background: #f7f7f7; /*0% 0% no-repeat padding-box*/
	border-radius: 5px;
}
.menuContainer a:hover {
	background: none !important;
}
.rect-newplus-container .row:hover {
	background: #f7f7f7;
}


.prev-question-icon a,.next-question-icon a{
	text-decoration: none !important;
	color: var(--primary-color);
}



/* for mobile view */
@media only screen and (max-width: 992px) {
	#header-main-container{
		z-index: 12;
	}
	.navbar-toggler-1 {
		outline: none !important;
		margin-right: 10px;
		font-size: 20px;
	}
	#div-user-img-container {
		float: right;
	}
	.full-list-mobile-view {
		position: relative;
	}
	#user-info-navbar-container {
		position: absolute;
		right: 60px;
	}
	#btn-question-header {
		z-index: 1;
		position: absolute;
		right: 115px;
	}
	.square-dropdown-container {
		right: 30%;
	}
	.col-6 {
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		max-width: 50%;
	}
	.row {
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		margin-right: -15px;
		margin-left: -15px;
	}
	.navbar-collapse.in {
		overflow: unset !important;
	}
	.modal-dialog {
		width: 90% !important;
		margin-right: auto;
		margin-left: auto;
	}
	#navbarNav {
		padding: 0px;
	}
	.mobile-navbar-options {
		display: block !important;
	}
	.mobile-navbar-options ul {
		list-style: none;
		cursor: default !important;
	}
	.mobile-view-header-options {
		font-family: 'Hind', sans-serif;
		display: block;
		margin-bottom: 0.5rem;
		text-align: center;
		border-bottom: 1px solid rgba(137, 52, 14, 0.4);
		padding: 10px 5px;
		cursor: default !important;
	}
	.mobile-view-header-options a {
		color: white;
		text-decoration: none;
	}
	.notification-dropdown-container {
		display: none;
	}
	#user-img {
		cursor: default;
	}
	.mobile-view-header-options:hover {
		padding: 10px 5px;
		margin-bottom: 0.5rem;
	}
	.container-fluid.dashboard-container {
		margin-top: 0px !important;
	}
	.time-left-container {
		margin-left: 0px !important;
	}
	.quiz-title-header {
		/* margin-left: 0px !important; */
		display: none;
	}
	#btn-question-header {
		display: none;
	}
	/* navbar open from right side */
	.navbar-toggler-1 {
		z-index: 10;
		outline: none !important;
	}
	.navbar-collapse {
		position: absolute;
		top: 65px;
		left: 100%;
		width: 50%;
		transition: all 0.2s ease-in-out;
		display: block;
		background-color: var(--primary-color);
		padding: 100px 50px;
		color: white;
	}
	.navbar-nav-1 li div,
	.navbar-nav-1 li div span,
	#btn-question-header {
		color: white !important;
	}
	.navbar-collapse.collapsing {
		height: calc(100vh - 65px) !important;
		margin-left: 50%;
		left: 50%;
		transition: all 0.2s ease-in-out;
	}
	.navbar-collapse.show {
		left: 50%;
		height: calc(100vh - 65px);
	}
	/* navbar open from right side */
}
@media only screen and (max-width: 350px) {
	#input-search-header-container {
		width: 250px;
	}
}
/* for mobile view */
.live-icon {
	display: inline-block;
	width: 10px;
	height: 10px;
	background: #46b72b;
	border-radius: 50%;
}
.timer {
	width: 75%;
	float: left;
	margin-top: 10px;
}
@media (max-width: 768px) {
	.quizHeader {
		width: -webkit-fill-available;
	}
}
@media (max-width: 630px) {
	.timer {
		width: 73%;
	}
}
@media (max-width: 500px) {
	.timer {
		width: 70%;
	}
}
.left-navbar{
	margin-left: 30px;
	display: flex;
}
.left-navbar div{
	padding-left: 15px;
	padding-right: 15px;
	margin-left: 8px;
	margin-right: 8px;
	height: 53px;
	display: flex;
	align-items: center;
}
.left-navbar div a{
	color: var(--primary-color);
	font-size: 15px;
	font-weight: 400;
	text-decoration: none;
	margin-top: 5px;
	font-family: 'Avenir','sans-serif' !important;
}
.left-navbar div.active{
	border-bottom: 3px solid var(--primary-color);
}
.left-navbar div.active a{
	font-weight: 500;
	margin-top: 10px;
}



/* temp style */
.dropdown *{
	font-weight: 400 !important;
	font-family: 'Hind', sans-serif !important;
}
.btn.dropdown-toggle::after{
	font-size: 17px !important;
	color: #818181 !important;
}
/* temp style */
.ql-tooltip{
	left: 10px !important;
} 

.cursor-pointer{
	cursor: pointer !important;
}

.tag-editor .placeholder{
	pointer-events: none;
	user-select: none;
	-moz-user-select: -moz-none;
}