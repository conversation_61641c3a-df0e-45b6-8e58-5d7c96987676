table {
    width: 100%;
    background-color: var(--primary-bg);
    border: 1px solid #f2f2f2;
}

.responsive-table {
    width: 100%;
    overflow: auto;
    height: calc(100vh - 155px);
    border-radius: 10px !important;
    -webkit-box-shadow: 0 -4px 3px -4px rgb(99 99 99 / 20%);
            box-shadow: 0 -4px 3px -4px rgb(99 99 99 / 20%);
    padding-bottom: 20px;
    background-color: var(--primary-bg);
}
table.dataTable tbody tr {
    background-color: var(--primary-bg);
}

#tblCCLst td,.responsive-table table td {
    border-top: 1px solid var(--primary-bg-1) !important;
    border-bottom: 1px solid var(--primary-bg-1) !important;
    border-left: none !important;
    border-right: none !important;
    vertical-align: middle !important;
}


#tblCCLst th,.responsive-table table th {
    font-size: 15px;
    font-family: 'Avenir', sans-serif;
    padding: 10px 0px 5px 17px !important;
}
#tblCCLst td,.responsive-table table td{
    font-size: 15px;
    font-family: 'Avenir Book', sans-serif;
    padding: 10px 0px 5px 17px !important;
}

#tblCCLst thead th, .responsive-table table thead th {
    position: sticky;
    top: 0px;
    z-index: 10;
    background-color: var(--primary-bg);
    text-transform: uppercase;
    border-bottom: 1px solid var(--primary-bg-1);
    /* padding: 15px 19px !important; */
    padding: 10px !important;
    -webkit-box-shadow: rgb(125 125 126 / 10%) 0px 1px 0px;
            box-shadow: rgb(125 125 126 / 10%) 0px 1px 0px;
}

#tblCCLst tr td:nth-child(1),.responsive-table table tr td:nth-child(1) {
    padding-right: 0 !important;
    padding-left: 10 !important;
    text-align: right;
}

.v-dots {
    width: 50px;
    height: 20px;
    background-image: -o-radial-gradient(circle, #B9B9B9 1.5px, transparent 2.5px);
    background-image: radial-gradient(circle, #B9B9B9 1.5px, transparent 2.5px);
    background-size: 100% 33.33%;
}

/* datatable css */
table.dataTable thead .sorting_asc {
    background-image: none !important;
}
table.dataTable thead .sorting {
    background-image: none !important;
}
table.dataTable thead .sorting span:after {
    font-family: 'FontAwesome';
    content: "\f0dc";
    top: 0px;
    opacity: 0.5;
    position: relative;
    left: 5px;
}

table.dataTable thead .sorting_asc span:after {
    font-family: 'FontAwesome';
    content: ' \f0d7 ' !important;
    top: 2px;
    opacity: 0.5;
}

table.dataTable thead .sorting_desc span:after {
    font-family: 'FontAwesome';
    content: '\f0d8 ' !important;
    top: 0px;
    opacity: 0.5;
    left: 3px;
    position: relative;
}

#tblCCLst_wrapper .top{
    display: none !important;
}

.dataTables_length select {
    cursor: pointer;
    font-size: 14px;
}

.dataTables_length select {
    font-weight: 400;
    /* background-color: #F5D7C8; */
    color: #8d8d8d;
    border-radius: 3px;
    -webkit-box-shadow: inset 0px 0px 3px 0px #00000029;
            box-shadow: inset 0px 0px 3px 0px #00000029;
    border: 1px solid #d1d1d1;
    height: 30px;
    outline: none;
    width: 55px;
    padding: 4px;
    margin-right: 70px;
    display: none !important;
}

.dataTables_length::before {
    font-size: 15px !important;
}

.dataTables_length::before {
    content: 'Entries';
    position: absolute;
    left: -52px;
    top: 5px;
    color: #8d8d8d;
    pointer-events: none;
    z-index: 1;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #fafafa 0% 0% no-repeat padding-box;
    -webkit-box-shadow: 0px 1px 2px #00000029;
            box-shadow: 0px 1px 2px #00000029;
    border: 1px solid #c7c7c7;
    border-radius: 3px;
    color: #333333 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    color: #9b9b9b !important;
    margin: 0;
    padding: 0;
    padding: 3px 9px 0px 9px !important;
    font-size: 14px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #fafafa 0% 0% no-repeat padding-box;
    -webkit-box-shadow: 0px 1px 2px #00000029;
            box-shadow: 0px 1px 2px #00000029;
    border: 1px solid #c7c7c7;
    border-radius: 3px;
    color: #333333 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: none !important;
    border: 1px solid #c7c7c7;
    color: #9b9b9b !important;
    margin: 0;
    padding: 0;
    padding: 3px 9px 0px 9px !important;
    font-size: 14px;
}

.bottom {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin-top: 30px;
    margin-bottom: 30px;
}

table.dataTable.no-footer {
    width: 99%;
    border: 0px solid #D1D1D1 !important;
    -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
            box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled .fa {
    opacity: .5;
    cursor: not-allowed !important;
}

.dataTables_empty{
    text-align: center !important;
}

.responsive-table{
    border-top-left-radius: 15px !important;
    border-top-right-radius: 20px !important;
}

#tblCCLst th:first-child,.responsive-table th:first-child {
    border-top-left-radius: 10px;
}
#tblCCLst th:last-child, .responsive-table th:last-child {
    border-top-right-radius: 10px;
}

#tblCCLst tr:last-child td:first-child, .responsive-table tr:last-child td:first-child {
    border-bottom-left-radius: 10px;
}

#tblCCLst tr:last-child td:last-child, .responsive-table tr:last-child td:last-child {
    border-bottom-right-radius: 10px;
}


table.dataTable{
    border-radius: 10px;
}

table.dataTable tbody>tr.selected, table.dataTable tbody>tr>.selected {
    background-color: #E8E8E8 !important;
}

.dataTables_length{
    font-size: 14px !important;
    line-height: 20px !important;
    font-family: 'Hind', sans-serif !important;
}

.dataTables_length .btn.dropdown-toggle.btn-default {
    width: 60px;
    padding: 3px 0px 2px 8px;
}

.dataTables_length .btn.dropdown-toggle::after{
    font-size: 15px !important;
    left: -10px !important;
}
.dataTables_length .bootstrap-select .dropdown-menu{
    min-width: 60px !important; 
}
.dataTables_length .open>.dropdown-toggle.btn-default{
    background: none !important;
}
.dataTables_length .btn-default:hover{
    background: none !important;
}

.dataTables_processing {
    top: 80px !important;
    height: 50px;
    z-index: 10;
    color: var(--primary-color) !important;
    font-weight: 700 !important;
    font-size: 17px !important;
    font-family: 'Mulish', sans-serif !important;
}