$('#input-search-header-container').show();
// header search of datatables
$("#input-search-header")
.removeAttr('onkeyup')
.unbind() // Unbind previous default bindings
.bind("input", function(e) { // Bind our desired behavior
	// If the length is 3 or more characters, or the user pressed ENTER, search
	var val =$(this).val().replace(/[^\w\s\][^,-@ ]/gi, '')
	const reportsSearch2 = JSON.parse(localStorage.getItem('userAttemptSearch')) || {};
	if(!reportsSearch2[userId]){
		reportsSearch2[userId] = {};
	}
	reportsSearch2[userId].searchValue = val ;
	localStorage.setItem('reportsSearch2', JSON.stringify(reportsSearch2));

	// console.log("this.value " + val );
	$(this).val( val )

	if( window.intervalId )
		clearInterval( window.intervalId )
	window.intervalId = setTimeout( function(){
		table.search(val.trim()).draw();
	}, 500 )
	return;
});

/**
 * 
 * @param {string} type 
 */
function setListType(type) {
	localStorage.setItem(`listTypeUserAttempt:${userId}`, type);
}

function getListType() {
	return localStorage.getItem(`listTypeUserAttempt:${userId}`);
}

function showEdits(el) {
	// $(this).next().toggleClass('hide');
	let check = $(el).next().hasClass('d-none');
	if (check) {
		$("#tblCCLst tr td:last-child").css({'z-index': '1'});
		$('.dropdown-list.user-options').addClass('d-none');
		$('.dots-btn').css({ 'background-color': ' white' });
		$(el).next().removeClass('d-none');
		$(el).parent().parent().css({'z-index': '2'})
		$(el).css({ 'background-color': '#EFEFEF' });
	} else {
		$("#tblCCLst tr td:last-child").css({'z-index': '1'});
		$('.dropdown-list.user-options').addClass('d-none');
		$('.dots-btn').css({ 'background-color': ' white' });
		$(el).next().addClass('d-none');
		$(el).css({ 'background-color': ' white' });
	}
}

function convertTimeHTML(timeDate, isSingleLine=false){
	if(!timeDate)
	return "<p style='text-align:center;margin:0'>-NA-</p>"

	if(timeDate == null)
	return "<p style='text-align:center;margin:0'>-NA-</p>"


	let timeDateInt = +timeDate;
	timeDate = new Date( isNaN( timeDateInt ) ? timeDate : timeDateInt )  

	if(timeDate == 'Invalid Date'){
		return "<p style='text-align:center;margin:0'>-NA-</p>";
	}
	// time converstion
	let hours = timeDate.getHours();
	let minutes = timeDate.getMinutes();
	let hr = hours<10 ? '0'+hours : hours;
	let min = minutes < 10 ? '0'+minutes : minutes;
	let date = timeDate.getDate();
	if(date<10){
		date = `0${timeDate.getDate()}`
	}
	let month = (timeDate.getMonth()+1);
	if(month < 10){
		month = `0${(timeDate.getMonth()+1)}`
	}
	let lineBreak = ' ';
	//return timeDate = date+'-'+month+'-'+timeDate.getFullYear()+' ,  '+strTime;
	if(!isSingleLine){
		lineBreak = '<br>';
	}
	return "<p style='text-align:center;margin:0'>"+date+'-'+month+'-'+timeDate.getFullYear()+`${lineBreak}`+ hr +":"+ min+"</p>";
}




var totalColumns = $('#tableList thead th').length - 1;
// datatables init
var table = $('#tableList').DataTable({
	language: {
		paginate: {
			next: '<i class="fa fa-caret-right" aria-hidden="true"></i>', // or '→'
			previous: '<i class="fa fa-caret-left" aria-hidden="true"></i>', // or '←'
		},
		sLengthMenu: '_MENU_',
	},
	oLanguage: {
		oPaginate: {
			sFirst: '',
			sLast: '',
		},
	},
	'drawCallback':function(){
		$('.user-sTime').each((ind,val)=>{
			if(ind > 0){
				if($(val).text()){
                    const dateObj = dayjs(parseInt(val));
                    if (dateObj.isValid()) {
                        $(val).text(dateObj.format('DD/MMM/YYYY HH:mm:ss a'))
                    }
					$(val).text(convertTime(new Date($(val).text().trim())))
				}
			}
		})

		$('.user-eTime').each((ind,val)=>{
			if(ind > 0){
                if ($(val).text()) {
                    const dateObj = dayjs(parseInt(val));
                    if (dateObj.isValid()) {
                        $(val).text(dateObj.format('DD/MMM/YYYY HH:mm:ss a'))
                    }
					$(val).text(convertTime(new Date($(val).text().trim())))
                }
			}
		})
	},
	dom: '<"top"f>rt<"bottom"lp><"clear">',
	sPaginationType: 'simple_numbers',
	lengthMenu: [20, 50, 100],
	columnDefs: [
		{
			targets: 0,
			className: 'select-checkbox',
			width: '10px',
			orderable: false,
		},
		{
			targets: totalColumns,
			orderable: false,
		},
	],
	select: {
		style: 'multi',
	},
	order: [[1, 'asc']],
	"fnRowCallback": function( nRow, aData, iDisplayIndex ) {
		var oSettings = this.fnSettings ();
		// console.log(oSettings._iDisplayStart+iDisplayIndex +1+'.',$("td:eq(0)", nRow).text().trim())
		// console.log($("td:eq(0)", nRow).children('span').remove())
		$("td:eq(0)", nRow).children('span').remove()
		let currentName = $("td:eq(0)", nRow).text().trim();
		$("td:eq(0)", nRow).html(`<span class="span-srno">${oSettings._iDisplayStart+iDisplayIndex +1}.</span> ${currentName}`);
	}
});
// datatables init


$('#tableList').on( 'length.dt', function ( e, settings, len ) {
    console.log( 'New page length: '+len );
} );

// Check/uncheck all checkboxes in the table


const openColumnList = () => {
	if ($('.column-list-btn').hasClass('active')) {
		$('.column-list-btn').removeClass('active');
		$('.columns-list-container').addClass('d-none');
	} else {
		$('.column-list-btn').addClass('active');
		$('.columns-list-container').removeClass('d-none');
	}
};

function insert(str, index, value) {
	return str.substr(0, index) + value + str.substr(index);
}

//selected users

const convertToCsv = (data) => {
	const csvRows = [];
	const header = Object.keys(data[0]);
	csvRows.push(header.join(','));
	for (const row of data) {
		csvRows.push(Object.values(row).join(','));
	}
	return csvRows.join('\n');
};
var selectedRange = '0';
function downloadCSV(data) {
	const blob = new Blob([data], { type: 'text/csv' });
	const url = window.URL.createObjectURL(blob);
	const a = document.createElement('a');
	a.setAttribute('hidden', '');
	a.setAttribute('href', url);
	a.setAttribute('download', `${$('.quiz-name').text()}.csv`);
	document.body.appendChild(a);
	a.click();
	document.body.removeChild(a);
}

function convert(timeDate){
	timeDate = new Date( (typeof(timeDate) === 'string' && !isNaN(timeDate)) ? Number(timeDate) : timeDate );
	if(timeDate == 'Invalid Date'){
		return '-NA-';
	}
	// time converstion
	let hours = timeDate.getHours();
	let minutes = timeDate.getMinutes();
    hours = hours<10 ? '0'+hours : hours;
	minutes = minutes < 10 ? '0'+minutes : minutes;
	let strTime = hours + ':' + minutes;
	let date = timeDate.getDate();
	if(date<10){
		date = `0${timeDate.getDate()}`
	}
	let month = (timeDate.getMonth()+1);
	if(month < 10){
		month = `0${(timeDate.getMonth()+1)}`
	}
	return timeDate = date+'-'+month+'-'+timeDate.getFullYear()+' '+strTime;
}


const exportToCsv = () => {
	let tableData;
	var heads = [];
	let finalData = [];
	/** @type {"all" | "attempted" | "not-attempted"} */
	const attemptStatus = "all";
	if($("#listType").val() == 2){
		if(!sampleData.emails || sampleData.emails.length <= 0){
			toggleAlertMsgModal('There is no record to export.',2);
			return;
		}

		sampleData.emails.forEach((val)=>{
			const isAttempted = setOfUserEmailsAttemptedTest.has(val);
			if(attemptStatus === "attempted" && !isAttempted) {
				return;
			}
			if(attemptStatus === "not-attempted" && isAttempted) {
				return;
			}
			finalData.push({
				Email: val,
				"Attempt Status": isAttempted ? "Attempted" : "Not Attempted"
			});
		})

		let csvData = convertToCsv(finalData);
		downloadCSV(csvData);
		return;
	}
	if($("#listType").val() == 3){
		if(!invitedUsers || invitedUsers.length <= 0){
			toggleAlertMsgModal('There is no record to export.',2);
			return;
		}
		invitedUsers.forEach((val)=>{
			const isAttempted = setOfUserEmailsAttemptedTest.has(val);
			if(attemptStatus === "attempted" && !isAttempted) {
				return;
			}
			if(attemptStatus === "not-attempted" && isAttempted) {
				return;
			}
			finalData.push({
				Email: val.email,
				Status: val.status,
				Expired:convert(val.validTill),
				"Attempt Status": isAttempted ? "Attempted" : "Not Attempted"
			})
		})

		let csvData = convertToCsv(finalData);
		downloadCSV(csvData);
		return;
	}
	if(table.rows().data().length <= 0){
		toggleAlertMsgModal('There is no record to export.',2);
		return;
	}
	tableData = table.rows().data();
	table.columns().every(function () {
		const columnTitle = this.header().textContent;
		if(columnTitle) {
			heads.push(columnTitle);
		}  
	});
	heads.push('Dashboard Link')
    // console.log('heads',heads);

	let correctDataOfTable = [];
	correctDataOfTable.push(heads.join(","))

	let object = table.rows().data()
	if (table.rows('.selected').data().length) {
		object = table.rows('.selected').data();
	}
	// console.log('object',object[0])

	for (const property in object) {
		let rows = [];
		//console.log(object);
		if(property < table.rows().data().length){
			rows.push(object[property].displayname)
			rows.push(object[property].email);
			rows.push(convert(object[property].startTime))
			if(object[property].endTime){
				rows.push("Completed");
			}else{
                if(Date.now() > quizReportQuizEndTime){ 
					rows.push("Not submitted");
				}else{ 
					rows.push("In progress");
				}
			}

			if(object[property].endTime){
				rows.push(convert(object[property].endTime))
			}else{
				rows.push('-NA-')
			}

			let timeTakenInMins;
			if(object[property].timeTaken){
				let remainingMin = Math.floor(+object[property].timeTaken);
				let remainingSec = parseInt((+object[property].timeTaken - remainingMin) * 60);
				if(remainingSec) timeTakenInMins = `${remainingMin} min ${remainingSec} sec`;
				else timeTakenInMins = `${remainingMin} min`;
			}

			if(timeTakenInMins){
				rows.push(timeTakenInMins)
			}else{
				rows.push('-NA-')
			}

			if(object[property].score){
				rows.push((object[property].score));
			}else{
				rows.push('0');
			}
            
			if(object[property].tabSwitchCount){
				rows.push((object[property].tabSwitchCount));
			}else{
				rows.push('0');
			}

			if(object[property].tabSwitchInCount){
				rows.push((object[property].tabSwitchInCount));
			}else{
				rows.push('0');
			}

			if(object[property].fullScreenOutCount){
				rows.push((object[property].fullScreenOutCount));
			}else{
				rows.push('0');
			}

			if(object[property].fullScreenInCount){
				rows.push((object[property].fullScreenInCount));
			}else{
				rows.push('0');
			}

			if(object[property].codePasteCount){
				rows.push((object[property].codePasteCount))
			}else{
				rows.push('0')
			}

			if(object[property].loginCount){
				if(object[property].currentIp){
					rows.push((`${object[property].loginCount} (${object[property].currentIp})`))
				}else {
					rows.push((object[property].loginCount))
				}
			}else{
				rows.push('0')
			}

			if(object[property].totalAttemptedQuestion){
				rows.push((object[property].totalAttemptedQuestion))
			}else{
				rows.push('0')
			}

			if(object[property].camBlockCount){
				rows.push((object[property].camBlockCount))
			}else{
				rows.push('0')
			}
			if(object[property].qualified){
				rows.push("Pass")
			}else{
				rows.push('Fail')
			}

			let jsonCSVuserDetails = {};
			object[property].quizUserDetails.forEach((data,index)=>{
			   jsonCSVuserDetails[data.fieldLabel] = data.fieldValue ? data.fieldValue.replace(/,/g, ' | ') : data.fieldValue;
			})  
            console.log("jsonCSVuserDetails",jsonCSVuserDetails)
			if(quizUserDetails){
			    quizUserDetails.forEach((details,index)=>{
			    	if(jsonCSVuserDetails[details.fieldLabel]){
			    		rows.push(jsonCSVuserDetails[details.fieldLabel]);
			    	}else{
			    		console.log(object[property].displayname+'.'+index, details.fieldLabel+":"+jsonCSVuserDetails[details.fieldLabel]);
			    		rows.push("-");
			    	}
			    })
		    }

			rows.push(`${location.origin}/useranswer/attemptedQuestion/quiz/${object[property]._id}/${quizId}`);
			correctDataOfTable.push(rows.join(","));         
		} 
	}
	const csvData = correctDataOfTable.join('\n');
	downloadCSV(csvData);
};

function convertTime(timeDate){
	timeDate = new Date(timeDate);
	if(timeDate == 'Invalid Date'){
		return '-NA-';
	}
	// time converstion
	let hours = timeDate.getHours();
	let minutes = timeDate.getMinutes();
	hours = hours<10 ? '0'+hours : hours;
	minutes = minutes < 10 ? '0'+minutes : minutes;
	let strTime = hours + ':' + minutes;
	let date = timeDate.getDate();
	if(date<10){
		date = `0${timeDate.getDate()}`
	}
	let month = (timeDate.getMonth()+1);
	if(month < 10){
		month = `0${(timeDate.getMonth()+1)}`
	}
	timeDate = date+'-'+month+'-'+timeDate.getFullYear()+' ,  '+strTime;
	return timeDate;
}

$(document).ready(function () { 
  let typeOfTable = getListType();
  if(typeOfTable=='invited'){
	  $("#listType").val('3').change();
  }else if(typeOfTable=='allowed'){
	  $("#listType").val('2').change();
  }else{
	$("#listType").val('1').change();
  }
})

function appendTableData() {
	$('#listHead').empty();
	$('#listData').empty();
}

function logout(userId, quizId) {
    console.log(userId, quizId);
    if (!userId || !quizId) {
        toggleAlertMsgModal(`Unable to logout user payload is not valid.`, 2);
        return;
    }
    let message = 'Are you sure you want to log out this user from the test?';
    toggleAlertMsgModal({message}, async (val) => {
        try {
            const rowResponse = await fetch(`/test/logoutUser/${userId}/${quizId}`);
            const result = rowResponse.json();
            if (result?.error) {
                throw new Error(result?.error);
            }
            if (result?.msg) {
                return toggleAlertMsgModal(result?.msg, 1);
            }
            toggleAlertMsgModal('User Logged out');
        } catch (error) {
            console.log(error);
            toggleAlertMsgModal(error?.message ?? error, 2);
        }
    })
} 


const toggleFilter = () =>{
	if($("#listTypeContainer").hasClass('d-none')){
		$("#listTypeContainer").removeClass('d-none')
		$('.filter_icon').addClass('active');
	}else{
		$("#listTypeContainer").addClass('d-none')
		$('.filter_icon').removeClass('active');
	}
}


var urlSearch = new URL(window.location.href);
var fromDB = urlSearch.searchParams.get("fromdb");
console.log(fromDB);

