.all-question-description-container .ql-syntax {
	background: #ebebeb;
	border: none;
	font-size: 13px;
	padding: 10px;
	border-radius: 4px;
	margin-bottom: 1rem;
}
.submit_choices {
	background-color: var(--primary-color);
	border: 1px solid var(--primary-color);
	float: right;
	text-transform: lowercase;
}
.submit_choices:hover {
	background-color: var(--primary-hover) !important;
    border-color: var(--primary-hover) !important;
    color: white !important;
}
#div-question-title-underline-container {
	float: left;
	/* margin-left: 1%; */
	margin-top: 1%;
	width: 200px;
	height: 5px;
}

#question-title-underline-1,
#question-title-underline-2,
#question-title-underline-3 {
	float: left;
	height: 6px;
}

#question-title-underline-1 {
	width: 50px;
	background: var(--primary-light) 0% 0% no-repeat padding-box;
	opacity: 1;
}

#question-title-underline-2 {
	width: 50px;
	background: var(--primary-medium) 0% 0% no-repeat padding-box;
	opacity: 1;
}

#question-title-underline-3 {
	width: 100px;
	background: var(--primary-color) 0% 0% no-repeat padding-box;
	opacity: 1;
}
body {
	font-family: 'Hind', sans-serif !important;
}
.navbar {
	margin-bottom: 0;
}
.left-menu {
	background-color: white;
	padding: 0px 0px 0px 0px;
	color: #8a8786 !important;
	box-shadow: 0px 0px 6px #00000029;
	min-height: calc(100vh - 70px);
	max-height: calc(100vh - 70px);
	position: absolute;
	width: 65px;
	transition: 0.1s ease-in-out;
	display: grid;
	grid-template-columns: 1fr 1fr;
}
.left-menu.closed {
	transform: translateX(-63px);
}
.left-menu.opened {
	transform: translateX(0px);
}
.left-menu-left-side-container {
	display: block;
}
.left-menu-right-side-container {
	height: 100%;
	background: #ffefe6;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}
.left-menu-right-side-container img {
	width: 15px;
	height: 15px;
	transition: 0.2s ease-in-out;
}
.left-menu.closed .left-menu-right-side-container img {
	transform: rotate(-90deg);
}
.left-menu.opened .left-menu-right-side-container img {
	transform: rotate(90deg);
}
.left-menu:hover {
	/* transition: 0.2s ease-in-out; */
	/* transform: translateX(0%); */
}
.sideBarScroll {
	max-height: calc(100vh - 111px);
	overflow: auto;
	scroll-behavior: smooth;
	min-height: calc(100vh - 160px);
}
.sideBarScroll::-webkit-scrollbar {
	display: none;
}
.scroll-up-icon-container {
	background: #ffefe6;
	height: max-content;
	text-align: center;
	padding: 2px 0px;
	cursor: pointer;
}
.scroll-up-icon-container img {
	width: 20px;
	height: 20px;
	transform: rotate(180deg);
}
.scroll-down-icon-container {
	background: #ffefe6;
	height: max-content;
	text-align: center;
	padding: 2px 0px;
	cursor: pointer;
}
.scroll-down-icon-container img {
	width: 20px;
	height: 20px;
}
.segmentTitle {
	color: var(--primary-color);
	text-align: center;
	font-family: 'Hind', sans-serif !important;
	font-weight: 400;
	font-size: 15px;
	word-break: break-all;
	padding: 0px 5px;
	padding-top: 10px;
}
ul.dashboard-left-nav.nav.nav-tabs.tabs-left li a {
	color: #595959;
	margin: 0;
	font-size: 15px;
	font-family: 'Hind', sans-serif !important;
	font-weight: 400;
	padding: 10px 10px;
	text-align: center;
}
.nav-tabs > li {
	margin-bottom: 0px;
}
.fa.fa-navicon {
	color: #8a8786 !important;
}
#quizContent {
	padding: 0px;
	/* overflow: hidden; */
}
.codingQuestionContainer {
	overflow: hidden;
}
input.execute_button {
	background-color: var(--primary-color) !important;
	border-color: var(--primary-color) !important;
	font-family: 'Hind', sans-serif !important;
	font-size: 15px;
	font-weight: 400;
	text-transform: lowercase;
}
input.execute_button:hover {
	background-color: var(--primary-hover) !important;
	border-color: var(--primary-hover) !important;
	color: white !important;
}
li.opened {
	/* background-color: var(--primary-light) !important;
    color: white; */
}
li.opened a {
	color: var(--primary-color) !important;
	font-weight: 600 !important;
}
#iconsLeftBar li a:hover {
	background-color: white !important;
	color: var(--primary-color) !important;
	font-weight: 600 !important;
}
#quizContainer {
	height: calc(100vh - 65px);
	overflow: hidden;
}
.technology_link {
	box-shadow: none !important;
	border: none;
	padding-left: 0px;
	margin-bottom: 1.5rem;
}
.question-name {
	font-size: 22px !important;
	font-weight: 500 !important;
	font-family: 'Hind', sans-serif !important;
}
.question_info {
    word-break: break-all;
    padding: 0px 0px 1px 0px;
    position: relative;
    top: -30px;
    height: unset;
}
.question_info p {
	font-family: hind, sans-serif !important;
	font-weight: 400;
	font-size: 15px;
	margin-bottom: 1rem;
}
.question_info p.describe{
	display: none;
}
.question_info p strong {
	font-family: hind, sans-serif !important;
	font-weight: 600;
}
.glyphicon.glyphicon-ok {
	color: var(--primary-color);
	position: absolute;
	top: 15px;
	font-size: 15px;
}
.select_language_section {
	background-color: #fafafa;
	padding: 0px;
	margin-top: 12px;
	border: none;
}

.locked-segment {
	height: max-content;
	position: relative;
}
.locked-segment-overlay-div {
	background: rgba(235, 235, 235, 0.6);
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: center;
}
.locked-segment-overlay-div-text {
	text-align: center;
}
.locked-segment-overlay-div-text img {
	width: 20px;
	height: 20px;
	cursor: default;
}

.bottom-nav-container {
	position: fixed;
	width: 41%;
	bottom: 0px;
	background: #fafafa;
	padding-left: 40px;
	padding-bottom: 6px;
	padding-top: 5px;
}
#previousBtn,
#nextBtn {
	font-family: 'Hind', sans-serif !important;
	font-size: 16px;
	text-decoration: none;
	color: #5c5c5c;
}
#nextBtn {
	padding: 2px 2px 1px 10px;
}
#previousBtn {
	padding: 2px 8px 1px 1px;
}
#previousBtn i,
#nextBtn i {
	color: var(--primary-color);
	font-size: 18px;
	position: relative;
	top: 2px;
	margin: 0 5px;
}
#previousBtn:hover,
#nextBtn:hover {
	color: #5c5c5c;
	background-color: var(--primary-light);
	border-radius: 5px;
}
#question-type-badge {
	background: #f7f7f7 0% 0% no-repeat padding-box;
	letter-spacing: 0px;
	color: #333333;
	opacity: 1;
	border-radius: 3px;
}
.badge.badge-secondary,
.badge.badge-success {
	font-family: 'Montserrat', sans-serif !important;
	font-weight: 400;
	font-size: 10px;
	padding: 4px 5px;
}
.coding-question-description-container {
	max-height: calc(100vh - 53px);
	overflow: auto;
	width: 40%;
	position: relative;
	float: left;
	scrollbar-color: transparent transparent;
	scrollbar-width: thin;
	/* display: inline-block; */
}
.coding-question-description-container:hover {
	scrollbar-color: initial;
}
.coding-question-description-container pre {
	background: #ebebeb;
	border: none;
	font-size: 13px;
	padding: 10px;
	border-radius: 4px;
}
.coding-question-code-attempt-area {
	width: 60.5%;
	/* display: inline-block; */
	position: relative;
	height: 100%;
}
.web-question-code-attempt-area {
	width: 60.5%;
	/* display: inline-block; */
	position: relative;
	height: 100%;
}

.subject-question-container {
	overflow: auto;
	height: 100vh;
}
.top_right_question_bar,
#question-list,
.dashboard-left-nav.tab-content,
#quizContent {
	height: 100%;
}
#hideInType5 {
	padding: 0;
}
.code_edtor {
	border: none;
}
/* custom select css */
.bootstrap-select,
.filter-option-inner-inner {
	font-family: 'Hind', sans-serif !important;
	font-weight: 600 !important;
	color: #818181 !important;
	font-size: 15px;
}
.dropdown-item.active,
.dropdown-item:active {
	background-color: #0000001a;
	outline: none !important;
}
.dropdown-item.active,
.dropdown-item:active {
	color: #333333;
}
.btn.dropdown-toggle.btn-light {
	background: white;
	border: 1px solid #e0e0e0;
	outline: none !important;
	color: #9b9b9b;
	/* box-shadow: inset 0px 0px 1px 0px #4e4e4e !important; */
}
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:focus,
.dropdown-menu > .active > a:hover {
	background-color: #0000001a;
	color: #333333;
}
.dropdown-menu.show {
	padding: 0 !important;
	margin-top: 8px;
}
.bootstrap__select__container .dropdown-toggle:focus {
	box-shadow: inset 0px 0px 3px 0px #4e4e4e !important;
	outline: none !important;
}
.bootstrap-select.show .btn.dropdown-toggle::after {
	content: '\f0d8';
}
.btn.dropdown-toggle::after {
	font-family: 'FontAwesome';
	content: '\f0d7';
	display: inline-block;
	vertical-align: middle;
	/* font-weight: 500; */
	position: relative;
	left: 0px;
	border: none;
	top: 1px;
}
.bootstrap-select.form-control-sm .dropdown-toggle {
	padding: 0.15rem 0.5rem 0.25rem 0.5rem;
	outline: none !important;
	height: 27px;
}
.bootstrap-select .dropdown-toggle:focus {
	outline: none !important;
}
/* custom select css */
.code_submision {
	padding: 0;
}
.CodeMirror {
	font-size: 15px;
	height: calc(100vh - 99px);
}
.attempted_code_area {
	max-height: calc(100vh - 153px);
	overflow: auto;
}
.fullscreenEditorHeight {
	height: calc(100vh - 40px) !important;
	max-height: calc(100vh - 40px) !important;
}
.CodeMirror-gutters {
	border: none;
	background-color: #f7f7f7;
}
.CodeMirror-linenumber.CodeMirror-gutter-elt {
	color: #36363d;
}
.attempted_code_here {
	margin-bottom: 0px;
}
.bottom-menu-container {
	/* background-color: red; */
	height: 60px;
	width: 100%;
	margin-left: 0px;
	display: grid;
	grid-template-columns: 0.1fr 1fr 1fr;
	border-top: 1px solid #d1d1d1;
	position: absolute;
	bottom: 0;
	background-color: white;
	z-index: 1;
}
.open-test-case-container {
	display: flex;
	align-items: center;
	max-width: max-content;
	margin-left: 5px;
	height: 30px;
	margin: 15px;
}
.btn-console-menu {
	margin-left: 2%;
	/* margin-top: 10px; */
	border: 1px solid #9d9d9d;
	border-radius: 5px;
	opacity: 1;
	height: 30px;
	background: #ffffff;
	outline: none;
	color: #818181;
	font-family: 'Hind', sans-serif;
	font-weight: 500;
	padding-left: 10px;
	padding-right: 25px;
	font-size: 13px;
	padding-top: 3px;
}
.open-test-case-container i {
	position: relative;
	right: 20px;
	color: #b1b1b1;
}
.submit-btns-container {
	display: flex;
	align-items: center;
}
.submit-btns-container a {
	margin-left: auto;
}
.execute_button {
	margin-left: 20px;
	margin-right: 30px;
}
.menu-box-container {
	height: 300px;
	background-color: white;
	/* z-index: 10; */
	position: absolute;
	width: 100%;
	bottom: 60px;
}
.menu-box-header {
	background-color: var(--primary-light);
	height: 40px;
	position: relative;
}
.menu-box-header .scolling-menu {
	overflow-y: hidden;
	white-space: nowrap;
	padding-right: 48px;
	scroll-behavior: smooth;
}
.menu-box-header .scolling-menu::-webkit-scrollbar {
	display: none;
}
.close-console-box {
	position: absolute;
	top: 10px;
	z-index: 1;
	right: 0px;
	background: var(--primary-light);
	height: 23px;
	padding: 0px 6px;
	padding-top: 5px;
	padding-right: 10px;
	color: #b1b1b1;
}
.close-console-box:hover {
	padding: 0px 6px;
	padding-top: 5px;
	padding-right: 10px;
}
.console-scroll-icon-right {
	display: inline-block;
	transform: rotate(-90deg);
	position: absolute;
	right: 20px;
	top: 8px;
	background: var(--primary-light);
	visibility: hidden;
}
.console-scroll-icon-left {
	display: inline-block;
	transform: rotate(90deg);
	position: absolute;
	left: 0px;
	top: 10px;
	background: var(--primary-light);
	z-index: 1;
	visibility: hidden;
}
.console-scroll-icon-left img {
	width: 25px;
	height: 25px;
}
.console-scroll-icon-right img {
	width: 25px;
	height: 25px;
}
.menu-box-header-options {
	width: 120px;
	height: 30px;
	opacity: 1;
	position: relative;
	margin-left: 2%;
	margin-top: 10px;
	display: inline-block;
	font-size: 14px;
	cursor: pointer;
}
.menu-box-header-options.selected {
	background: white;
	border-top-right-radius: 6px;
    border-top-left-radius: 6px;
}
.menu-box-header-options span {
	color: #818181;
	text-align: center;
	display: block;
	margin-top: 5px;
	margin-right: 5px;
}
.menu-box-header-options.selected span {
	color: #333333;
	font-weight: 600;
}
.menu-selected-box {
	background-color: white;
	height: 259px;
	max-height: 259px;
	overflow: auto;
	width: 100%;
}
.cstomInputBox {
	border: 1px solid #e0e0e0;
	border-radius: 5px;
	width: 98%;
	height: 238px;
	resize: none;
}
.test-case-number {
	margin: auto;
	margin-top: 5px;
}
.test-case-number.selected {
	/* background: #ebebeb;
	border-radius: 5px; */
	padding: 0px 0px;
	/* margin-bottom: 15px; */
	margin-top: 0px;
}
.test-case-number.selected .result-img-container {
	padding-top: 5px;
}
.test-case-number .result-img-container {
	margin-bottom: 1px !important;
	cursor: default;
	padding: 0;
}
.test-case-number .result-img-container img {
	cursor: default;
}
.test-case-number.selected .col-9 {
	background: #ebebeb;
	margin-left: 10px;
	padding: 5px 10px;
	border-radius: 5px;
	padding-right: 10px !important;
	max-width: max-content;
}
.test-case-number .col-9 {
	margin-left: 10px;
	padding: 5px 10px;
	padding-top: 0;
}
p.passing_test_score{
	padding: 5px 0;
}
.span-correct {
	font-family: 'Hind', sans-serif;
	letter-spacing: 0px;
	opacity: 1;
	font-weight: 400;
}
.verticleDivider {
	width: 8px;
	float: left;
	background-color: #e7e7e7;
	opacity: 1;
	z-index: 0;
	cursor: col-resize;
	height: calc(100vh - 53px);
}
.v-ellipsis {
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: #b9b9b9;
	box-shadow: 0px 10px 0px #b9b9b9, 0px 20px 0px #b9b9b9;
}
#ghostbar {
	width: 8px;
	background-color: #000;
	opacity: 0.5;
	position: absolute;
	z-index: 2000;
}
.paramContainer {
	margin-bottom: 15px;
}
.paramContainer label{
	font-size: 15px;
	color: #818181;
	margin-bottom: 0.2rem;
}
/* web question */
.web-question-btn-container {
	position: absolute;
	bottom: 0;
	width: 60%;
	display: flex;
	justify-content: center;
	border-top: 1px solid #d1d1d1;
	padding: 8px;
	padding-left: 0px;
	padding-right: 10px;
	z-index: 10;
}
.btn-code-submit {
	float: right;
	margin-left: 2%;
	margin-right: 3%;
	margin-top: 10px;
	width: 80px;
	border: 1px solid var(--primary-color);
	background: var(--primary-color) 0% 0% no-repeat padding-box;
	border-radius: 5px;
	opacity: 1;
	border-radius: 5px;
	opacity: 1;
	color: #ffffff;
	height: 36px;
	outline: none;
	font-size: 15px;
}
.web-question-btn-container .btn-code-submit {
	margin-top: 5px;
	margin-bottom: 5px;
	margin-right: 3%;
	margin-left: auto;
	float: right;
	width: auto;
	border: 1px solid var(--primary-color);
	background: var(--primary-color) 0% 0% no-repeat padding-box;
	opacity: 1;
	border-radius: 4px;
	opacity: 1;
	color: #ffffff;
	height: 36px;
	outline: none;
	font-size: 15px;
	text-transform: lowercase;
}
.btn-code-submit:disabled,
.execute_button:disabled {
	cursor: not-allowed;
	opacity: 0.5;
}
.web-question-btn-container .execute_button {
	padding: 5px 35px;
}
.web-question-btn-container .btn-console-menu {
	margin-top: 10px;
}
.web-question-btn-container .fa {
	color: #b1b1b1;
	top: 18px;
	position: relative;
	right: 20px;
}
.web-question-btn-container .fa.fa-caret-up {
	top: 16px;
}
.web-components-container .CodeMirror {
	height: calc(100vh - 153px);
}
.web-components-container {
	height: calc(100vh - 115px);
	overflow: auto;
}
.web-test-case-container {
	position: absolute;
	bottom: 63px;
	z-index: 10;
	background: white;
	width: 60%;
	height: 250px;
	/* overflow: auto; */
	display: none;
}

.webcomponent-left-side {
	display: inline-block;
	width: 59%;
}
.web-components-container .tabs {
	height: 40px !important;
	padding: 10px 8px;
	margin-right: 0px;
	color: #818181;
	font-family: 'Hind', sans-serif;
}
.web-components-container .change-editor-code-lang {
	margin-bottom: 0px;
	background: #fafafa;
	margin: 0;
}
.preview-code-btn-container {
	display: flex;
	align-items: center;
	max-height: 40px;
	margin-left: auto;
}
.preview-web-btn {
	margin-top: 0px;
	height: 30px;
	width: 100px;
	padding-left: 10px;
	text-align: left;
}
.preview-code-btn-container img {
	height: 16px;
	width: 16px;
	position: relative;
	right: 28px;
	padding-right: 5px;
	transform: rotate(90deg);
	margin-top: 7px;
}
.webcomponent-right-side {
	display: inline-block;
	width: 38.5%;
	height: calc(100vh - 110px);
}
.browser-btn-container {
	height: 58px;
	border-bottom: 1px solid #ccc;
	box-shadow: 0px 1px 2px #00000029;
	display: flex;
	align-items: center;
}
.browser-reload-btn {
	width: 20px;
	height: 20px;
	margin-left: 10px;
}
.browser-reload-btn:hover {
	margin-left: 10px;
}
.browser-url-input {
	border: 1px solid #c1c1c1;
	background-color: #ffffff;
	border-radius: 5px;
	padding: 5px;
	margin-left: 20px;
	width: 80%;
	overflow: auto;
}
.web-browser-container {
	height: calc(100vh - 168px);
	border: 1px solid #ebebeb;
}
.webcomponent-divider {
	display: inline-block;
	height: calc(100vh - 110px);
	max-width: 1%;
	width: 8px;
	background-color: #e7e7e7;
	cursor: col-resize;
}
.webcomponent-divider .v-ellipsis {
	margin: auto;
	position: relative;
	top: 55%;
}
.web-test-case-text-container {
	background: var(--primary-light);
	padding: 0px 10px;
	padding-top: 5px;
}
.web-test-case-text-container h4 {
	color: #333333;
	font-weight: 500;
	font-family: 'Hind', sans-serif;
	font-size: 14px;
	margin-bottom: 0px;
	width: 100px;
	text-align: center;
	height: 38px;
	background: white;
	display: inline-flex;
	justify-content: center;
	align-items: center;
}
.web-test-case-text-container i {
	color: #b1b1b1;
	float: right;
	margin-right: 10px;
	margin-top: 10px;
}
.web-test-case-text-container i:hover {
	margin-right: 10px;
	margin-top: 10px;
}
.web-test-case {
	/* padding: 10px 0px; */
	font-family: 'Hind', sans-serif;
	font-weight: 500;
	color: var(--primary-color);
	font-size: 15px;
}
.web-test-case:hover {
	/* padding: 10px 0px; */
}
.transparent-overlay {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	z-index: 10;
}
/* web question */

.all-question-description-container {
	overflow: auto;
	height: calc(100vh - 53px);
	border-right: #dddddd 5px solid;
}
.span-choose-any {
	font-size: 20px;
	font-family: 'Hind', sans-serif;
	font-weight: 500;
	letter-spacing: 0px;
	color: #333333;
	opacity: 1;
	margin-top: 2%;
}
.span-choose-any div {
	width: 50%;
	margin-bottom: 10px;
	margin-top: 5px;
	border-bottom: 1px solid #e0e0e0;
}
.editTextBox {
	height: 80px !important;
	outline: none;
	border: 1px solid #d1d1d1;
	border-radius: 5px;
	opacity: 1;
	padding: 5px;
}
button#unselect_all {
	color: var(--primary-color);
	outline: none;
}
.label-choose-answer {
	position: relative;
	font-size: 15px;
	font-family: 'Hind', sans-serif !important;
	font-weight: 400;
	letter-spacing: 0px;
	color: #4d4d4d;
	opacity: 1;
	cursor: pointer;
	padding-left: 40px;
	padding-top: 5px;
	margin-left: 2%;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	margin-bottom: 0px;
}

.div-radio-btn-choose-answer input {
	position: absolute;
	opacity: 0 !important;
	cursor: pointer;
}

/* Create a custom radio button */
.choose-answer-checkmark {
	position: absolute;
	top: 5px;
	left: 10px;
	height: 20px;
	width: 20px;
	background-color: var(--primary-light);
	border-radius: 50%;
}

.div-radio-btn-choose-answer input:checked ~ .choose-answer-checkmark {
	background-color: var(--primary-light);
}

.choose-answer-checkmark:after {
	content: '';
	position: absolute;
	display: none;
}

.div-radio-btn-choose-answer input:checked ~ .choose-answer-checkmark:after {
	display: block;
}

.div-radio-btn-choose-answer .choose-answer-checkmark:after {
	top: 5px;
	left: 5px;
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background: var(--primary-color) 0% 0% no-repeat padding-box;
	opacity: 1;
}
.label-choose-answer {
	white-space: break-spaces;
	display: inline-flex;
	max-width: 87%;
}
.fa.fa-check {
	color: #3e9a16;
	position: absolute;
}
/* .overflowRunning{
    position: absolute;
    background: rgba(255,255,255,0.5);
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 11;
    display: none;
} */
.attempted_code_area {
	position: relative;
}
/* reduce test case scroll width */
.all-test-case-container::-webkit-scrollbar {
	width: 6px;
}
.disabled {
	opacity: 0.5;
}
.scrollll:hover {
	background: #8e8e8e transparent;
}
.scrollll::-webkit-scrollbar-thumb {
	background: none;
}
::-webkit-scrollbar-thumb:hover {
	background: #8e8e8e;
}
.correct_result{
	width: 16px;
	height: 16px;
}
.mcq-result-text{
	font-family: 'Hind', sans-serif;
    font-size: 13px;
    margin-left: 10px;
	font-weight: 500;
}
@media only screen and (max-width: 992px) {
	.span-correct {
		word-break: break-all;
	}
	.test_cases {
		padding: 0px 5px !important;
	}
	#testCaseCounts {
		padding: 0px !important;
	}
	#testCaseDescription {
		padding: 0 !important;
	}
	#quizContainer {
		overflow: auto;
	}
	.all-question-description-container {
		border-right: none;
		border-bottom: #dddddd 2px solid;
		height: calc(50vh - 66px);
	}
	ul.dashboard-left-nav.nav.nav-tabs.tabs-left li {
		display: block;
		width: 100%;
	}
	.bottom-nav-container {
		display: none;
	}
	.admin_code_bar {
		height: max-content !important;
	}
	#codeSide {
		max-height: 50vh;
		overflow: auto;
	}
	.test_cases_code {
		height: max-content !important;
	}
	#zoomCode {
		display: none;
	}
	#question-code-separator {
		display: none !important;
	}
	.coding-question-all-container {
		display: block !important;
	}
	.coding-question-description-container {
		width: 100%;
		max-height: calc(100vh - 122px);
		/* border-bottom: #dddddd 2px solid; */
	}
	.coding-question-code-attempt-area {
		width: 95%;
		margin-left: auto;
		max-height: unset !important;
		/* height: calc(50vh) !important; */
		height: calc(100vh - 122px) !important;
	}
	.attempted_code_area {
		height: unset !important;
	}
	.bottom-menu-container {
		z-index: 6;
		width: 100%;
		overflow: auto;
	}
	.open-test-case-container {
		max-width: max-content;
	}
	.webcomponent-divider {
		display: none !important;
	}
	.switch-view-mobile {
		font-family: 'Hind', sans-serif;
		position: absolute;
		bottom: 0;
		height: 50px;
		/* background: black; */
		width: 100%;
		box-shadow: 0px 0px 15px #00000029;
		display: flex !important;
	}
	.code-switch-btn {
		width: 49%;
		/* margin-top: 5%; */
		background: white;
		border: none;
		color: #8b8b8b;
		font-size: 20px;
	}
	.code-switch-btn.selected {
		color: rgb(222, 104, 52);
	}
	.codingQuestionContainer {
		overflow: hidden !important;
	}
	.webcomponent-left-side {
		height: calc(100vh - 122px);
		width: 100%;
	}
	.webcomponent-right-side {
		width: 100%;
	}
	.web-browser-container {
		height: calc(100vh - 210px);
	}
	.CodeMirror {
		height: calc(100vh - 293px);
	}
}
@media screen and (max-width: 1024px) and (min-width: 768px) and (orientation: landscape) {
	#iconsLeftBar {
		display: grid !important;
	}
}

.please-run {
	background: #f8f8f8;
	margin: 0;
	padding: 15px;
	border-radius: 5px;
}

/* radio btn style starts */
.container-radio {
	display: block;
	position: relative;
	padding-left: 35px;
	cursor: pointer;
	color: #818181;
	/* font-size: 20px; */
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	/* margin-top: -15px; */
	display: inline-block;
	/* margin-right: 20px; */
	font-family: 'Hind', sans-serif !important;
	font-weight: 500;
	font-size: 13px;
}

.container-radio input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
}
.checkmark {
	position: absolute;
	top: -1px;
	left: 0;
	height: 20px;
	width: 20px;
	background-color: white;
	border-radius: 3px;
	box-shadow: 0px 3px 6px #00000029;
}

.checkmark:after {
	content: '';
	position: absolute;
	display: none;
}

.container-radio input:checked ~ .checkmark:after {
	display: block;
}

.container-radio .checkmark:after {
	top: 4px;
	left: 4px;
	width: 12px;
	height: 12px;
	border-radius: 3px;
	background: #4b4b4b;
}
/* radio btn style ends */
.form-check label{
	color: #8d8d8d;
}

.solution-btn{
	background: white;
    border: 1px solid #9d9d9d;
    border-radius: 3px;
    color: #818181;
	outline: none;
}
.solution-btn:focus{
	outline: none;
}

#exampleModal .CodeMirror{
	height: 300px !important;
}

.noTestCase{
	font-family: 'Hind', sans-serif !important;
	background-color: #feeee5;
    padding: 0.75rem;
    border-radius: 5px;
    font-size: 13px;
    margin: 20px;
}