body {
	font-family: 'Hind', sans-serif;
	/* font-family: 'Montserrat', sans-serif; */
}
.full-page-container {
	height: calc(100vh - 53px);
	overflow: auto;
	position: relative;
	padding-bottom: 50px;
	/* padding-right: 5%; */
}

.save-btn {
	float: left;
	margin-left: 80px;
	outline: none;
	border: none;
	background: var(--primary-color);
	color: white;
	border-radius: 3px;
	padding: 9px 24px;
	font-size: 15px;
}
.cancel-btn{
	color: var(--primary-color);
    cursor: pointer;
    margin-top: 10px;
    display: inline-block;
    width: max-content;
    margin-left: 20px;
	text-decoration: none !important;
}
.cancel-btn:hover{
	color: var(--primary-color);
}
.add-question-text {
	font-family: 'Hind', sans-serif;
	color: var(--primary-color);
	font-weight: 500;
	/* padding: 30px 40px 20px 80px; */
}
.dropdowns-container {
	padding: 0px 40px 20px 80px;
}
/* bootstrap button */
.bootstrap-select.show .btn.dropdown-toggle::after {
	content: '\f0d8';
	color: #9b9b9b;
	top: -1px;
}
.progLang div.dropdown-menu {
	transform: translate3d(0px, 36px, 0px) !important;
}
.bootstrap-select div.dropdown-menu.show {
	/* transform: translate3d(0px, -217px, 0px) !important; */
    margin-top: 8px;
    border: none !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    border-radius: 3px !important;
    box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
}
.bootstrap-select div.dropdown-menu.show .inner.show {
	overflow: auto !important;
	min-height: auto !important;
	max-height: 250px !important;
}
.btn.dropdown-toggle::after {
	font-family: 'FontAwesome';
	content: '\f0d7';
	display: inline-block;
	vertical-align: middle;
	/* font-weight: 500; */
	position: relative;
	left: 0px;
	border: none;
	/* top: 1px; */
	color: #9b9b9b;
}
.btn.dropdown-toggle {
	background: white;
	border: 1px solid #d1d1d1;
	border-radius: 5px;
	outline: none !important;
}
.bootstrap-select .dropdown-toggle:focus {
	outline: none !important;
}
.btn-light:not(:disabled):not(.disabled).active,
.btn-light:not(:disabled):not(.disabled):active,
.show > .btn-light.dropdown-toggle {
	background-color: white !important;
}
.dropdown-item{
	padding: 8px 12px !important;
    background-color: transparent;
    border: 0;
    font-family: 'Hind',sans-serif !important;
    font-size: 16px;
    color: #212529 !important;
}
.dropdown-item.active,
.dropdown-item:active {
	background-color: #0000001a !important;
	color: #525252 !important;
}
.dropdown-item:focus, .dropdown-item:hover{
	background-color: #f8f9fa;
}
.bootstrap-select .dropdown-menu li a {
	outline: none;
}
.bootstrap-select .dropdown-toggle .filter-option-inner-inner {
    font-family: 'Hind', sans-serif !important;
    font-weight: 500;
    color: #212529 !important;
    font-size: 15px;
}
/* bootstrap button */
.dropdown-btns {
	/* display: grid;
    grid-template-columns: 1fr 1fr 1fr; */
	max-width: 100%;
	/* gap: 20px; */
}
.dropdown-btns > div {
	margin-bottom: 20px;
}
.dropdown.bootstrap-select {
	width: 100% !important;
	border: none;
	box-shadow: none;
}
.btn.btn-default.bootstrap-touchspin-up {
	border: 1px solid #d9d9d9;
	border-radius: 0;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}
.btn.btn-default.bootstrap-touchspin-down {
	border: 1px solid #d9d9d9;
	border-radius: 0;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}

/* tag editor */
.tag-editor {
	border: 1px solid #d1d1d1;
	border-radius: 4px;
	padding: 2px 5px;
    min-height: 37px !important;
    max-height: 70px !important;
    overflow: auto;
}
.tag-editor .placeholder {
	padding: 2px 8px;
}
.tag-editor .tag-editor-tag {
	border-radius: 4px 0 0 4px;
	display: inline-block;
	max-width: 96%;
	cursor: default !important;
}
.tag-editor-tag,
.tag-editor-delete {
	font-family: 'Hind', sans-serif !important;
	color: white !important;
	background: var(--primary-dark) !important;
	border-radius: 5px;
	padding: 2px 5px 2px 5px !important;
}
.tag-editor .tag-editor-delete {
	border-radius: 0 4px 4px 0;
}
.tag-editor li{
	width: max-content;
}
.tag-editor li:hover {
	margin: 3px 0;
}
.tag-editor .tag-editor-delete i:before {
	color: white !important;
}
.tag-editor .tag-editor-delete:hover i:before{
	color: white !important;
}

.keywords-section .ui-autocomplete{
	display: flex;
	flex-direction: column;
}
/* tag editor */

/* quill editor */
.ql-toolbar.ql-snow {
	border: 1px solid #e8e8e8;
	/* border-bottom: none; */
	border-top-right-radius: 10px;
    border-top-left-radius: 10px;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow{
	border-bottom-right-radius: 10px;
	border-bottom-left-radius: 10px;
}
.ql-container.ql-snow {
	border: 1px solid #e8e8e8;
}
/* quill editor */

.edit-question-name-container {
	padding: 0px 40px 20px 80px;
}
.edit-question-name {
	display: inline-block;
	width: 100%;
}
.edit-question-name p {
	font-weight: 500;
	color: #333333;
	font-size: 25px;
	margin: 0;
}
.edit-question-name input {
	/* border: none; */
	width: 100%;
	font-weight: 500;
	color: #333333;
	font-size: 25px;
	outline: none;
	border: none;
	border-bottom: 1px solid #e0e0e0;
}
.edit-question-name input::placeholder {
	font-weight: 500;
	/* color: #333333; */
	font-size: 25px;
}
.edit-question-name-container img {
	width: 20px;
	height: 20px;
	margin-left: 15px;
}
.question-description-container {
	padding: 0px 40px 20px 80px;
}
/* radio btn style starts */
.container-radio {
	display: block;
	position: relative;
	padding-left: 28px !important;
	cursor: pointer;
	font-size: 14px;
	color: #818181;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.container-radio input {
	position: absolute;
	right: 30px;
    top: 4px;
	opacity: 0;
	cursor: pointer;
}
.checkmark {
	position: absolute;
	top: 1px !important;
	left: 0;
	height: 20px;
	width: 20px;
	background-color: white;
	border-radius: 3px;
	border: 1px solid #C7C7C7;
}
.radio-btnn .checkmark {
	border-radius: 50%;
}

.checkmark:after {
	content: '';
	position: absolute;
	display: none;
}

.container-radio input:checked ~ .checkmark:after {
	display: block;
}

.container-radio .checkmark:after {
	top: 4px !important;
	left: 4px;
	width: 10px;
	height: 10px;
	border-radius: 3px;
    background: #4b4b4b;
}
.radio-btnn.container-radio .checkmark:after {
	border-radius: 50% !important;
}
/* radio btn style ends */
.btn-secondary-default{
	color: var(--primary-color);
	background-color: white;
	border: 0;
	outline: 0;
	box-shadow: none !important;
}
.btn-secondary-default:hover{
	color: var(--primary-color);
}
#txtQuesTitle::placeholder{
	color: #d1d1d1;
}
#txtQuesTitle:-ms-input-placeholder{
	color: #d1d1d1;
}
#txtQuesTitle::-ms-input-placeholder{
	color: #d1d1d1;
}
.inputNum::placeholder {
	color: #d1d1d1;
}
.form-control:focus {
	border-color: #ced4da;
}
.mcq-section-container {
	padding: 20px 40px 40px 80px;
}
.answer-choice-text {
	color: var(--primary-color);
	font-weight: 500;
	font-size: 23px;
	width: max-content;
}
.answer-choice-text::after {
	height: 1px;
	display: block;
	width: 130%;
	background: #e0e0e0;
	border-right: 1px white;
	content: '';
}
.correct-answer-text {
	font-size: 14px;
	color: #818181;
	font-weight: 500;
	width: 75%;
	text-align: right;
	display: block;
}
.mcq-option {
	width: 80%;
	padding: 10px 5px 5px 5px;
	border-radius: 4px;
	transition: 0.2s ease-in-out;
}
.mcq-option:hover {
	background: #f2f2f2;
}
.mcq-option img {
	width: 20px;
	height: 20px;
	margin-top: 13px;
}
.mcq-option img:hover {
	margin-top: 13px;
}
.mcq-option .index-number {
	text-align: center;
	padding-top: 5px;
	color: #818181;
	font-size: 15px;
}
.mcq-option .input-box textarea {
	width: 100%;
	border: 1px solid #e0e0e0;
	outline: none;
	resize: none;
	padding: 8px;
	height: 42px;
}
.mcq-option .correct-answer .checkmark {
	margin: auto;
	right: 0;
	left: 0;
	top: 14px !important;
}

.code-option .correct-answer .checkmark{
	margin: auto;
	right: 0;
	left: 0;
	top: 28px !important;
}

.code-option {
	width: 100%;
	padding: 12px 5px 5px 5px;
	border-radius: 4px;
	transition: 0.2s ease-in-out;
}
.code-option:hover {
	background: #f2f2f2;
}
.code-option img {
	width: 20px;
	height: 20px;
	margin-top: 13px;
}
.code-option img:hover {
	margin-top: 13px;
}
.code-option .index-number {
	text-align: center;
	padding-top: 5px;
	color: #818181;
	font-size: 15px;
}
.code-option .input-box textarea {
	width: 100%;
	border: 1px solid #e0e0e0;
	outline: none;
	resize: none;
	padding: 8px;
}
.code-option .input-box input {
	width: 100%;
	height: 60px;
	border: 1px solid #e0e0e0;
	outline: none;
	resize: none;
	padding: 10px;
	margin-top: 10px;
	text-align: center;
}
.code-option .input-box input[type='number']::-webkit-inner-spin-button,
.code-option .input-box input[type='number']::-webkit-outer-spin-button {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	margin: 0;
}
.code-option .correct-answer .checkmark {
	margin: auto;
	right: 0;
	left: 0;
	top: 14px;
}
.add-more-choice {
	color: var(--primary-color);
	font-size: 15px;
	font-weight: 500;
	margin-top: 20px;
	display: block;
	margin-left: 7%;
	cursor: pointer;
	width: max-content;
}
.refrence-file-container {
	padding: 0px 40px 0px 80px;
}
.coding-section-container {
	padding: 10px 40px 0px 80px;
}
#uploadDiv {
	margin-left: 15px;
}
.web-question-section-container {
	padding: 20px 40px 20px 80px;
}
/* .change-lang-container .bootstrap-select > .dropdown-toggle {
	outline: none !important;
	background: var(--primary-lighter) !important;
	border: none;
	border-radius: 2px;
	box-shadow: none !important;
}
.change-lang-container .btn.dropdown-toggle::after {
	color: var(--primary-color);
} */
.selected-langs-container {
	background-color: #fafafa;
	width: 100%;
	padding-left: calc(28px + 0.7em);
}
.selected-langs-container p {
	display: inline-block;
	margin-bottom: 0;
	font-size: 15px;
	padding: 8px 10px 5px 10px;
	cursor: pointer;
}
.selected-langs-container p.selected {
	background-color: #efefef;
	font-weight: 600;
	transition: 0.2s ease-in-out;
}
.code-part-container {
	background-color: var(--primary-lighter);
	width: 100%;
	padding-left: calc(28px + 0.7em);
}
.code-part-container p {
	display: inline-block;
	margin-bottom: 0;
	font-size: 15px;
	padding: 8px 10px 5px 10px;
	cursor: pointer;
}
.code-part-container p.selected {
	background-color: var(--primary-medium);
	color: #ffffff;
}

.code-part-web-container {
	background-color: var(--primary-lighter);
	width: 100%;
	padding-left: calc(28px + 0.7em);
}
.code-part-web-container p {
	display: inline-block;
	margin-bottom: 0;
	font-size: 15px;
	padding: 8px 10px 5px 10px;
	cursor: pointer;
}
.code-part-web-container p.selected {
	background-color: var(--primary-medium);
	color: #ffffff;
}
.CodeMirror {
	box-shadow: 0px 1px 3px #00000029;
}
.CodeMirror-gutter {
	background: #efefef;
}
.CodeMirror-foldgutter {
	background: #efefef;
}

.solTailContainer .CodeMirror{
	box-shadow: none !important;
}

.run-button{
	color: white !important;
    text-decoration: none;
    background-color: var(--primary-color) !important;
    font-weight: 400;
    font-size: 17px;
	margin-left: 2px;
	cursor: pointer !important;
    border-radius: 0px !important;
}

.subjective-section-container,
.project-section-container {
	padding: 20px 40px 20px 80px;
}

.tabs {
	background: #efefef;
	padding: 3px 8px;
	height: 30px !important;
	margin-right: 5px;
	cursor: pointer;
}
.question-tabs-container {
	padding: 30px 10px 0px 85px;
	border-bottom: 1px solid #ededed;
	margin-bottom: 20px;
}
.page-tabs-container .tabs-2 {
	display: inline-block;
	margin-right: 10px;
	/* font-family: 'Avenir'; */
	font-size: 17px;
	padding-bottom: 5px;
}
.page-tabs-container .tabs-2::after{
	/* content: "";
    margin-left: 18px;
	border-right: 1px solid #212529; */
}
.segmentDivider{
	display: inline-block;
    width: 2px;
    height: 20px;
    background: #212529;
    position: relative;
    top: 6px;
    margin-right: 5px;
}
.page-tabs-container .tabs-2 a {
	text-decoration: none;
	color: inherit;
}
.page-tabs-container .tabs-2.active {
	border-bottom: 4px solid var(--primary-color);
}
.testCase-container {
	display: grid;
	grid-template-columns: 1fr;
}
.testcase-table-container table {
	width: max-content;
	margin: auto;
}
.add-test-case-btn {
	float: unset;
	max-width: max-content;
	text-decoration: none !important;
	margin: auto;
	display: block;
	margin-bottom: 10px;
	cursor: pointer;
}
.add-test-case-btn:hover {
	color: white;
}
.testcase-table-container td,
.testcase-table-container th {
	border: none;
	border-bottom: none !important;
}
.table thead th {
	font-family: 'Montserrat', sans-serif !important;
	font-size: 15px;
	font-weight: 600;
}
.sr-no-col {
	width: 20px;
}
.test-case-col {
	width: 120px;
}
.score-col {
	width: 90px;
}
.actions-col span {
	color: var(--primary-color);
	padding: 5px;
	margin: 0px 6px;
}
.actions-col span:hover {
	padding: 5px;
	margin: 0px 6px;
}
.actions-col .fa-pencil {
	color: var(--primary-color);
}

.testcase-table-container tr td:nth-child(4),.testcase-table-container tr th:nth-child(4){
	text-align: center;
}


.save-testcaseBtn {
	background: var(--primary-color);
	border: var(--primary-color);
}
.save-testcaseBtn:hover {
	background: var(--primary-color) !important;
	border: var(--primary-color);
}
.save-testcaseBtn:focus {
	background: var(--primary-color) !important;
}
#testCaseScore {
	border: 1px solid #e0e0e0;
}
#testcaseInput,
#testcaseOutput {
	border: 1px solid #e0e0e0;
	outline: none;
	resize: none;
	border-radius: 4px;
	padding: 7px;
}
.modal-body label {
	color: #818181;
	width: 227px;
}
.th-border::before {
	content: '';
	display: block;
	width: 80%;
	border-bottom: 1px solid #dedede;
	position: relative;
	top: 25px;
}
.th-border.th-border-2::before {
	width: 100%;
}
.th-border-3::before{
	content: '';
	display: block;
	width: 80%;
	border-bottom: 1px solid #dedede;
	position: relative;
	top: 25px;
	left: 6px;
}
.upload-btn {
	background: var(--primary-color);
	border: 1px solid var(--primary-color);
	border-radius: 5px;
	color: white;
	padding: 5px 20px;
	cursor: pointer;
}
.totalFilesSelected {
	margin-left: 10px;
	color: var(--primary-color);
	font-weight: 600;
}
#preview {
	border: 1px solid var(--primary-color);
	color: var(--primary-color);
	background-color: transparent;
	margin-top: 13px;
	text-transform: lowercase;
}
#preview:hover {
	background-color: var(--primary-color);
	color: white;
	transition: 0.5s;
}
.tag-editor {
	/* width: max-content !important; */
	/* max-width: 550px !important; */
}
.close span {
	color: #aaaaaa;
	font-size: 24px;
	font-weight: 400;
	cursor: pointer;
	height: 30px;
	margin-top: -10px;
	margin-left: auto;
	width: 15px;
}
.close span:hover {
	color: #000;
	text-decoration: none;
	cursor: pointer;
}
[data-customTooltip] {
    position: relative;
}

[data-customTooltip]::after {
	text-transform: lowercase;
    background-color: black;
    color: #fff;
    font-size: 12px;
    padding: 2px 10px;
    height: fit-content;
    width: fit-content;
    border-radius: 3px;
    position: absolute;
    text-align: center;
    left: 50%;
    content: attr(data-customTooltip);
    transform-origin: top;
    transition: 0.2s;
    z-index: 1000;
    white-space: nowrap;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, .2), 0 0 0 1px rgba(0, 0, 0, .05);
}

.tooltip-down[data-customTooltip]::after {
    bottom: -42px;
    transform: translate(-50%, 0%) scale(0);
}

.tooltip-up[data-customTooltip]::after {
    transform: translate(-50%, 100%) scale(0);
    top: -22px;
}


[data-customTooltip]::before {
    content: " ";
    position: absolute;
    transform: translate(-50%, 0%) scale(0);
    transition: 0.2s;
    left: 50%;
    z-index: 1000;
}

.tooltip-up[data-customTooltip]::before {
    top: -5px;
    border-top: 11px solid #222;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
}

.tooltip-down[data-customTooltip]::before {
    bottom: -25px;
    border-bottom: 11px solid #222;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
}

[data-customTooltip]:hover:after {
    display: block;
    transform: translate(-50%, 0%) scale(1);
}

[data-customTooltip]:hover::before {
    display: block;
    transform: translate(-50%, 0%) scale(1);
}

.CodeMirror-sizer{
	padding-bottom: 5px !important;
}


/* solution tab */
.solution-part-container {
	background-color: var(--primary-lighter);
	width: 100%;
	padding-left: calc(28px + 0.7em);
}
.solution-part-container p {
	display: inline-block;
	margin-bottom: 0;
	font-size: 15px;
	padding: 8px 10px 5px 10px;
	cursor: default;
}
.solution-part-container p.selected {
	background-color: var(--primary-medium);
	color: #ffffff;
}

.selected-solution-langs-container {
	background-color: #fafafa;
	width: 100%;
	padding-left: calc(28px + 0.7em);
}
.selected-solution-langs-container p {
	display: inline-block;
	margin-bottom: 0;
	font-size: 15px;
	padding: 8px 10px 5px 10px;
	cursor: pointer;
}
.selected-solution-langs-container p.selected {
	background-color: #efefef;
	font-weight: 600;
	transition: 0.2s ease-in-out;
}
.test-case-result{
	display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    text-align: center;
    margin-top: 20px;
    font-family: 'Hind', sans-serif;
    letter-spacing: 0px;
    opacity: 1;
    font-weight: 400;
    color: var(--primary-color);
	margin-bottom: 10px;
}
.test-case-result p{
	width: max-content;
	margin-left: 2px;
	cursor: pointer;
	margin-bottom: 18px;
}
.test-case-result img{
	width: 13px;
    height: 13px;
    margin-left: 5px;
}
.test-case-result textarea{
	width: 300%;
    height: 100px;
    background: black;
    resize: none;
    color: #fff;
    border-radius: 3px;
	overflow: auto;
}
#testCreateContainer .bootstrap-select div.dropdown-menu.show{
	top: 0px !important;
}

.solHeadContainer .CodeMirror, .solTailContainer .CodeMirror{
	height: unset;
	/* max-height: 150px; */
	background: rgb(247, 247, 247);
}

.solHeadContainer .CodeMirror-linenumber, .solTailContainer .CodeMirror-linenumber{
	visibility: hidden;
}

/* switch */
.switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 25px;
}
.switch input {
	display: none;
}
.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: 0.4s;
	transition: 0.4s;
	max-width: 52px;
}
.slider:before {
	position: absolute;
	content: '';
	height: 14px;
	width: 14px;
	left: 4px;
	bottom: 6px;
	background-color: #fff;
	-webkit-transition: 0.4s;
	transition: 0.4s;
}
input:checked + .slider {
	/* background-color: #2196f3; */
}
input:focus + .slider {
	/* box-shadow: 0 0 1px #	2196f3; */
}
input:checked + .slider:before {
	-webkit-transform: translateX(26px);
	-ms-transform: translateX(26px);
	transform: translateX(26px);
}
.slider.round {
	border-radius: 34px;
}
.slider.round:before {
	border-radius: 50%;
}

.slider {
	background-color: #e5e5e5;
}

input:checked + .slider {
	background-color: var(--slider-bg);
}
input:checked + .slider:before {
	background-color: var(--primary-dark);
}
.span-slider{
	color: #6F6F6F;
    font-size: 15px;
	font-weight: 500;
    position: relative;
    top: -16px;
	font-family: 'Avenir',sans-serif;
}