/* import fonts */
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Black.ttf')
		format('truetype');
	font-weight: 900;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Heavy.ttf')
		format('truetype');
	font-weight: 700;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Medium.ttf')
		format('truetype');
	font-weight: 500;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir Book';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Book.ttf') format('truetype');
	font-weight: 500;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir Book';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-BookOblique.ttf')
		format('truetype');
	font-weight: 400;
	font-style: italic;
}
/* import fonts */
.text-orange {
	color: var(--primary-color) !important;
}

.filterIcon {
	height: -webkit-fit-content;
	height: -moz-fit-content;
	height: fit-content;
	border-radius: 4px;
	padding: 8px 5px;
	margin-top: 4px;
}
.filterIcon.active{
	background-color: #dedede;
}

#filteredEmails::-webkit-input-placeholder {
    font-family: 'Hind', sans-serif;
    font-size: 15px;
    color: #d9d9d9;
    padding-top: 10px;
    padding-bottom: 10px;
}

#filteredEmails::-moz-placeholder {
    font-family: 'Hind', sans-serif;
    font-size: 15px;
    color: #d9d9d9;
    padding-top: 10px;
    padding-bottom: 10px;
}

#filteredEmails:-ms-input-placeholder {
    font-family: 'Hind', sans-serif;
    font-size: 15px;
    color: #d9d9d9;
    padding-top: 10px;
    padding-bottom: 10px;
}

#filteredEmails::-ms-input-placeholder {
    font-family: 'Hind', sans-serif;
    font-size: 15px;
    color: #d9d9d9;
    padding-top: 10px;
    padding-bottom: 10px;
}

#filteredEmails::placeholder {
    font-family: 'Hind', sans-serif;
    font-size: 15px;
    color: #d9d9d9;
    padding-top: 10px;
    padding-bottom: 10px;
}

.search-multiple .tag-editor {
    min-height: 40px;
    max-height: 40px;
    overflow: auto;
    width: 100%;
    outline: none !important;
    border-radius: 0.25rem;
    padding-top: 9px;
    -webkit-box-shadow: inset 0px 0px 2px 0px #00000029 !important;
            box-shadow: inset 0px 0px 2px 0px #00000029 !important;
    border: 1px solid #D1D1D1;
	margin-left: 15px;
	padding-right: 63px;
}

.search-multiple .tag-editor li:first-child{
	height: 0px !important;
}

.search-multiple .tag-editor .tag-editor-tag, .search-multiple .tag-editor .tag-editor-delete {
    color: #696969;
    font-size: 16px;
    background: transparent;
    font-family: 'Hind', sans-serif;
}

.search-multiple .tag-editor .tag-editor-tag {
	cursor: default !important;
}

.search-multiple .tag-editor li{
	margin: 0px;
}

.search-multiple .tag-editor .tag-editor-delete i {
	line-height: 9px;
}

.search-multiple .tag-editor .placeholder {
	white-space: nowrap;
}

.search-multiple .tag-editor .tag-editor-delete {
    padding: 0;
    margin: 0;
}

body {
	font-family: 'Avenir';
}
.common-quiz-header-container {
	padding: 30px 98px 0px 98px;
	border-bottom: 1px solid #ededed;
}
.all-tests {
	font-family: 'Avenir';
	font-weight: 700;
	font-size: 17px;
	color: #000000;
	margin-bottom: 0;
}
.all-tests a{
	font-family: 'Avenir';
	font-weight: 400;
	font-size: 17px;
	color: #000000;
	text-decoration: none;
}
.all-tests i {
	color: var(--primary-color);
	position: relative;
	left: 5px;
	top: 2px;
	font-size: 20px;
}
.quiz-name-options-container {
	display: -ms-grid;
	display: grid;
	-ms-grid-columns: 1fr 10px 1fr;
	grid-template-columns: 1fr 1fr;
	gap: 10px;
}
.quiz-options-container {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	text-align: right;
}
.quiz-options-container button {
	white-space: nowrap;
}
.quiz-options-container button.more {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 0.5rem;
}
.quiz-options-container button.more .bi {
	padding-top: 0.25em;
	font-size: small;
}
.quiz-options-container button.more.expanded .bi:nth-of-type(1),
.quiz-options-container button.more:not(.expanded) .bi:nth-of-type(2) {
	display: none;
}
.quiz-options-container .dropdown-buttons {
	list-style: none;
	position: absolute;
	top: 120%;
	right: 0.5rem;
	padding: 0.5rem 1px;
	width: auto;
	min-width: 200px;
	border-radius: 0.5rem;
	background-color: #fff;
	box-shadow: 0px 0px 1rem #0003;
	z-index: 11;
}
.quiz-options-container .dropdown-buttons li {
	width: 100%;
}
.quiz-options-container .dropdown-buttons li :is(button, a) {
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: 1em;
	padding: 0.4em 1em;
	width: 100%;
	font-size: 1rem;
	font-weight: 400;
	text-align: left;
	text-transform: capitalize;
	white-space: nowrap;
	color: #222;
	background-color: transparent;
	border: unset;
}
.quiz-options-container .dropdown-buttons li :is(button, a):hover {
	text-decoration: none;
	background-color: #f4f4f4;
}

.quiz-name {
	color: var(--primary-color);
	font-family: 'Avenir';
	font-weight: 500;
	font-size: 29px;
	margin-bottom: 5px;
	max-width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.btn-1 {
	border: none;
	border-radius: 3px;
	padding: 5px 20px;
	background: var(--primary-color);
	color: white;
	font-family: 'Avenir';
	font-weight: 500;
	margin-right: 10px;
	cursor: pointer;
}
a.btn-1 {
	text-decoration: none;
}
.btn-2 {
	border: none;
	border-radius: 3px;
	padding: 5px 20px;
	background: var(--primary-light);
	color: var(--primary-color);
	font-family: 'Avenir';
	font-weight: 500;
	margin-right: 10px;
}
.page-tabs-container{
	position: relative;
}
.tabs-slider, .allowed-email-button,
.tabs-slider, .invited-email-button {
	position: relative;
	top: 0px;
	margin-left: auto;
	margin-right: 58px;
}
.allowed-email-button a,
.invited-email-button a {
	cursor: pointer;
	color: var(--primary-color);
	font-weight: 500;
	white-space: nowrap;
}
.page-tabs-container .tabs {
	display: inline-block;
	margin-right: 10px;
	font-family: 'Avenir';
	font-size: 17px;
	padding-bottom: 5px;
}
.page-tabs-container .tabs a{
	text-decoration: none;
	color: inherit;
}
.page-tabs-container .tabs.active {
	border-bottom: 4px solid var(--primary-color);
}
.reports-page-container {
	/* padding: 25px 50px 0px 85px; */
	min-height: 43vh;
	height: -webkit-max-content;
	height: -moz-max-content;
	height: max-content;
	/* padding: 25px 50px 0px 40px; */
	/* height: calc(100vh - 182px); */
	overflow: auto;
	max-height: calc(100vh - 182px);
	width: calc(100vw - 70px);
	display: inline-block;
}
.table-options-container{
    display: inline-block;
    vertical-align: top;
    width: 35px;
    /* padding-top: 84px; */
	padding-left: 8px;
}
.filter-container {
	width: 100%;
	padding-bottom: 5px;
	position: fixed;
    z-index: 10;
	padding: 25px 50px 10px 95px;
	background-color: white;
}
.filter-container img {
	width: 18px;
	height: 18px;
	display: inline-block;
}
.filter-container i{
	margin-right: 10px;
	color: var(--primary-color);
	padding: 7px;
	border-radius: 3px;
}
.filter-container i:hover{
	margin-right: 10px;
	padding: 7px;
}
.filter-container i.active{
	background-color: var(--primary-light);
}
.filter-container input {
	border: none;
	outline: none;
}
.table-container {
	margin-top: 50px;
	padding: 20px 50px 0px 40px;
}
.inviteListContainer{
	margin-top: 40px;
}

/* datatables css */

/* datatables checkbox */
table.dataTable th.select-checkbox:before,
table.dataTable tbody td.select-checkbox:before {
	width: 20px;
	height: 20px;
	border: 1px solid #c7c7c7;
	border-radius: 3px;
	-webkit-box-shadow: 0px 0px 4px #00000029;
	        box-shadow: 0px 0px 4px #00000029;
	background: white;
	cursor: pointer;
	content: ' ';
	display: block;
	position: absolute;
	left: -40px;
	margin-top: -6px;
	margin-left: -6px;
	top: 23px;
}
table.dataTable tr.selected td.select-checkbox:after,
table.dataTable th.select-checkbox.selected:after {
	text-align: center;
	text-shadow: 1px 1px #b0bed9, -1px -1px #b0bed9, 1px -1px #b0bed9,
		-1px 1px #b0bed9;
	left: -41px;
	width: 10px;
	height: 10px;
	border-radius: 3px;
	background: #4b4b4b;
	margin: 0 !important;
	content: '' !important;
	-webkit-box-shadow: 0px 0px 4px #00000029;
	        box-shadow: 0px 0px 4px #00000029;
	border: 1px solid #c7c7c7;
	cursor: pointer;
	display: block;
	position: absolute;
	top: 22px;
}
/* datatables checkbox */
.opacity-0-5{
	opacity: .5;
	cursor: default !important;
}
#tableList_wrapper{
	margin-left: 45px;
	/* margin-top: -50px; */
}
#tableList_wrapper .top {
	/* display: none; */
	position: fixed;
	right: 40px;
	z-index: 10;
	margin-top: -60px;
	display: none;
}
html{
	overflow-y: hidden;
}
#tableList_wrapper label {
    font-family: 'Hind', sans-serif !important;
    font-weight: 500 !important;
}
#tableList_wrapper input {
    border: none !important;
    border-bottom: 1px solid #dedede !important;
	outline: none !important;
}
/* #tableList_wrapper th.select-checkbox:before{
   top: 63px;
}
table.dataTable th.select-checkbox.selected:after{
	top: 62px;
} */
.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
	background: 0% 0% no-repeat padding-box padding-box rgb(250, 250, 250);
	-webkit-box-shadow: rgb(0 0 0 / 16%) 0px 1px 2px;
	        box-shadow: rgb(0 0 0 / 16%) 0px 1px 2px;
	border: 1px solid rgb(199, 199, 199);
	border-radius: 3px;
	color: rgb(51, 51, 51) !important;
}
.dataTables_wrapper .dataTables_paginate .paginate_button {
	margin: 0px;
	font-size: 14px;
	color: rgb(155, 155, 155) !important;
	padding: 2px 11px !important;
	padding-top: 4px !important;
}
#tableList_length label select{
	background-image: -o-linear-gradient(45deg, transparent 50%, gray 50%),
    -o-linear-gradient(315deg, gray 50%, transparent 50%),
    -o-linear-gradient(left, #ccc, #ccc);
	background-image: linear-gradient(45deg, transparent 50%, gray 50%),
    linear-gradient(135deg, gray 50%, transparent 50%),
    linear-gradient(to right, #ccc, #ccc);
    background-position:
    calc(100% - 12px) calc(1em + -2px),
    calc(100% - 7px) calc(1em + -2px),
    calc(100% - 2.5em) 0.5em;
  background-size:
    5px 5px,
    5px 5px,
    0px 1.5em;
  background-repeat: no-repeat;
  -webkit-appearance: none;
  padding-left: 7px !important;
}
.dataTables_length::before {
	content: 'Entries';
	position: relative;
	left: -10px;
	top: 0px;
	color: rgb(141, 141, 141);
	pointer-events: none;
	z-index: 10;
}
.dataTables_length select {
	cursor: pointer;
	font-weight: 400;
	color: rgb(141, 141, 141);
	border-radius: 3px !important;
	-webkit-box-shadow: rgb(0 0 0 / 16%) 0px 0px 3px 0px inset;
	        box-shadow: rgb(0 0 0 / 16%) 0px 0px 3px 0px inset;
	border: 1px solid rgb(209, 209, 209) !important;
	height: 30px;
	outline: none;
	width: 55px;
	padding: 4px !important;
	margin-right: 70px;
	display: none !important;
}
.bottom {
	width: 95%;
	margin: auto;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	    -ms-flex-pack: center;
	justify-content: center;
	font-size: 14px;
	font-family: 'Hind', sans-serif;
	/* position: absolute; */
}
table.dataTable thead th,
table.dataTable thead td {
	border-bottom: none;
}
table.dataTable thead th {
	padding: 8px 10px;
	font-family: 'Montserrat', sans-serif;
	font-weight: 600;
	font-size: 15px;
	cursor: default;
}

table.dataTable tbody td {
	padding: 15px 20px;
	vertical-align: middle;
	word-break: break-all;
	font-family: 'Hind', sans-serif;
	font-size: 15px;
	padding-top: 12px !important;
	padding-bottom:10px !important;

}
table.dataTable tbody td .ellipsis-line {
	max-width: 100%;
}
#tblCCLst-2 td:first-child, #tblCCLst-3 td:first-child {
	padding-top: 15px !important;
}
#tblCCLst tr td{
	vertical-align: middle;
}
table.dataTable tbody td:first-child{
	padding-top: 5px !important;
}
table.dataTable tbody > tr.selected,
table.dataTable tbody > tr > .selected {
	background-color: #f2f2f2;
}
table.dataTable tbody > tr.selected.removeSelected,
table.dataTable tbody > tr > .selected.removeSelected {
	background-color: white !important;
}
table.dataTable.no-footer {
	border: none;
}

/* sorting */
table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc {
	/* background: none; */
	cursor: default !important;
	background-image: none !important;
}

table.dataTable thead .sorting span,
table.dataTable thead .sorting_asc span,
table.dataTable thead .sorting_desc span {
  cursor: pointer !important;
}

table.dataTable thead .sorting_asc span:after {
	font-family: 'FontAwesome';
	content: ' \f0d7 ' !important;
	top: 2px;
	opacity: 0.5;
}
table.dataTable thead .sorting_desc span:after {
	font-family: 'FontAwesome';
	content: '\f0d8 ' !important;
	top: 0px;
	opacity: 0.5;
	left: 3px;
	position: relative;
}
table.dataTable thead .sorting span:after {
	font-family: 'FontAwesome';
	content: '\f0dc';
	top: 0px;
	opacity: 0.5;
	position: relative;
	left: 5px;
}
/* sorting */

/* border */
.th-border::before {
	content: '';
	display: block;
	width: 80%;
	border-bottom: 1px solid #dedede;
	position: relative;
	top: 25px;
}
.th-border-new::after{
	content: '';
	display: block;
	width: 80%;
	border-bottom: 1px solid #dedede;
	position: relative;
	top: 3px;
}
/* border */
#tblCCLst{
	margin-top: 60px;
}

#tblCCLst thead tr th{
	white-space: nowrap !important;
}


#tblCCLst thead tr th:nth-child(3) {
	min-width: 15vw;
}
#tblCCLst thead tr th:nth-child(4) {
	min-width: 5vw;
}
#tblCCLst thead tr th:nth-child(5) {
	min-width: 5vw;
}
#tblCCLst thead tr th:nth-child(6) {
	min-width: 5vw;
}
#tblCCLst thead tr th:nth-child(7) {
	min-width: 5vw;
}

#tableList{
	border: 1px solid #dedede;
	margin-top: 10px !important;
}
#tableList thead th {
    position: sticky;
    top: 0;
    background-color: white;
    border-top: 1px solid #dedede !important;
    z-index: 1;
    -webkit-box-shadow: rgb(125 125 126 / 10%) 0px 1px 0px;
            box-shadow: rgb(125 125 126 / 10%) 0px 1px 0px;
	padding: 17px 10px !important;
}

#tableList tbody td {
    border-top: 1px solid #D8D8D8 !important;
    border-bottom: 1px solid #D8D8D8 !important;
    border-left: none !important;
    border-right: none !important;
    vertical-align: middle !important;
	border-radius: 0 !important;
}

#tableList tbody tr {
	border-radius: 5px;
}
#tableList tbody tr td:first-child {
	border-top-left-radius: 5px;
	border-bottom-left-radius: 5px;
}
#tableList tbody tr td:last-child {
	border-top-right-radius: 5px;
	border-bottom-right-radius: 5px;
}
/* datatables css */

.column-list-btn {
	display: block;
	text-align: start;
	cursor: pointer;
}
.column-list-btn i {
	margin: 0px 0px -0.3rem 0.8rem;
	padding: 0.4rem 0.5rem;
	color: var(--primary-color);
	border-radius: 0.25rem;
}
.column-list-btn i:hover {
	margin: 0px 0px -0.3rem 0.8rem;
	padding: 0.4rem 0.5rem;
}
.column-list-btn.active i {
	background: var(--primary-light);
}

.dropdown-list {
	width: 220px;
	background: #ffffff;
	border-radius: 5px;
	z-index: 1;
	position: absolute;
	right: 64px;
	top: 10px;
	-webkit-transition: 0.2s ease-in-out;
	-o-transition: 0.2s ease-in-out;
	transition: 0.2s ease-in-out;
	/* transform: scale(1); */
	padding: 10px;
	-webkit-box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
	        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
.user-options.dropdown-list .col-6{
	padding-left: 0;
	padding-right: 0px;
}
.user-options.dropdown-list .col-12{
	padding-left: 0;
	padding-right: 0px;
}
.dropdown-list img {
	width: 20px;
	height: 20px;
	display: block;
	margin: auto;
	margin-bottom: 8px;
	margin-top: 4px;
}
.dropdown-list img:hover {
	width: 20px;
	height: 20px;
	display: block;
	margin: auto;
	margin-bottom: 8px;
	margin-top: 4px;
}
.dropdown-list a {
	font-family: 'Hind', sans-serif !important;
	text-decoration: none !important;
	color: #848484;
	font-size: small;
	text-align: center;
	display: block;
	margin: auto;
	cursor: pointer;
}
.hover-focus {
	padding: 5px;
}
.hover-focus:hover {
	background-color: #f7f7f7;
	border-radius: 5px;
}
.wrap-words {
	word-break: break-word;
	white-space: normal;
}
.dots-btn {
	cursor: pointer;
	color: rgb(132, 132, 132);
	margin: auto;
	padding: 2px 10px 2px 7px;
	border-radius: 3px;
	position: relative;
	/* left: 20px; */
	top: -2px;
	width: 18px;
	background-color: white;
}

/* radio btn style starts */
.container-radio {
	font-family: 'Hind', sans-serif;
	color: #818181;
	display: block;
	position: relative;
	padding-left: 35px;
	cursor: pointer;
	font-size: 15px;
	font-weight: 400;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	/* margin-top: -15px; */
}
.container-radio input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
}
.checkmark {
	position: absolute;
	top: -1px;
	left: 0;
	height: 20px;
	width: 20px;
	background-color: white;
	border-radius: 3px;
	-webkit-box-shadow: 0px 0px 4px #00000029;
	        box-shadow: 0px 0px 4px #00000029;
	border: 1px solid #c7c7c7;
}
.radio-btnn .checkmark {
	border-radius: 50%;
}

.checkmark:after {
	content: '';
	position: absolute;
	display: none;
}

.container-radio input:checked ~ .checkmark:after {
	display: block;
}

.container-radio .checkmark:after {
	top: 4px;
	left: 4px;
	width: 10px;
	height: 10px;
	border-radius: 3px;
	background: #4b4b4b;
}
.radio-btnn.container-radio .checkmark:after {
	border-radius: 50% !important;
}
/* radio btn style ends */

.table-options-container {
	position: relative;
	top: 14px;
	right: 65px;
	padding: 0px;
	width: 240px;
	background-color: #fff;
	border-radius: 5px;
	box-shadow: 2px 2px 1rem #ccc;
	z-index: 4;
}

.columns-list-container-header {
	display: flex;
	justify-content: space-between;
	padding: 8px 15px;
	width: 100%;
	color: #666;
	border-bottom: 1px solid #ddd;
}
.columns-list-container-header {
	font-weight: 600;
}
.columns-list-container-header .close-button {
	font-weight: 500;
	cursor: pointer;
}
.columns-list-container-header .close-button img {
	width: 0.75em;
}

.columns-list-container .container-radio {
	margin-top: 10px;
}
.columns-list-container {
	width: 100%;
	height: 320px;
	overflow: auto;
	margin-top: 0px;
	z-index: 2;
	top: unset;
	padding: 0;
}
#tableList {
	border-spacing: 0 5px;
}

/* columns fixed width */
#tableList {
	width: 100% !important;
}
.user-uName {
	width: 150px !important;
	min-width: 150px;
	max-width: 150px;
	word-break: break-all;
}
td.user-uName{
    padding-left: 0px !important;
}
.user-email {
	min-width: 185px;
	max-width: 185px;
	width: 185px !important;
}
.user-sTime {
	min-width: 160px;
	max-width: 160px;
	width: 160px !important;
}
.user-status {
	min-width: 95px;
	max-width: 95px;
	width: 95px !important;
}
.user-eTime {
	min-width: 160px;
	max-width: 160px;
	width: 160px !important;
}
.user-timeTaken {
	min-width: 115px;
	max-width: 115px;
	width: 115px !important;
}
.user-score {
	min-width: 60px;
	max-width: 60px;
	width: 60px !important;
}
.user-rNo {
	min-width: 90px;
	max-width: 90px;
	width: 90px !important;
}
.user-tSwitch {
	min-width: 136px;
	max-width: 136px;
	width: 136px !important;
}
.user-cBlock{
	min-width: 136px;
	max-width: 136px;
	width: 136px !important;
}
.user-cPaste {
	min-width: 150px;
	max-width: 150px;
	width: 150px !important;
}
.user-lCount {
	min-width: 126px;
	max-width: 126px;
	width: 126px;
}
.user-aQuestion {
	min-width: 186px;
	max-width: 186px;
	width: 186px !important;
}
.user-gName{
	min-width: 186px;
	max-width: 186px;
	width: 186px !important;
}
.user-qStatus {
	min-width: 83px;
	max-width: 83px;
	width: 83px !important;
}
.user-cMenu {
	min-width: 50px;
	max-width: 50px;
	width: 50px !important;
	position: relative;
}
.user-cCollege {
	min-width: 115px;
	max-width: 115px;
	width: 115px !important;
}
/* columns fixed width */

.send-invite-container {
	width: 500px;
	height: calc(100vh - 53px);
	-webkit-box-shadow: 0px 3px 10px #00000029;
	        box-shadow: 0px 3px 10px #00000029;
	position: absolute;
	right: 0;
	background-color: white;
	z-index: 11;
}
.close-sidebar-btn-container {
	font-family: 'Open Sans', sans-serif;
	color: #a7a7a7;
	font-size: 17px;
	text-align: right;
	cursor: pointer;
	margin-left: auto;
	margin-top: 20px;
	margin-right: 25px;
	width: -webkit-max-content;
	width: -moz-max-content;
	width: max-content;
}
.close-sidebar-btn-container img {
	width: 18px;
	height: 18px;
	margin-top: -2px;
	margin-left: 4px;
}
.close-sidebar-btn-container img:hover {
	margin-top: -2px;
	margin-left: 4px;
}
.invite-text-container {
	padding: 15px 0px;
	margin: 0px 43px 0px 40px;
}
.invite-text {
	font-family: 'Avenir Book';
	font-weight: 400;
	font-size: 32px;
	margin-bottom: 0;
}
.invite-left-text {
	font-family: 'Avenir Book';
	font-weight: 400;
	font-size: 17px;
}
.invite-form-container {
	padding: 10px 40px;
	padding-top: 0 !important;
	max-height: calc(100vh - 200px);
	overflow: auto;
}
.invite-field {
	margin-top: 20px;
}
.invite-field .input-label {
	font-weight: 500;
	color: var(--primary-color);
	margin-bottom: 0.5rem;
	font-size: 17px;
}
.invite-field input {
	border: none;
	border-bottom: 1px solid #dedede;
	width: 100%;
	outline: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
}
#inviteEmailField input{
	border: none !important;
}
.invite-field textarea {
	border: none;
	border-bottom: 1px solid #dedede;
	width: 100%;
	outline: none;
	resize: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
}
.invite-field .tag-editor {
	border: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
	max-height: 80px;
    overflow: auto;
	/* margin-right: 50px; */
}
.invite-field .tag-editor .tag-editor-tag {
	background: white;
	color: #333333;
	cursor: default !important;
}
.invite-field .tag-editor .tag-editor-delete {
	background: white;
	color: #333333;
}
.invite-field .tag-editor .tag-editor-delete i {
	color: #333333;
}
.invite-field .tag-editor li {
	margin: 0;
	padding: 0;
}
.invite-field .tag-editor .placeholder div {
	padding: 0;
	color: #333333;
	opacity: 0.8;
}
.send-invite-btn {
	background: var(--primary-color);
	border: none;
	color: white;
	padding: 8px 20px;
	border-radius: 4px;
	/* font-weight: 500; */
	font-size: 15px;
	margin-left: auto;
	display: block;
	margin-top: 20px;
	font-family: 'Avenir', sans-serif;
}

.listTypeContainer {
	border: none;
	outline: none;
	/* for Firefox */
	-moz-appearance: none;
	/* for Safari, Chrome, Opera */
	-webkit-appearance: none;
}

#tbEmailReport {
	border-spacing: 0px 20px;
	border-collapse: unset;
	table-layout: fixed;
	width: 100%;
	margin-left: 30px;
}
#listData tr td:last-child {
	position: relative;
}
#listHead th {
	padding: 15px 10px;
	padding-bottom: 0;
	font-family: 'Montserrat', sans-serif;
	font-weight: 600;
	font-size: 15px;
}
#listData tr td {
	padding: 10px 10px;
	word-break: break-all;
	vertical-align: top;
	font-family: 'Hind', sans-serif;
	font-size: 15px;
}

.bootstrap-select,
.filter-option-inner-inner {
	color: #212529;
}

.bootstrap-select>.dropdown-toggle{
	font-family: 'Hind', sans-serif;
	font-weight: 500;
	-webkit-box-shadow: inset 0px 0px 2px 0px #00000029 !important;
	        box-shadow: inset 0px 0px 2px 0px #00000029 !important;
	border: 1px solid #D1D1D1;
	background-color: white;
	outline: none !important;
	color: #818181;
}
.dropdown-toggle::after{
	border: none;
}
.bootstrap-select.show .btn.dropdown-toggle::after{
	content: "\f0d8";
}
.btn.dropdown-toggle::after{
	font-family: "FontAwesome";
	content: "\f0d7";
	display: inline-block;
	vertical-align: middle;
	/* font-weight: 500; */
	position: relative;
	/* left: 13px; */
	color: #818181;
}
.btn-light:not(:disabled):not(.disabled).active, .btn-light:not(:disabled):not(.disabled):active, .show>.btn-light.dropdown-toggle{
	background-color: white !important;
	color: #818181;
}
.bootstrap-select .dropdown-toggle:focus, .bootstrap-select>select.mobile-device:focus+.dropdown-toggle{
	outline: none !important;
	color: #818181;
}
.dropdown-item:focus, .dropdown-item:hover{
	background-color: #F2F2F2 !important;
	color: #525252 !important;
}
.span-srno{
	position: relative;
	left: -10px;
	top: 1px;
	font-weight: 500;
}
#tblCCLst{
	margin-top: 0px;
}
#tblCCLst_wrapper .top,#tblCCLst-2_wrapper .top,#tblCCLst-3_wrapper .top {
    display: none !important;
}
#tblCCLst_wrapper .bottom,#tblCCLst-2_wrapper .bottom,#tblCCLst-3_wrapper .bottom{
	margin-top: 20px !important;
}
.table-card{
	overflow-x: scroll;
	margin-left: 95px;
	margin-right: 3%;
	margin-top: 80px;
	overflow-y: scroll;
    max-height: calc(100vh - 270px);
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
}

#tblCCLst input[type="checkbox"]::before,
#tblCCLst-2 input[type="checkbox"]::before,
#tblCCLst-3 input[type="checkbox"]::before {
	position: relative;
	display: block;
	width: 20px;
	height: 20px;
	border: 1px solid #C7C7C7;
	border-radius: 3px;
	-webkit-box-shadow: 0px 0px 4px #00000029;
	        box-shadow: 0px 0px 4px #00000029;
	content: "";
	background: white;
	cursor: pointer;
	margin-left: -2px;
 }

 #tblCCLst input[type="checkbox"]::after,
 #tblCCLst-2 input[type="checkbox"]::after,
 #tblCCLst-3 input[type="checkbox"]::after {
	position: relative;
	display: block;
	left: 0px;
	top: -20px;
	width: 20px;
	height: 20px;
	border: 1px solid #C7C7C7;
	border-radius: 3px;
	-webkit-box-shadow: 0px 0px 4px #00000029;
	        box-shadow: 0px 0px 4px #00000029;
	content: "";
	margin-left: -2px;
	cursor: pointer;
}
#tblCCLst input[type="checkbox"]:checked::after,
#tblCCLst-2 input[type="checkbox"]:checked::after,
#tblCCLst-3 input[type="checkbox"]:checked::after {
	width: 10px;
	height: 10px;
	border-radius: 3px;
	background: #4b4b4b;
	margin: 0 !important;
	text-shadow: none;
	-webkit-box-shadow: 0px 0px 4px #00000029 !important;
	        box-shadow: 0px 0px 4px #00000029 !important;
	border: none !important;
	outline: none !important; 
	position: relative;
	margin-top: 5px !important;
	margin-left: 3px !important; 
	cursor: pointer;
}
#tblCCLst tr th, #tblCCLst-2 tr th,#tblCCLst-3 tr th{
	padding-top: 15px !important;
	padding-bottom: 15px !important;
	border-top: 0 !important;
	text-transform: uppercase;
	position: sticky;
	top: 0;
	z-index: 3;
	background-color: white;
	padding-left: 20px !important;
	padding-right: 20px !important;
}
.table {
	color: #333333 !important;
}

#tblCCLst tr td, #tblCCLst tr th,#tblCCLst-2 tr td, #tblCCLst-2 tr th,#tblCCLst-3 tr th,#tblCCLst-3 tr td{
	white-space: nowrap;
	border-top: 1px solid #EDEDED !important;
    border-bottom: 1px solid #EDEDED !important;
	border-left: none !important;
    border-right: none !important;
	/* background-color: white; */
	border-top: 0 !important;
}
#tblCCLst tr td:first-child,#tblCCLst tr th:first-child {
    position: sticky;
    left: 0;
    z-index: 5;
	-webkit-box-shadow: 5px 0 5px -5px #333 !important;
	        box-shadow: 5px 0 5px -5px #333 !important;
	border-right: 1px solid #f2f2f2 !important;
	background-color: white;
}

/*If not working comment this upto search end-code */
#tblCCLst tr td:nth-child(2),#tblCCLst tr th:nth-child(2){
    position: sticky;
    left: 54px;
    z-index: 5;
	-webkit-box-shadow: 5px 0 5px -5px #333 !important;
	        box-shadow: 5px 0 5px -5px #333 !important;
	border-right: 0px solid #f2f2f2 !important;
	background-color: white;
}

#tblCCLst tr th:nth-child(2),#tblCCLst tr td:nth-child(2){
	padding-left: 20px !important;
	padding-right: 20px !important;
	text-align: center;
	-webkit-box-shadow: 5px 0 5px 0px rgb(0 0 0 / 3%) !important;
	        box-shadow: 5px 0 5px 0px rgb(0 0 0 / 3%) !important;
}

#tblCCLst tr td:nth-child(3),#tblCCLst tr th:nth-child(3) {
    position: sticky;
    left: 105px;
    z-index: 5;
	min-width: 200px !important;
	max-width: 200px !important;
	-webkit-box-shadow: 5px 0 5px -5px #333 !important;
	        box-shadow: 5px 0 5px -5px #333 !important;
	background-color: white;
}

#tblCCLst tr th:nth-child(3),#tblCCLst tr td:nth-child(3){
	padding-left: 20px !important;
	padding-right: 20px !important;
	text-align: left;
	-webkit-box-shadow: 5px 0 5px 0px rgb(0 0 0 / 3%) !important;
	        box-shadow: 5px 0 5px 0px rgb(0 0 0 / 3%) !important;
}

#tblCCLst tr td:nth-child(2):before,#tblCCLst tr th:nth-child(2):before{
	content: "";
	height: 100%;
	left: 0;
	top:0;
	width: 0px;
    position: absolute;
}

#tblCCLst tr td:nth-child(4), #tblCCLst tr th:nth-child(4){
	position: sticky;
	left: 345px;
	z-index: 5;
	width: 300px;
	max-width: 300px !important;
	min-width: 100px !important;
	-webkit-box-shadow: 5px 0 5px -5px #333 !important;
	        box-shadow: 5px 0 5px -5px #333 !important;
	border-right: 0px solid #f2f2f2 !important;
	background-color: white;
}
#tblCCLst tr th:nth-child(1),
#tblCCLst tr th:nth-child(2),
#tblCCLst tr th:nth-child(3),
#tblCCLst tr th:nth-child(4) {
	z-index: 6;
}

#tblCCLst tr th:nth-child(4),#tblCCLst tr td:nth-child(4){
	padding-left: 20px !important;
	padding-right: 20px !important;
	text-align: left;
	-webkit-box-shadow: 5px 0 5px 0px rgb(0 0 0 / 3%) !important;
	        box-shadow: 5px 0 5px 0px rgb(0 0 0 / 3%) !important;
}

/* end-code */

#tblCCLst tr td:last-child, #tblCCLst tr th:last-child {
    position: sticky;
    right: 0;
    z-index: 3;
	-webkit-box-shadow: -5px 0 5px 0px rgb(0 0 0 / 3%) !important;
	        box-shadow: -5px 0 5px 0px rgb(0 0 0 / 3%) !important;
	text-align: center;
	background-color: white !important;
	min-width: 25px !important;
	max-width: 30px !important;
	padding-left: 5px !important;
}
#tblCCLst{
	-webkit-box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px !important;
	        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px !important;
	border-radius: 10px;
}
.table-top-box{
	-webkit-box-shadow: 0 -4px 3px -4px rgba(99, 99, 99, 0.2);
	        box-shadow: 0 -4px 3px -4px rgba(99, 99, 99, 0.2);
	margin-left: 95px;
	margin-right: 2.9%;
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
}

.table-top-box .table-card{
	margin-left: 0 !important;
	margin-right: 0% !important;
	margin-top: 80px !important;
	overflow-y: scroll !important;
    max-height: calc(100vh - 275px) !important;
	min-height: calc(100vh - 275px);
}

.table-top-box .table-card::-webkit-scrollbar-track {
	margin-bottom: 58px;
}

/* .table-card{
	overflow-x: scroll;
	margin-left: 95px;
	margin-right: 3%;
	margin-top: 80px;
	overflow-y: scroll;
    max-height: calc(100vh - 270px);
} */
#tblCCLst tr th:last-child .table-options-container{
	text-align: left !important; /* only for th + dropdown */
}
#tblCCLst tr td:first-child,#tblCCLst tr td:last-child{
	z-index: 1;
}
#tblCCLst tr th:first-child,#tblCCLst tr td:first-child{
	padding-left: 20px !important;
	padding-right: 20px !important;
	text-align: center;
	-webkit-box-shadow: 5px 0 5px 0px rgb(0 0 0 / 3%) !important;
	        box-shadow: 5px 0 5px 0px rgb(0 0 0 / 3%) !important;
}
#tblCCLst tr td:first-child::before,#tblCCLst tr th:first-child::before{
	content: "";
	border: .5px solid #f2f2f2;
	height: 100%;
	left: 0;
	top:0;
    position: absolute;
}
#tblCCLst tr td:last-child::after,#tblCCLst tr th:last-child::after{
	content: "";
	border: .5px solid #f2f2f2;
	height: 100%;
	right: 0;
	top:0;
    position: absolute;
}
#tblCCLst tr:last-child td{
	border-bottom: none !important;
}
#tblCCLst tr:last-child td:first-child{
	border-bottom-left-radius: 10px;
}
#tblCCLst tr:last-child td:last-child{
	border-bottom-right-radius: 10px;
}

#tblCCLst-3,#tblCCLst-2{
	-webkit-box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
	        box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
    border-bottom: none !important;
    border-top: none !important;
    background-color: white;
    border-radius: 10px;
	width: 99.5% !important;
}

#tblCCLst-3 tr:last-child td:first-child,#tblCCLst-2 tr:last-child td:first-child{
	border-bottom-left-radius: 10px;
}
#tblCCLst-3 tr:last-child td:last-child,#tblCCLst-2 tr:last-child td:last-child{
	border-bottom-right-radius: 10px;
}
th:first-child {
	border-top-left-radius: 10px;
}
th:last-child {
	border-top-right-radius: 10px;
}

#tblCCLst-3 td:first-child,
#tblCCLst-2 td:first-child{
	text-align: center !important;
}

#tblCCLst-3 td:first-child.dataTables_empty  {
	text-align: center !important;
}
#tblCCLst-2 td:first-child.dataTables_empty{
	text-align: center !important;
}

#tblCCLst_length select,#tblCCLst-2_length select, #tblCCLst-3_length select{
	background-image: -o-linear-gradient(45deg, transparent 50%, gray 50%),
    -o-linear-gradient(315deg, gray 50%, transparent 50%),
    -o-linear-gradient(left, #ccc, #ccc);
	background-image: linear-gradient(45deg, transparent 50%, gray 50%),
    linear-gradient(135deg, gray 50%, transparent 50%),
    linear-gradient(to right, #ccc, #ccc);
    background-position:
    calc(100% - 12px) calc(1em + -2px),
    calc(100% - 7px) calc(1em + -2px),
    calc(100% - 2.5em) 0.5em;
  background-size:
    5px 5px,
    5px 5px,
    0px 1.5em;
  background-repeat: no-repeat;
  -webkit-appearance: none;
}

th {
    font-family: 'Avenir', sans-serif !important;
    font-weight: 500 !important;
    font-size: 15px !important;
    padding: 19px 18px !important;
}

.reports-page-container {
	max-height: unset !important;
}

table.dataTable.stripe tbody tr.odd, table.dataTable.display tbody tr.odd {
    background-color: white;
}
table.dataTable.hover tbody tr:hover, table.dataTable.display tbody tr:hover {
    background-color: white;
}
table.dataTable.order-column tbody tr>.sorting_1, table.dataTable.order-column tbody tr>.sorting_2, table.dataTable.order-column tbody tr>.sorting_3, table.dataTable.display tbody tr>.sorting_1, table.dataTable.display tbody tr>.sorting_2, table.dataTable.display tbody tr>.sorting_3 {
    background-color: white !important;
}
table.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {
    background-color: #f2f2f2 !important;
}

table.dataTable tbody > tr.selected > td {
	background-color: #f2f2f2 !important;
}
table.dataTable.order-column tbody tr.selected>.sorting_1, table.dataTable.order-column tbody tr.selected>.sorting_2, table.dataTable.order-column tbody tr.selected>.sorting_3, table.dataTable.display tbody tr.selected>.sorting_1, table.dataTable.display tbody tr.selected>.sorting_2, table.dataTable.display tbody tr.selected>.sorting_3 {
	background-color:unset !important;
	background: #f2f2f2 !important;
}

table.dataTable tbody th, table.dataTable tbody td {
    font-size: 15px;
    font-family: 'Avenir Book' !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover{
	background: none !important;
	-webkit-box-shadow: rgb(0 0 0 / 16%) 0px 1px 2px;
	        box-shadow: rgb(0 0 0 / 16%) 0px 1px 2px;
	border: 1px solid rgb(199, 199, 199);
	border-radius: 3px;
	color: rgb(51, 51, 51) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled .fa {
    opacity: .5;
    cursor: not-allowed !important;
}

/* tbEmailReport */
a.btn-1:hover{
	color: white !important;
}
.hover-focus-outside{
	width: 28px;
    height: 28px;
    border-radius: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding: 4px;
    margin: 0px 5px;
	cursor: pointer;
}

.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 180px !important;
}

#select-template-container .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 220px !important;
}

	/* dropdown css */
	.btn.dropdown-toggle.btn-light {
		background: none;
		-webkit-box-shadow: inset 0px 0px 3px #00000029 !important;
		        box-shadow: inset 0px 0px 3px #00000029 !important;
		border: 1px solid #d1d1d1;
		color: #212529;
		outline: none !important;
	}

	#select-template-container .btn.dropdown-toggle::after {
		color: var(--primary-color) !important;
		border: none;
	}
	.bootstrap-select.show .btn.dropdown-toggle::after {
		content: '\f0d8';
	}
	
	.btn.dropdown-toggle::after {
		font-family: 'FontAwesome';
		content: '\f0d7';
		display: inline-block;
		vertical-align: middle;
		/* font-weight: 500; */
		position: relative;
		/* left: 13px; */
		color: #818181;
	}
	
	.dropdown-item.active,
	.dropdown-item:active {
		background-color: #f2f2f2 !important;
		color: #525252 !important;
	}
	
	.dropdown-menu.inner.show {
		-webkit-box-shadow: none !important;
		        box-shadow: none !important;
						max-height: 185px;
	}
	#select-template-container .dropdown * {
		font-family: 'Avenir Book' !important;
		font-weight: 400 !important;
		font-size: 15px;
	}


[data-customTooltip]{
    position: relative;	
}
[data-customTooltip]::after {
	text-transform: lowercase;
    background-color: black;
    color: #fff;
    font-size:12px;
    padding: 3px 10px;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    border-radius: 3px;
    position: absolute;
    text-align: center;
    bottom: 12px;
    left: 50%;
    content: attr(data-customTooltip);
    -webkit-transform: translate(-50%, 110%) scale(0);
        -ms-transform: translate(-50%, 110%) scale(0);
            transform: translate(-50%, 110%) scale(0);
    -webkit-transform-origin: top;
        -ms-transform-origin: top;
            transform-origin: top;
    -webkit-transition: 0.2s;
    -o-transition: 0.2s;
    transition: 0.2s;
	z-index: 1000;
	white-space: nowrap;
    -webkit-box-shadow: 0 4px 14px 0 rgba(0,0,0,.2), 0 0 0 1px rgba(0,0,0,.05);
            box-shadow: 0 4px 14px 0 rgba(0,0,0,.2), 0 0 0 1px rgba(0,0,0,.05);
  }

  .tooltip-up[data-customTooltip]::after{
    left: 55%;
  }

  [data-customTooltip]::before{
	content: " ";
	border-top: 11px solid #222;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	position: absolute;
	-webkit-transform: translate(-50%, 110%) scale(0);
	    -ms-transform: translate(-50%, 110%) scale(0);
	        transform: translate(-50%, 110%) scale(0);
	-webkit-transition: 0.2s;
	-o-transition: 0.2s;
	transition: 0.2s;
	top: -2px;
	right: -10%;
	z-index: 1000;
  }

  .tooltip-up[data-customTooltip]::before{
    top: -8px;
	left: 50%;
  }

  [data-customTooltip]:hover:after {
    display: block;
    -webkit-transform: translate(-50%, -100%) scale(1);
        -ms-transform: translate(-50%, -100%) scale(1);
            transform: translate(-50%, -100%) scale(1);
  }
  [data-customTooltip]:hover::before {
    display: block;
    -webkit-transform: translate(-50%, -100%) scale(1);
        -ms-transform: translate(-50%, -100%) scale(1);
            transform: translate(-50%, -100%) scale(1);
  }

  /* dropdown customize */
.dropdown-menu{
	margin-top: 8px;
	border: none !important;
	padding-top: 0px !important;
	padding-bottom: 0px !important;
	border-radius: 0px !important;
	-webkit-box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
	        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
.bootstrap-select .dropdown-menu li a{
	padding: 8px 12px !important;
}


.ql-toolbar.ql-snow{
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
}
.ql-container.ql-snow {
	border-top: 0px;
	border-bottom-left-radius: 10px;
	border-bottom-right-radius: 10px;
}

.switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 25px;
}
.switch input {
	display: none;
}
.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: 0.4s;
	-o-transition: 0.4s;
	transition: 0.4s;
	max-width: 52px;
}
.slider:before {
	position: absolute;
	content: '';
	height: 14px;
	width: 14px;
	left: 4px;
	bottom: 6px;
	background-color: #fff;
	-webkit-transition: 0.4s;
	-o-transition: 0.4s;
	transition: 0.4s;
}
input:checked + .slider {
	/* background-color: #2196f3; */
}
input:focus + .slider {
	/* box-shadow: 0 0 1px #	2196f3; */
}
input:checked + .slider:before {
	-webkit-transform: translateX(26px);
	-ms-transform: translateX(26px);
	transform: translateX(26px);
}
.slider.round {
	border-radius: 34px;
}
.slider.round:before {
	border-radius: 50%;
}

.slider {
	background-color: #e5e5e5;
}

input:checked + .slider {
	background-color: var(--slider-bg);
}
input:checked + .slider:before {
	background-color: var(--primary-dark);
}
.span-slider{
	color: #6F6F6F;
    font-size: 15px;
	font-weight: 500;
    position: relative;
    top: -16px;
}
.ql-editor, .ql-toolbar.ql-snow{
	font-family: 'Avenir',sans-serif !important;
}

.inactive-link{
	-webkit-transform: rotate(180deg);
	    -ms-transform: rotate(180deg);
	        transform: rotate(180deg);
}
.ql-tooltip{
	left: 10px !important;
} 

.TemplateVariable-group{
	background-color: white;
	border-bottom-left-radius: 12px;
	border-bottom-right-radius: 12px;
	border: 1px solid #f2f2f2;
	-webkit-box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
	        box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
}

.btn.btn-orange{
	color: #212529;
	font-size: 12px;
	white-space: nowrap;
	padding-top: 8px;
	padding-bottom: 8px;
	font-weight: 500;
	border-radius: 0;
	background-color: #FFFF;
}
.btn.btn-orange:hover{
	color: #212529;
}
.btn.btn-orange:nth-child(1){
	padding-right: 0px;
	border-bottom-left-radius: 12px !important;
}
.btn.btn-orange:nth-child(2){
	padding-left: 4px;
	padding-right: 4px;
}
.btn.btn-orange:nth-child(3){
    padding-left: 2px;
	padding-right: 4px;
}
.btn.btn-orange:nth-child(4){
	padding-left: 0px;
	padding-right: 10px;
	border-bottom-right-radius: 12px !important;
}
.btn.btn-orange:nth-child(1)::after,.btn.btn-orange:nth-child(2)::after,.btn.btn-orange:nth-child(3)::after{
	content: "";
	margin-left: 18px;
	border-right: 1px solid #212529 !important;
}

/* datatable pagination custom css */

.dataTables_length{
    font-size: 14px !important;
    line-height: 20px !important;
    font-family: 'Hind', sans-serif !important;
}

.dataTables_length .btn.dropdown-toggle.btn-light{
	width: 60px !important;
	font-size: 14px !important;
	padding: 3px 0px 2px 8px !important;
}
.dataTables_length .btn.dropdown-toggle::after{
    left: -8px !important;
} 
.dataTables_length .dropup .dropdown-toggle::after{
	border:none !important;
	border-bottom: none !important;
}

.dataTables_length .bootstrap-select .dropdown-menu{
	min-width: 60px !important; 
}

.btn-secondary-thm {
	width: 37px;
	height: 37px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: var(--secondary-color);
	color: var(--primary-color);
	outline: none !important;
	box-shadow: none !important;
	border: 0 !important;
	border-radius: 4px;
	margin-right: 15px;
	padding: 3px 10px;
}

.under-tooltip[data-customTooltip]::before {
	transition: 0s !important;
	top: 50px !important;
	transform: translate(-50%, -110%) rotate(180deg) scale(0) !important;
}

.under-tooltip[data-customTooltip]::after {
	transition: 0s !important;
	top: 70px;
	left: 150% !important;
	transform-origin: bottom;
}


.modal-content-rooms-bulk-upload {
	z-index: 2000;
	position: relative;
	background-color: #fefefe;
	margin: auto;
	padding-top: 15px;
	padding-bottom: 15px;
	padding-right: 20px;
	padding-left: 20px;
	width: 30%;
	background: #ffffff 0% 0% no-repeat padding-box;
	box-shadow: 0px 0px 10px #00000029;
	border-bottom-left-radius: 10px;
	border-bottom-right-radius: 10px;
	margin-top: 42px;
}

.modal-allow-room-upload {
	position: fixed;
	z-index: 11;
	padding-top: 10px;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	overflow: auto;
	background-color: rgba(0, 0, 0, 0);
			transform: translateY(-1000px);
	transition: 0.3s ease-in-out;
}

.modal-allow-room-upload .upload-room-file img {
	width: 13px;
}

.visible-modal-allow-room-upload {
	transform: translateY(0);
}

.main-error-modal {
	width: 53%;
}

.main-error-modal .modal-content-header {
	padding: 0px 20px;
	padding-bottom: 20px;
}

.heading-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 4px 0px 8px;
}

.heading-bar h6 {
	margin-top: 0px;
	margin-bottom: 0px;
	font-family: 'Hind', sans-serif !important;
	font-size: 16px;
	font-weight: 500;
}

.table-holder {
	max-height: 700px;
	border-radius: 4px;
	overflow: auto;
}

.table-holder table {
	border: 1px solid #e5e5e5;
	border-width: 0px;
	width: 100%;
	border-right-width: 1px;
}

#download-data {
	margin-top: 20px;
	font-size: 16px;
}

.btn-thm {
	width: 37px;
	height: 37px;
	background: var(--primary-color);
	color: white;
	outline: none !important;
	box-shadow: none !important;
	border: 0 !important;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 4px;
}

.heading-bar .close-button-icon {
	font-size: 20px;
	cursor: pointer;
}

.bulk-upload-btn [data-customTooltip]::after {
	left: 3.5rem;
	bottom: 30px;
}
.bulk-upload-btn [data-customTooltip]::before {
	border-left: 11px solid transparent;
	border-right: 11px solid transparent;
}

.error-table-tr td:not(:last-child) {
	width: unset !important;
}