/* import fonts */
/* @font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Black.ttf')
		format('truetype');
	font-weight: 900;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Heavy.ttf')
		format('truetype');
	font-weight: 700;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Medium.ttf')
		format('truetype');
	font-weight: 500;
	font-style: normal;
} */
@font-face {
	font-family: 'Avenir Book';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-Book.ttf') format('truetype');
	font-weight: 500;
	font-style: normal;
}
@font-face {
	font-family: 'Avenir Book';
	src: url('/websiteNew/assets/fonts/Avenir/Avenir-BookOblique.ttf')
		format('truetype');
	font-weight: 400;
	font-style: italic;
}

@font-face {
	font-family: 'Avenir';
	src: url('/fonts/Avenir.ttc')
		format('truetype');
}
/* import fonts */
.row {
	width: 100%;
}
body {
	font-family: 'Hind', sans-serif;
}
.full-page-container {
	width: 100%;
	/* display: block;
    margin: auto; */
	height: calc(100vh - 130px);
	overflow-y: auto;
	overflow: hidden;
}
.page-divider-container{
	display: grid;
    grid-template-columns: 0.35fr 1.28fr;
	min-height: calc(100vh - 250px);
	max-height: calc(100vh - 250px);
	/* overflow: auto; */
	margin-top: 120px;
}
.quick-link-container{
	border-right: 1px solid #EDEDED;
	text-align: right;
    font-size: 17px;
	font-family: 'Avenir';
	font-weight: 500;
	padding-top: 50px;
}
.active-tab-icon{
	visibility: hidden;
	margin-left: 10px;
}
.quick-link-container .quick-link{
	font-family: 'Avenir Book';
	margin-bottom: 10px;
	width: max-content;
    margin-left: auto;
	cursor: pointer;
}
.quick-link-container .quick-link.active{
	color: var(--primary-color);
	font-family: 'Avenir';
}
.quick-link-container .quick-link.active .active-tab-icon{
	visibility: visible;
	color: var(--primary-color);
}
.quiz-controls-container{
	display: none;
}
.mail-settings-container{
	display: none;
	padding-left: 15px;
}
.add-class-container {
	/* margin-top: 30px; */
	padding: 0px 20px;
	padding-top: 51px;
	padding-bottom: 100px;
	overflow: auto;
	padding-right: 96px;
	/* height: calc(100vh - 249px); */
}
.add-class-container .quiz-text-msg {
	color: var(--primary-color);
	font-weight: 500;
}
.add-class-form-container .form-control {
	/* box-shadow: 0px 2px 4px #00000029 !important; */
	/* border: 1px solid #d1d1d1; */
	color: #495057 !important;
}
input,textarea{
	color: #495057 !important;
}
.add-class-form-container label {
	font-size: 15px;
	font-family: 'Avenir';
	font-weight: 500;
	color: #6F6F6F;
}
#quiz-title-name , #testLink{
	height: 27px;
}
.add-class-container .add-description {
	/* margin-top: 50px; */
	border: none;
	width: 100%;
	border-bottom: 1px solid #e0e0e0;
}
.add-class-container .add-description:focus {
	outline: none;
}
.add-class-container .add-description::placeholder {
	color: #8d8d8d;
}
.add-description.add-title::placeholder {
	/* font-size: 25px; */
}
.forms-container{
	margin-bottom: 30px;
	padding-left: 15px !important;
}
.forms-container label{
	font-size: 15px;
	font-family: 'Avenir';
	font-weight: 500;
	color: #6F6F6F;
}
#fieldTable.table{
	font-family: 'Avenir Book';
}
#instructionContainer .ql-toolbar.ql-snow{
	border-top-right-radius: 10px;
	border-top-left-radius: 10px;
}
#instructionContainer .ql-container.ql-snow{
	border-bottom-right-radius: 10px;
	border-bottom-left-radius: 10px;
}
.add-content-container p {
	font-family: 'Hind', sans-serif;
	font-size: 32px;
	color: var(--primary-color);
	margin-bottom: 0.5rem;
}
.add-content-container a {
	font-family: 'Hind', sans-serif;
	color: var(--primary-color);
	font-size: 14px;
}

/* radio btn style starts */
.container-radio {
	display: block;
	position: relative;
	padding-left: 28px;
	margin-bottom: 12px;
	cursor: pointer;
	font-size: 16px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	width: max-content;
}
.container-radio input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
}
.checkmark {
	position: absolute;
	top: 0;
	left: 0;
	height: 20px;
	width: 20px;
	background-color: white;
	border-radius: 4px;
	box-shadow: 0px 1px 6px #00000029;
	border: 1px solid #C7C7C7;
}

.checkmark:after {
	content: '';
	position: absolute;
	display: none;
}

.container-radio input:checked ~ .checkmark:after {
	display: block;
}

.container-radio .checkmark:after {
	top: 4px;
	left: 4px;
	width: 10px;
	height: 10px;
	border-radius: 2px;
	background: #4b4b4b;
}
/* radio btn style ends */
.upload-documents-container {
	margin-top: 50px;
	font-family: 'Hind', sans-serif;
}
.upload-documents-container h2 {
	color: var(--primary-color);
	font-weight: 500;
}
.upload-documents-container p {
	color: #8d8d8d;
}
.upload-btn {
	background: var(--primary-color);
	border: 1px solid var(--primary-color);
	border-radius: 5px;
	color: white;
	padding: 5px 20px;
	cursor: pointer;
}
.uploaded-files-container {
	height: 110px;
	overflow-y: auto;
}
.uploaded-files-container p,
.uploaded-files-container a {
	color: var(--primary-color);
	margin: 0;
}
.save-btn-container {
	background-color: #efefef;
	padding: 20px;
	position: absolute;
	bottom: 0;
	width: 100%;
}
.save-btn-container button {
	float: left;
	background: var(--primary-color);
	border: 1px solid var(--primary-color);
	border-radius: 5px;
	color: white;
	padding: 5px 20px;
}
.class-code {
	box-shadow: 0px 2px 4px #00000029 !important;
	border: 1px solid #d1d1d1;
	color: #8d8d8d;
	/* margin-top: 10px; */
	/* width: 200px; */
}

/* tag editor */
.tag-editor .tag-editor-delete i:before {
	color: white;
	position: relative;
	top: 1px;
}
.tag-editor .tag-editor-delete:hover i::before {
	color: white;
}
.tag-editor .tag-editor-delete {
	border-radius: 0 4px 4px 0;
}
.tag-editor .tag-editor-tag {
	border-radius: 4px 0 0 4px;
	display: inline-block;
	max-width: 96%;
	cursor: default !important;
}
.tag-editor {
	background-color: #f7f7f7;
	/* height: 35px; */
	border-radius: 5px;
	border: none;
	min-height: 35px;
	overflow: auto;
	max-height: 300px;
}

#scrollingArea .tag-editor{
	max-height: 70px !important;
	background:#fff;
	padding: 1px 0px;
	border: 1px solid #dedede !important;
}

.tag-editor-tag,
.tag-editor-delete {
	font-family: 'Hind', sans-serif !important;
	color: white !important;
	background: var(--primary-dark) !important;
	border-radius: 5px;
	padding: 3px 5px !important;
}
.tag-editor li {
	width: max-content;
}
.tag-editor li:hover {
	margin: 3px 0 !important;
}

.keywords-box .tag-editor .ui-sortable-handle , #fieldValuesContainer .tag-editor .ui-sortable-handle , .allowedIPContainer .tag-editor .ui-sortable-handle{
	position: relative;
	right: 3px;
}
/* tag editor */

/* select box design */
.select2-container--default .select2-selection--single {
	box-shadow: 0px 2px 4px #00000029 !important;
	border: 1px solid #d1d1d1;
	color: #8d8d8d;
	outline: none;
	height: calc(1.5em + 0.75rem + 2px);
	padding-top: 5px;
}
.select2-results__option:hover {
	padding: 6px;
}
.select2-container--default
	.select2-results__option--highlighted[aria-selected] {
	background-color: var(--primary-color);
}
.select2-container--default
	.select2-selection--single
	.select2-selection__arrow {
	padding-top: 35px;
}
.select2-results__option {
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

li.select2-results__option {
	font-size: 13px;
}
.select2-container--default
	.select2-selection--multiple
	.select2-selection__choice {
	background: var(--primary-dark);
	color: white;
	font-size: 15px;
}
.select2-container--default
	.select2-selection--multiple
	.select2-selection__rendered {
	overflow: auto;
}
.dropdown-item.active,
.dropdown-item:active {
	background-color: #0000001a;
	outline: none;
	color:#525252 !important;
}
.bootstrap-select.show .btn.dropdown-toggle::after {
	content: '\f0d8';
	color: #9b9b9b;
	top: -1px;
}
.btn.dropdown-toggle::after {
	font-family: 'FontAwesome';
	content: '\f0d7';
	display: inline-block;
	vertical-align: middle;
	/* font-weight: 500; */
	position: relative;
	left: 0px;
	border: none;
	/* top: 1px; */
	color: #9b9b9b;
}
.btn.dropdown-toggle {
	background: white;
	box-shadow: inset 0px 0px 2px 0px #00000029 !important;
	border: 1px solid #D1D1D1 !important;
	border-radius: 5px;
	outline: none !important;
}
.bootstrap-select, .filter-option-inner-inner{
	font-family: 'Hind', sans-serif !important;
    font-weight: 500 ;
    color: #212529 !important;
    font-size: 15px;
}
.btn-light:not(:disabled):not(.disabled).active,
.btn-light:not(:disabled):not(.disabled):active,
.show > .btn-light.dropdown-toggle {
	background: white;
	outline: none !important;
}
.bootstrap-select .dropdown-toggle:focus,
.bootstrap-select > select.mobile-device:focus + .dropdown-toggle {
	outline: none !important;
}
.dropdown.bootstrap-select {
	/* border: 1px solid #d1d1d1; */
}
.bootstrap-select div.dropdown-menu.show{
	/* transform: translate3d(0px, 36px, 0px) !important; */
	margin-top: 8px;
    border: none !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    border-radius: 0px !important;
    box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
}
.LanguagesAllowed .bootstrap-select div.dropdown-menu.show{
	transform: translate3d(0px, 36px, 0px) !important;
}
.dropdown-item{
	padding: 8px 12px !important;
	background-color: transparent;
    border: 0;
    font-family: 'Hind',sans-serif !important;
    font-size: 16px;
	color: #212529 !important;
}
.bootstrap-select div.dropdown-menu.show .inner.show{
	max-height: 200px;
}
.choose-file-btn {
	color: var(--primary-color);
	cursor: pointer;
}
.choose-file-btn img {
	width: 20px;
	height: 20px;
}
.message-box-container {
	display: grid;
	/* grid-template-columns: 1fr 10fr; */
	margin-top: 10px;
}
.message-box-container textarea,
.message-box-container input {
	font-family: 'Hind', sans-serif !important;
    width: 90%;
    outline: none;
    border: none;
    border-bottom: 1px solid #E0E0E0;
}
.template-playholder-container{
    background: var(--primary-light);
    width: max-content;
    display: block;
    margin-left: auto;
	margin-right: 14%;
    padding: 9px 5px;
    border-bottom-right-radius: 12px;
    border-bottom-left-radius: 12px;
	font-family: 'Hind', sans-serif !important;
	font-weight: 500;
	font-size: 15px;
}
.template-playholder-container a{
	/* background-color: var(--primary-light); */
	color: var(--primary-color);
	cursor: pointer;
	text-decoration: none;
}
.template-playholder-container a:hover{
	color: var(--primary-color);
}
.template-playholder-container a{
	border-right: 1px solid var(--primary-color);
	padding-right: 10px;
	padding-left: 10px;
}
.template-playholder-container a:last-child{
	border-right: none !important;
}
.template-btns-container {
	margin-top: 20px;
}
.template-btns-container a {
	background: var(--primary-color);
	border: none;
	outline: none;
	color: white;
	border-radius: 3px;
	padding: 3px 8px;
	margin-right: 10px;
	cursor: pointer;
}
.template-btns-container a:hover {
	color: white;
}
.template-setting-msg {
	font-weight: 500;
	margin-bottom: 0;
	margin-top: 20px;
	width: max-content;
	font-size: 17px;
}
.hide {
	display: none;
}
.common-quiz-header-container {
	padding: 30px 98px 0px 98px;
	border-bottom: 1px solid #ededed;
	font-family: 'Avenir';
	position: fixed;
	z-index: 8;
	background-color: white;
	width: 100%;
}
.all-tests {
	font-family: 'Avenir';
	font-weight: 400;
	font-size: 17px;
	color: #000000;
	margin-bottom: 0;
}
.all-tests a{
	font-family: 'Avenir';
	font-weight: 400;
	font-size: 17px;
	color: #000000;
	text-decoration: none;
}
.all-tests i {
	color: var(--primary-color);
	position: relative;
	left: 5px;
	top: 2px;
	font-size: 20px;
}
.quiz-name-options-container {
	display: grid;
	grid-template-columns: 2fr 1.2fr;
	gap: 10px;
}
.quiz-name, .quiz-type {
	color: var(--primary-color);
	font-family: 'Avenir';
	font-weight: 500;
	font-size: 29px;
	margin-bottom: 5px;
	max-width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.btn-1 {
	border: none;
	border-radius: 3px;
	padding: 5px 20px;
	background: var(--primary-color);
	color: white !important;
	font-family: 'Avenir';
	font-weight: 500;
	margin-right: 10px;
	width: max-content;
	/* margin: auto; */
	margin-left: auto;
	cursor: pointer;
}
.btn-2 {
	border: none;
	border-radius: 3px;
	padding: 5px 20px;
	background: var(--primary-light);
	color: var(--primary-color);
	font-family: 'Avenir';
	font-weight: 500;
	margin-right: 10px;
}
.page-tabs-container .tabs {
	display: inline-block;
	margin-right: 10px;
	font-family: 'Avenir';
	font-size: 17px;
	padding-bottom: 5px;
}
.page-tabs-container .tabs a {
	text-decoration: none;
	color: inherit;
}
.page-tabs-container .tabs.active {
	border-bottom: 4px solid var(--primary-color);
}

.send-invite-container {
	width: 500px;
	height: calc(100vh - 53px);
	box-shadow: 0px 3px 10px #00000029;
	position: absolute;
	right: 0;
	background-color: white;
	z-index: 11;
}
.close-sidebar-btn-container {
	font-family: 'Open Sans', sans-serif;
	color: #a7a7a7;
	font-size: 17px;
	text-align: right;
	cursor: pointer;
	margin-left: auto;
	margin-top: 20px;
	margin-right: 25px;
	width: max-content;
}
.close-sidebar-btn-container img {
	width: 18px;
	height: 18px;
	margin-top: -2px;
	margin-left: 4px;
}
.close-sidebar-btn-container img:hover {
	margin-top: -2px;
	margin-left: 4px;
}
.invite-text-container {
	padding: 15px 0px;
	margin: 0px 43px 0px 40px;
}
.invite-text {
	font-family: 'Avenir Book';
	font-weight: 400;
	font-size: 32px;
	margin-bottom: 0;
}
.invite-left-text {
	font-family: 'Avenir Book';
	font-weight: 400;
	font-size: 17px;
}
.invite-form-container {
	padding: 10px 40px;
	padding-top: 0 !important;
	max-height: calc(100vh - 200px);
	overflow: auto;
}
.invite-field {
	margin-top: 20px;
}
.invite-field .input-label {
	font-weight: 500;
	color: var(--primary-color);
	margin-bottom: 0.5rem;
	font-size: 17px;
	font-family: 'Avenir' !important;
}
.invite-field input {
	border: none;
	border-bottom: 1px solid #dedede;
	width: 100%;
	outline: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
}
.invite-field textarea {
	border: none;
	border-bottom: 1px solid #dedede;
	width: 100%;
	outline: none;
	resize: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
}
.invite-field .tag-editor {
	border: none;
	font-size: 15px;
	font-family: 'Avenir', sans-serif;
    background-color: white;
    min-height: unset;
	max-height: 80px;
    overflow: auto;
	/* margin-right: 50px; */
}
.invite-field .tag-editor .tag-editor-tag {
	background: white !important;
	color: #333333 !important;
}
.invite-field .tag-editor .tag-editor-delete {
	background: white !important;
	color: #333333 !important;
}
.invite-field .tag-editor .tag-editor-delete i {
	color: #333333 !important;
}
.invite-field .tag-editor li {
	margin: 0 !important;
	padding: 0 !important;
}
.invite-field .tag-editor li input {
	border: none;
}
.invite-field .tag-editor .placeholder div {
	padding: 0 !important;
	color: #333333 !important;
	opacity: 0.8 !important;
}
.invite-field .tag-editor .tag-editor-delete i:before{
    color: #8ba7ba;
}

/* tag editor css start */
.ui-widget-content {
	overflow: auto;
	padding: 0 !important;
	max-height: 250px !important;
    border: none !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    border-radius: 0px !important;
    box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
	width: 160px;
}

.ui-menu .ui-menu-item-wrapper {
	color: #525252 !important;
	border: none !important;
	margin: 0 !important;
	background: transparent !important;
}

.ui-menu .ui-menu-item-wrapper:hover {
	border: none !important;
	margin: 0 !important;
}

.ui-menu .ui-menu-item {
	border: none !important;
	margin: 0 !important;
	padding: 5px 3px !important;
}

.ui-menu .ui-menu-item:hover {
	border: none !important;
	margin: 0 !important;
	color: #525252 !important;
	padding: 5px 3px !important;
	background-color: #f2f2f2 !important;
}
/* tag editor css end */

#TemplateVariable-group{
	overflow-x: scroll;
    background-color: white;
	border-bottom-left-radius: 12px;
	border-bottom-right-radius: 12px;
	border: 1px solid #f2f2f2;
	box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
}
.btn.btn-orange{
	color: #212529;
	font-size: 12px;
	white-space: nowrap;
	padding-top: 8px;
	padding-bottom: 8px;
	font-weight: 500;
	border-radius: 0;
	background-color: #FFFF;
}
.btn.btn-orange:hover{
	color: #212529;
}
.btn.btn-orange:nth-child(1){
	padding-right: 0px;
	border-bottom-left-radius: 12px !important;
}
.btn.btn-orange:nth-child(2){
	padding-left: 4px;
	padding-right: 4px;
}
.btn.btn-orange:nth-child(3){
    padding-left: 2px;
	padding-right: 4px;
}
.btn.btn-orange:nth-child(4){
	padding-left: 0px;
	padding-right: 10px;
	border-bottom-right-radius: 12px !important;
}
.btn.btn-orange:nth-child(1)::after,.btn.btn-orange:nth-child(2)::after,.btn.btn-orange:nth-child(3)::after{
	content: "";
	margin-left: 18px;
	border-right: 1px solid #212529 !important;
}
.send-invite-btn {
	background: var(--primary-color);
	border: none;
	color: white;
	padding: 8px 20px;
	border-radius: 4px;
	font-weight: 500;
	font-size: 15px;
	margin-left: auto;
	display: block;
	margin-top: 20px;
	font-family: 'Avenir' !important;
}


.switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 25px;
}
.switch-wrapper.disabled .switch {
	z-index: -1;
}
.switch input {
	display: none;
}
.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: 0.4s;
	transition: 0.4s;
	max-width: 52px;
}
.switch-wrapper.disabled .slider {
	background-color: #e9ecef;
}
.slider::before {
	position: absolute;
	content: '';
	height: 14px;
	width: 14px;
	left: 4px;
	bottom: 6px;
	background-color: #fff;
	-webkit-transition: 0.4s;
	transition: 0.4s;
}
.switch-wrapper.disabled .slider::before {
	background-color: #f8f8f8;
}
input:checked + .slider {
	/* background-color: #2196f3; */
}
input:focus + .slider {
	/* box-shadow: 0 0 1px #	2196f3; */
}
input:checked + .slider:before {
	-webkit-transform: translateX(26px);
	-ms-transform: translateX(26px);
	transform: translateX(26px);
}
.slider.round {
	border-radius: 34px;
}
.slider.round:before {
	border-radius: 50%;
}

.slider {
	background-color: #e5e5e5;
}

input:checked + .slider {
	background-color: var(--slider-bg);
}
input:checked + .slider:before {
	background-color: var(--primary-dark);
}
.span-slider{
	color: #6F6F6F;
    font-size: 15px;
	font-weight: 500;
    position: relative;
    top: -16px;
	font-family: 'Avenir',sans-serif;
}
.switch-wrapper.disabled .span-slider {
	color: #c7c8c8;
}
.switch-wrapper.disabled .span-slider>div{
	color: #d9dbdc !important;
}
.tag-editor div{
	padding: 4px 4px !important;
}
input[type="checkbox"]:before {
	position: relative;
	display: block;
	width: 20px;
	height: 20px;
	border: 1px solid #C7C7C7;
	border-radius: 3px;
	box-shadow: 0px 0px 4px #00000029;
	content: "";
	background: white;
	cursor: pointer;
	margin-left: -2px;
 }
 input[type="checkbox"]:after {
	position: relative;
	display: block;
	left: 0px;
	top: -20px;
	width: 20px;
	height: 20px;
	border: 1px solid #C7C7C7;
	border-radius: 3px;
	box-shadow: 0px 0px 4px #00000029;
	content: "";
	margin-left: -2px;
	cursor: pointer;
}
input[type="checkbox"]:checked:after {
	width: 12px;
	height: 12px;
	border-radius: 3px;
	background: #4b4b4b;
	margin: 0 !important;
	text-shadow: none;
	box-shadow: 0px 0px 4px #00000029 !important;
	border: none !important;
	outline: none !important; 
	position: relative;
	margin-top: 4px !important;
	margin-left: 2px !important; 
	cursor: pointer;
}
.form-check label{
	margin-top: 4px;
	margin-left: 8px;
	cursor: pointer;
	color: #6F6F6F;
	font-size: 15px;
}
.email-template-container .dropdown.bootstrap-select{
	width: 240px !important;
}
#test-proctoring .span-slider{
	margin-left: 12px;
}

.allow-tab-switch-count-box{
	position: relative;
    bottom: 1px;
}
.tabSwitchAlertLimit-icon{
	width: 15px;
    height: 15px;
	margin-left: 15px;
	opacity: .5;
	position: relative;
	bottom: 1px;
    right: 9px;
}
.tabSwitchAlertLimit-icon:hover{
	margin-left: 15px;
}
.tabSwitchAlertLimit-icon.active{
	opacity: 1 !important;
}
.tabSwitchAlertLimit-count{
	position: relative;
	top: .8px;
	left: 2px;
	cursor: default !important;
}

.tabSwitchAlertLimit-input-box{
	display: inline-block;
	width: auto;
	height: 25px;
	margin-left: 3px;
	position: relative;
	top: 7px;
	border: 1px solid #D1D1D1;
	border-radius: 2.5px;
	background-color: #fff;
	box-shadow: inset 0px 0px 2px 0px #00000029 !important;
	padding-left: 4px;
	margin-top: -9px;
}
.tabSwitchAlertLimit-input{
	width: 20px;
	height: 23px;
	float: left;
	margin: 0;
	border: 0 !important;
	padding-left: 0 !important;
	padding-right: 0px !important;
	outline: unset !important;
	box-shadow: unset !important;
}
.tabSwitchAlertLimit-input-box input[type=number]::-webkit-inner-spin-button, 
.tabSwitchAlertLimit-input-box input[type=number]::-webkit-outer-spin-button { 
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                margin: 0; 
            }

.tabSwitchAlertLimit-btn{
	border: none !important;
	height: 20px;
	width: 20px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: var(--primary-color);
	float: right;
	margin-top: 1px;
}
.tabSwitchAlertLimit-btn img{
	width: 10px;
	height: 10px;
}
.allow-tab-switch-count-box{
	display: inline-block;
}

.ql-editor, .ql-toolbar.ql-snow{
	font-family: 'Avenir',sans-serif !important;
}
.custom-input-options img{
	width: 15px;
}
.custom-input-options  .delete-custom-input{
	opacity: 0.3;
	margin-left: 0px;
}
.editFieldOptions img{
	opacity: 0.5;
}
.editFieldOptions img:nth-child(2){
	margin-left: 10px;
}
#fieldTable.table td{
	word-break: break-all !important;
    max-width: 100px !important;
	cursor: default;
}

#fieldTable.table td[contenteditable = "true"]{
	cursor: text;
}

#fieldTable.table tr.ui-sortable-helper{
	display: table;
	border: 1px solid #0002;
	background-color: #fffc;
}
#fieldTable.table tr.ui-sortable-helper td{
	border-color: transparent;
}
input:focus {
	border-color: var(--primary-border);
}
input.form-control:focus{
	border-color: var(--primary-border);
	outline: 1px solid var(--primary-border);
	box-shadow: unset;
	/* box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); */
}

#quizTime{
	outline: none !important;
    box-shadow: none !important;
    border: none !important;
    border-bottom: 1px solid #e0e0e0 !important;
}

table {
    border-collapse: unset !important;
}

.table thead th{
	border-bottom: 1px solid #e0e0e0 !important;;
}

::placeholder{
	color: #b6b6b6 !important;
}
:-ms-input-placeholder{
	color: #b6b6b6 !important;
}
::-moz-placeholder{
	color: #b6b6b6 !important;
}

.send-invite-container input, .send-invite-container textarea, .send-invite-container li{
	color: #000000 !important;
}